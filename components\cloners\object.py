"""
Object Cloner - Исправленная версия с точной копией логики из бэкапа
"""

import bpy
import bmesh
from mathutils import Vector
from ..base_cloner import BaseCloner


def calculate_edge_normals(mesh_obj):
    """
    Вычисляет стабильные нормали рёбер для правильного Offset - ТОЧНАЯ КОПИЯ ИЗ BACKUP.
    
    Использует несколько подходов для максимальной стабильности:
    1. Приоритет плоских поверхностей (для архитектуры)
    2. Средняя точка между нормалями граней (для органики)  
    3. Стабилизация направления через мировые координаты
    
    Args:
        mesh_obj: Объект меша для обработки
    """
    import bmesh
    from mathutils import Vector
    
    # Создаем bmesh из меша
    bm = bmesh.new()
    bm.from_mesh(mesh_obj.data)
    
    # Убеждаемся, что нормали граней актуальны
    bm.faces.ensure_lookup_table()
    bm.normal_update()
    bm.faces.ensure_lookup_table()
    
    # Создаем слой атрибутов для нормалей рёбер
    if "edge_normal" not in bm.edges.layers.float_vector:
        edge_normal_layer = bm.edges.layers.float_vector.new("edge_normal")
    else:
        edge_normal_layer = bm.edges.layers.float_vector["edge_normal"]
    
    # Глобальные направления для стабилизации
    WORLD_UP = Vector((0, 0, 1))
    WORLD_FORWARD = Vector((0, 1, 0))
    WORLD_RIGHT = Vector((1, 0, 0))
    
    for edge in bm.edges:
        if len(edge.link_faces) >= 2:
            # СЛУЧАЙ 1: Внутреннее ребро (2+ грани) - наиболее частый случай
            face_normals = [face.normal for face in edge.link_faces]
            
            # Проверяем угол между гранями для определения стратегии
            angle = face_normals[0].angle(face_normals[1])
            
            if angle < 0.1:  # Практически плоская поверхность (< ~6 градусов)
                # Для плоских поверхностей - используем усреднённую нормаль
                avg_normal = sum(face_normals, Vector()).normalized()
                edge[edge_normal_layer] = avg_normal
                
            else:  # Угловое ребро
                # Вычисляем биссектрису между нормалями граней
                # Это даёт стабильное направление "наружу" от ребра
                n1, n2 = face_normals[0].normalized(), face_normals[1].normalized()
                bisector = (n1 + n2).normalized()
                
                # Используем биссектрису как стабильную нормаль ребра
                edge[edge_normal_layer] = bisector
                
        elif len(edge.link_faces) == 1:
            # СЛУЧАЙ 2: Граничное ребро - используем нормаль единственной грани
            face_normal = edge.link_faces[0].normal.normalized()
            edge[edge_normal_layer] = face_normal
            
        else:
            # СЛУЧАЙ 3: Изолированное ребро - используем стабильное мировое направление
            edge_vector = (edge.verts[1].co - edge.verts[0].co).normalized()
            
            # Выбираем наиболее перпендикулярное мировое направление
            dot_up = abs(edge_vector.dot(WORLD_UP))
            dot_forward = abs(edge_vector.dot(WORLD_FORWARD))
            dot_right = abs(edge_vector.dot(WORLD_RIGHT))
            
            if dot_up < dot_forward and dot_up < dot_right:
                fallback_normal = WORLD_UP
            elif dot_forward < dot_right:
                fallback_normal = WORLD_FORWARD
            else:
                fallback_normal = WORLD_RIGHT
                
            edge[edge_normal_layer] = fallback_normal
    
    # Применяем изменения к мешу
    bm.to_mesh(mesh_obj.data)
    bm.free()


class ObjectCloner(BaseCloner):
    """
    Object Cloner - точная копия логики из BACKUP
    """
    
    bl_idname = "OBJECT"
    bl_label = "Object Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """Регистрация всех свойств - точная копия из бэкапа"""
        # Source Settings
        props_owner.object_source_mode = bpy.props.EnumProperty(
            name="Source Mode",
            items=[
                ('0', "Object", "Use single object as source"),
                ('1', "Collection", "Use collection as source")
            ],
            default='0'
        )
        
        # Distribution Settings  
        props_owner.object_distribution_mode = bpy.props.EnumProperty(
            name="Distribution Mode",
            items=[
                ('0', "Vertices", "Distribute on vertices"),
                ('1', "Edges", "Distribute on edges"), 
                ('2', "Faces", "Distribute on faces")
            ],
            default='2'
        )
        
        # Instance Count
        props_owner.object_instance_count = bpy.props.IntProperty(
            name="Instance Count",
            default=50,
            min=1,
            max=10000
        )
        
        # Density
        props_owner.object_density = bpy.props.FloatProperty(
            name="Density",
            default=1.0,
            min=0.001
        )
        
        # Face Center Mode
        props_owner.object_face_center_mode = bpy.props.BoolProperty(
            name="Face Center Mode",
            default=False
        )
        
        # Offset
        props_owner.object_offset = bpy.props.FloatProperty(
            name="Offset",
            default=0.0
        )
        
        # Align to Normal
        props_owner.object_align_to_normal = bpy.props.BoolProperty(
            name="Align to Normal",
            default=False
        )
        
        # Uniform Scale
        props_owner.object_uniform_scale = bpy.props.BoolProperty(
            name="Uniform Scale",
            default=False
        )
        
        # Collection Pick Instance
        props_owner.object_pick_instance = bpy.props.BoolProperty(
            name="Collection Pick Instance",
            default=True
        )
        
        # Collection settings
        props_owner.object_instance_index = bpy.props.IntProperty(
            name="Collection Instance Index",
            default=0,
            min=0
        )
        
        props_owner.object_collection_seed = bpy.props.IntProperty(
            name="Collection Random Seed",
            default=2,
            min=0
        )
        
        # INSTANCE TRANSFORM PARAMETERS - ДОБАВЛЯЕМ НЕДОСТАЮЩИЕ!
        # Instance Scale
        props_owner.object_instance_scale = bpy.props.FloatVectorProperty(
            name="Instance Scale",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        
        # Instance Rotation
        props_owner.object_instance_rotation = bpy.props.FloatVectorProperty(
            name="Instance Rotation",
            default=(0.0, 0.0, 0.0),
            size=3,
            subtype='EULER'
        )
        
        # RANDOM PARAMETERS - ДОБАВЛЯЕМ НЕДОСТАЮЩИЕ!
        # Random Position
        props_owner.object_random_position = bpy.props.FloatVectorProperty(
            name="Random Position",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        
        # Random Rotation
        props_owner.object_random_rotation = bpy.props.FloatVectorProperty(
            name="Random Rotation",
            default=(0.0, 0.0, 0.0),
            size=3,
            subtype='EULER'
        )
        
        # Random Scale
        props_owner.object_random_scale = bpy.props.FloatProperty(
            name="Random Scale",
            default=0.0,
            min=0.0
        )
        
        # Random Seed
        props_owner.object_random_seed = bpy.props.IntProperty(
            name="Random Seed",
            default=0,
            min=0
        )
        
        # Hide Original
        props_owner.object_hide_original = bpy.props.BoolProperty(
            name="Hide Original",
            default=False
        )
    
    def get_specific_sockets(self):
        """Сокеты - точная копия из бэкапа"""
        return [
            # Source settings
            ("Source Mode", "NodeSocketInt", "INPUT", 0),
            ("Source Object", "NodeSocketObject", "INPUT", None),
            ("Source Collection", "NodeSocketCollection", "INPUT", None),
            
            # Distribution settings
            ("Distribution Mode", "NodeSocketInt", "INPUT", 2),
            ("Instance Count", "NodeSocketInt", "INPUT", 50),
            ("Density", "NodeSocketFloat", "INPUT", 1.0),
            ("Face Center Mode", "NodeSocketBool", "INPUT", False),
            
            # Transform settings  
            ("Offset", "NodeSocketFloat", "INPUT", 0.0),
            ("Align to Normal", "NodeSocketBool", "INPUT", False),
            ("Instance Scale", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Instance Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Uniform Scale", "NodeSocketBool", "INPUT", False),
            
            # Random parameters
            ("Random Position", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Random Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Random Scale", "NodeSocketFloat", "INPUT", 0.0),
            ("Random Seed", "NodeSocketInt", "INPUT", 0),
            
            # Collection settings
            ("Collection Pick Instance", "NodeSocketBool", "INPUT", True),
            ("Collection Instance Index", "NodeSocketInt", "INPUT", 0),
            ("Collection Object Count", "NodeSocketInt", "INPUT", 3),
            ("Collection Random Seed", "NodeSocketInt", "INPUT", 2),
            
            # Visibility
            ("Hide Original", "NodeSocketBool", "INPUT", False),
        ]
    
    def create_node_group(self, mode="STACKED", target_obj=None):
        """Создание node group - точная копия логики из бэкапа"""
        try:
            # ВАЖНО: Предварительно вычисляем правильные нормали рёбер для target объекта - ТОЧНАЯ КОПИЯ ИЗ BACKUP
            if target_obj and target_obj.type == 'MESH':
                calculate_edge_normals(target_obj)
            
            # Уникальное имя как в оригинале
            import time
            base_name = "ObjectCloner3D_Advanced"
            unique_suffix = str(int(time.time() * 1000000) % 1000000)
            group_name = f"{base_name}_{unique_suffix}"
            
            counter = 1
            original_name = group_name
            while group_name in bpy.data.node_groups:
                group_name = f"{original_name}_{counter}"
                counter += 1
            
            node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')
            
            # Создаем интерфейс
            self._create_node_group_interface(node_group)
            
            # Строим дерево нод - точная копия из бэкапа
            self._build_node_tree(node_group)
            
            # Socket mapping
            socket_mapping = self._create_socket_mapping(node_group)
            
            return node_group, socket_mapping
            
        except Exception as e:
            print(f"[ERROR] ObjectCloner node group creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def _create_node_group_interface(self, node_group):
        """Создание интерфейса - точная копия из бэкапа"""
        interface = node_group.interface
        interface.clear()
        
        # Input Geometry
        interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
        
        # Source Mode
        source_mode_socket = interface.new_socket(name="Source Mode", in_out='INPUT', socket_type='NodeSocketInt')
        source_mode_socket.default_value = 0
        source_mode_socket.min_value = 0
        source_mode_socket.max_value = 1
        
        # Source Object
        interface.new_socket(name="Source Object", in_out='INPUT', socket_type='NodeSocketObject')
        
        # Source Collection
        interface.new_socket(name="Source Collection", in_out='INPUT', socket_type='NodeSocketCollection')
        
        # Distribution Mode
        dist_mode_socket = interface.new_socket(name="Distribution Mode", in_out='INPUT', socket_type='NodeSocketInt')
        dist_mode_socket.default_value = 2
        dist_mode_socket.min_value = 0
        dist_mode_socket.max_value = 2
        
        # Instance Count
        count_socket = interface.new_socket(name="Instance Count", in_out='INPUT', socket_type='NodeSocketInt')
        count_socket.default_value = 50
        count_socket.min_value = 1
        count_socket.max_value = 10000
        
        # Density
        density_socket = interface.new_socket(name="Density", in_out='INPUT', socket_type='NodeSocketFloat')
        density_socket.default_value = 1.0
        density_socket.min_value = 0.001
        
        # Face Center Mode
        face_center_socket = interface.new_socket(name="Face Center Mode", in_out='INPUT', socket_type='NodeSocketBool')
        face_center_socket.default_value = False
        
        # Offset
        offset_socket = interface.new_socket(name="Offset", in_out='INPUT', socket_type='NodeSocketFloat')
        offset_socket.default_value = 0.0
        
        # Align to Normal
        align_socket = interface.new_socket(name="Align to Normal", in_out='INPUT', socket_type='NodeSocketBool')
        align_socket.default_value = False
        
        # Instance Scale
        scale_socket = interface.new_socket(name="Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
        scale_socket.default_value = (1.0, 1.0, 1.0)
        
        # Instance Rotation
        rotation_socket = interface.new_socket(name="Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
        rotation_socket.default_value = (0.0, 0.0, 0.0)
        
        # Uniform Scale
        uniform_scale_socket = interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')
        uniform_scale_socket.default_value = False
        
        # Random Position
        rand_pos_socket = interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
        rand_pos_socket.default_value = (0.0, 0.0, 0.0)
        
        # Random Rotation
        rand_rot_socket = interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
        rand_rot_socket.default_value = (0.0, 0.0, 0.0)
        
        # Random Scale
        rand_scale_socket = interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
        rand_scale_socket.default_value = 0.0
        
        # Random Seed
        seed_socket = interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
        seed_socket.default_value = 0
        
        # Collection Pick Instance
        pick_socket = interface.new_socket(name="Collection Pick Instance", in_out='INPUT', socket_type='NodeSocketBool')
        pick_socket.default_value = True
        
        # Collection Instance Index
        index_socket = interface.new_socket(name="Collection Instance Index", in_out='INPUT', socket_type='NodeSocketInt')
        index_socket.default_value = 0
        index_socket.min_value = 0
        index_socket.max_value = 50
        
        # Collection Object Count
        object_count_socket = interface.new_socket(name="Collection Object Count", in_out='INPUT', socket_type='NodeSocketInt')
        object_count_socket.default_value = 3
        object_count_socket.min_value = 1
        object_count_socket.max_value = 50
        
        # Collection Random Seed
        coll_seed_socket = interface.new_socket(name="Collection Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
        coll_seed_socket.default_value = 2
        
        # Hide Original
        hide_socket = interface.new_socket(name="Hide Original", in_out='INPUT', socket_type='NodeSocketBool')
        hide_socket.default_value = False
        
        # Realize Instances (Anti-recursion) - берем значение из scene settings
        realize_socket = interface.new_socket(name="Realize Instances", in_out='INPUT', socket_type='NodeSocketBool')
        # Получаем настройку анти-рекурсии из scene
        import bpy
        scene_anti_recursion = getattr(bpy.context.scene, 'use_anti_recursion', True)
        realize_socket.default_value = scene_anti_recursion
        
        # Output Geometry
        interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
    
    def _build_node_tree(self, node_group):
        """Строим дерево нод - точная копия из BACKUP"""
        nodes = node_group.nodes
        links = node_group.links
        
        # Основные узлы
        group_input = nodes.new('NodeGroupInput')
        group_output = nodes.new('NodeGroupOutput')
        group_input.location = (-800, 0)
        group_output.location = (800, 0)
        
        # Object Info для получения геометрии Source Object - ТОЧНАЯ КОПИЯ
        source_object_info = nodes.new('GeometryNodeObjectInfo')
        source_object_info.location = (-600, 200)
        source_object_info.name = "Source Object Info"
        source_object_info.transform_space = 'ORIGINAL'  # КРИТИЧЕСКИ ВАЖНО!
        
        # Collection Info для рандомизации (separate objects) - ТОЧНАЯ КОПИЯ
        source_collection_info_random = nodes.new('GeometryNodeCollectionInfo')
        source_collection_info_random.location = (-600, 0)
        source_collection_info_random.name = "Source Collection Info Random"
        source_collection_info_random.transform_space = 'RELATIVE'
        
        # Collection Info для всей коллекции (combined) - ТОЧНАЯ КОПИЯ
        source_collection_info_combined = nodes.new('GeometryNodeCollectionInfo')
        source_collection_info_combined.location = (-600, -100)
        source_collection_info_combined.name = "Source Collection Info Combined"
        source_collection_info_combined.transform_space = 'RELATIVE'
        
        # Настройка рандомного Collection Info - ТОЧНАЯ КОПИЯ
        try:
            if hasattr(source_collection_info_random, 'separate_children'):
                source_collection_info_random.separate_children = True
            elif 'Separate Children' in source_collection_info_random.inputs:
                source_collection_info_random.inputs['Separate Children'].default_value = True
        except:
            pass
        
        # ВАЖНО: reset_children для правильного transform space
        try:
            if hasattr(source_collection_info_random, 'reset_children'):
                source_collection_info_random.reset_children = True
            elif 'Reset Children' in source_collection_info_random.inputs:
                source_collection_info_random.inputs['Reset Children'].default_value = True
        except:
            pass
        
        # Настройка combined Collection Info - ТОЧНАЯ КОПИЯ  
        try:
            if hasattr(source_collection_info_combined, 'separate_children'):
                source_collection_info_combined.separate_children = False
            elif 'Separate Children' in source_collection_info_combined.inputs:
                source_collection_info_combined.inputs['Separate Children'].default_value = False
        except:
            pass
        
        # ВАЖНО: reset_children для правильного transform space
        try:
            if hasattr(source_collection_info_combined, 'reset_children'):
                source_collection_info_combined.reset_children = True
            elif 'Reset Children' in source_collection_info_combined.inputs:
                source_collection_info_combined.inputs['Reset Children'].default_value = True
        except:
            pass
        
        # Подключаем входы - ТОЧНАЯ КОПИЯ
        links.new(group_input.outputs['Source Object'], source_object_info.inputs['Object'])
        links.new(group_input.outputs['Source Collection'], source_collection_info_random.inputs['Collection'])
        links.new(group_input.outputs['Source Collection'], source_collection_info_combined.inputs['Collection'])
        
        # Подключаем Collection Random Seed только к рандомному Collection Info - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        if 'Seed' in source_collection_info_random.inputs:
            links.new(group_input.outputs['Collection Random Seed'], source_collection_info_random.inputs['Seed'])
        elif 'Random Seed' in source_collection_info_random.inputs:
            links.new(group_input.outputs['Collection Random Seed'], source_collection_info_random.inputs['Random Seed'])
        
        # Collection Mode Switch - ТОЧНАЯ КОПИЯ
        collection_mode_switch = nodes.new('GeometryNodeSwitch')
        collection_mode_switch.input_type = 'GEOMETRY'
        collection_mode_switch.location = (-400, -50)
        collection_mode_switch.name = "Collection Mode Switch"
        
        # Подключаем Collection Pick Instance для переключения - ТОЧНАЯ КОПИЯ
        links.new(group_input.outputs['Collection Pick Instance'], collection_mode_switch.inputs['Switch'])
        # False = combined (вся коллекция), True = random (отдельные объекты)
        links.new(source_collection_info_combined.outputs['Instances'], collection_mode_switch.inputs['False'])
        links.new(source_collection_info_random.outputs['Instances'], collection_mode_switch.inputs['True'])
        
        # Switch для выбора между Object и Collection режимами - ТОЧНАЯ КОПИЯ
        source_mode_switch = nodes.new('GeometryNodeSwitch')
        source_mode_switch.input_type = 'GEOMETRY'
        source_mode_switch.location = (-400, 100)
        source_mode_switch.name = "Source Mode Switch"
        
        # Создаем Compare node для проверки Source Mode - ТОЧНАЯ КОПИЯ
        is_collection_mode = nodes.new('FunctionNodeCompare')
        is_collection_mode.location = (-500, 300)
        is_collection_mode.name = "Is Collection Mode"
        is_collection_mode.data_type = 'INT'
        is_collection_mode.operation = 'EQUAL'
        is_collection_mode.inputs['B'].default_value = 1  # Collection = 1
        
        # Подключаем логику переключения - ТОЧНАЯ КОПИЯ
        links.new(group_input.outputs['Source Mode'], is_collection_mode.inputs['A'])
        links.new(is_collection_mode.outputs['Result'], source_mode_switch.inputs['Switch'])
        
        # False = Object mode, True = Collection mode - ТОЧНАЯ КОПИЯ
        links.new(source_object_info.outputs['Geometry'], source_mode_switch.inputs['False'])
        links.new(collection_mode_switch.outputs[0], source_mode_switch.inputs['True'])
        
        # ЛОГИКА РАСПРЕДЕЛЕНИЯ - вызываем точную копию из бэкапа
        distribution_switch = self._create_distribution_logic(nodes, links, group_input.outputs['Geometry'], group_input)
        
        # СОЗДАНИЕ ЭКЗЕМПЛЯРОВ - ТОЧНАЯ КОПИЯ
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        instance_on_points.location = (200, 0)
        instance_on_points.name = "Instance on Points"
        
        # Подключаем точки и источник - ТОЧНАЯ КОПИЯ
        links.new(distribution_switch.outputs[0], instance_on_points.inputs['Points'])
        links.new(source_mode_switch.outputs[0], instance_on_points.inputs['Instance'])
        
        # РАНДОМИЗАЦИЯ КОЛЛЕКЦИЙ через Instance on Points - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        # ВАЖНО: Pick Instance и Instance Index нужны ТОЛЬКО для коллекций!
        # Для Object mode они мешают нормальной работе
        
        if 'Pick Instance' in instance_on_points.inputs:
            # Создаем Switch для Pick Instance - включаем только в Collection режиме
            pick_instance_switch = nodes.new('GeometryNodeSwitch')
            pick_instance_switch.input_type = 'BOOLEAN'
            pick_instance_switch.location = (-200, -100)
            pick_instance_switch.name = "Pick Instance Switch"
            
            # False = Object mode (Pick Instance = False), True = Collection mode (user value)
            links.new(is_collection_mode.outputs['Result'], pick_instance_switch.inputs['Switch'])
            pick_instance_switch.inputs['False'].default_value = False  # Object mode: не использовать Pick Instance
            links.new(group_input.outputs['Collection Pick Instance'], pick_instance_switch.inputs['True'])
            
            # Подключаем результат к Instance on Points
            links.new(pick_instance_switch.outputs[0], instance_on_points.inputs['Pick Instance'])
        
        if 'Instance Index' in instance_on_points.inputs:
            # Создаем Switch для Instance Index - используем только в Collection режиме
            instance_index_switch = nodes.new('GeometryNodeSwitch')
            instance_index_switch.input_type = 'INT'
            instance_index_switch.location = (-200, -150)
            instance_index_switch.name = "Instance Index Switch"
            
            # Для Collection режима создаем СЛУЧАЙНЫЙ Instance Index для каждой точки
            # Это обеспечит настоящую рандомизацию коллекции
            random_instance_index = nodes.new('FunctionNodeRandomValue')
            random_instance_index.location = (-350, -200)
            random_instance_index.name = "Random Instance Index"
            random_instance_index.data_type = 'INT'
            
            # Устанавливаем диапазон от 0 до количества объектов в коллекции
            random_instance_index.inputs['Min'].default_value = 0
            # Подключаем параметр Collection Object Count для динамического максимума
            links.new(group_input.outputs['Collection Object Count'], random_instance_index.inputs['Max'])
            
            # Подключаем Collection Random Seed к генератору
            links.new(group_input.outputs['Collection Random Seed'], random_instance_index.inputs['Seed'])
            
            # Switch логика: False = Object mode (Index = 0), True = Collection mode (random per point)
            links.new(is_collection_mode.outputs['Result'], instance_index_switch.inputs['Switch'])
            instance_index_switch.inputs['False'].default_value = 0  # Object mode: всегда 0
            
            # В Collection режиме используем случайный индекс для каждой точки
            links.new(random_instance_index.outputs['Value'], instance_index_switch.inputs['True'])
            
            # Подключаем результат к Instance on Points
            links.new(instance_index_switch.outputs[0], instance_on_points.inputs['Instance Index'])
        
        # Применяем offset transform - ТОЧНАЯ КОПИЯ
        offset_result = self._apply_offset_transform(nodes, links, instance_on_points, group_input, distribution_switch)
        
        # Применяем normal alignment - ДОБАВЛЕНО ИЗ BACKUP
        aligned_result = self._apply_normal_alignment(nodes, links, offset_result, group_input)
        
        # Применяем instance transforms - ТОЧНАЯ КОПИЯ  
        transform_result = self._apply_instance_transforms(nodes, links, aligned_result, group_input)
        
        # Hide Original логика - ТОЧНАЯ КОПИЯ
        final_result = self._apply_hide_original(nodes, links, transform_result, group_input)
        
        # Применяем анти-рекурсию - ВСЕГДА (Switch управляется сокетом)
        base_nodes = {
            'nodes': nodes,
            'links': links,
            'group_input': group_input,
            'group_output': group_output
        }
        anti_recursion_result = self.apply_anti_recursion(base_nodes, final_result, use_anti_recursion=True)
        links.new(anti_recursion_result, group_output.inputs['Geometry'])
    
    def _create_distribution_logic(self, nodes, links, surface_source, group_input):
        """ТОЧНАЯ КОПИЯ функции _create_distribution_logic из бэкапа"""
        
        # VERTICES - конвертация в точки на вершинах
        mesh_to_points_vertices = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points_vertices.location = (-400, 200)
        mesh_to_points_vertices.mode = 'VERTICES'
        mesh_to_points_vertices.name = "Vertices Distribution"
        
        # EDGES - конвертация в точки на рёбрах
        mesh_to_curve = nodes.new('GeometryNodeMeshToCurve')
        mesh_to_curve.location = (-500, 0)
        mesh_to_curve.name = "Mesh to Curve"
        
        curve_to_points = nodes.new('GeometryNodeCurveToPoints')
        curve_to_points.location = (-400, 0)
        curve_to_points.mode = 'COUNT'
        curve_to_points.name = "Edges Distribution"
        
        # FACES - два варианта: случайное распределение или центры граней
        distribute_on_faces = nodes.new('GeometryNodeDistributePointsOnFaces')
        distribute_on_faces.location = (-400, -200)
        distribute_on_faces.distribute_method = 'RANDOM'
        distribute_on_faces.name = "Faces Distribution Random"
        
        # Альтернатива: точки в центрах граней
        mesh_to_points_faces = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points_faces.location = (-400, -300)
        mesh_to_points_faces.mode = 'FACES'
        mesh_to_points_faces.name = "Face Centers"
        
        # Store Named Attribute для сохранения нормалей граней (random)
        store_normal_faces_random = nodes.new('GeometryNodeStoreNamedAttribute')
        store_normal_faces_random.location = (-250, -200)
        store_normal_faces_random.name = "Store Face Normals Random"
        store_normal_faces_random.data_type = 'FLOAT_VECTOR'
        store_normal_faces_random.domain = 'POINT'
        store_normal_faces_random.inputs['Name'].default_value = "surface_normal"
        
        # Store Named Attribute для сохранения нормалей граней (centers)
        store_normal_faces_centers = nodes.new('GeometryNodeStoreNamedAttribute')
        store_normal_faces_centers.location = (-250, -300)
        store_normal_faces_centers.name = "Store Face Normals Centers"
        store_normal_faces_centers.data_type = 'FLOAT_VECTOR'
        store_normal_faces_centers.domain = 'POINT'
        store_normal_faces_centers.inputs['Name'].default_value = "surface_normal"
        
        # Switch между случайным распределением и центрами граней
        face_mode_switch = nodes.new('GeometryNodeSwitch')
        face_mode_switch.input_type = 'GEOMETRY'
        face_mode_switch.location = (-100, -250)
        face_mode_switch.name = "Face Mode Switch"
        
        # Первый switch: Vertices vs (Edges/Faces)
        vertices_switch = nodes.new('GeometryNodeSwitch')
        vertices_switch.input_type = 'GEOMETRY'
        vertices_switch.location = (-200, 100)
        vertices_switch.name = "Vertices Switch"
        
        # Второй switch: Edges vs Faces
        edges_faces_switch = nodes.new('GeometryNodeSwitch')
        edges_faces_switch.input_type = 'GEOMETRY'
        edges_faces_switch.location = (-200, -50)
        edges_faces_switch.name = "Edges vs Faces Switch"
        
        # Compare nodes для переключения
        is_vertices = nodes.new('FunctionNodeCompare')
        is_vertices.location = (-300, 150)
        is_vertices.name = "Is Vertices Mode"
        is_vertices.data_type = 'INT'
        is_vertices.operation = 'EQUAL'
        is_vertices.inputs['B'].default_value = 0  # VERTICES = 0
        
        is_edges = nodes.new('FunctionNodeCompare')
        is_edges.location = (-300, 50)
        is_edges.name = "Is Edges Mode"
        is_edges.data_type = 'INT'
        is_edges.operation = 'EQUAL'
        is_edges.inputs['B'].default_value = 1  # EDGES = 1
        
        # Normal input для захвата нормалей
        normal_input = nodes.new('GeometryNodeInputNormal')
        normal_input.location = (-450, 100)
        normal_input.name = "Surface Normal Input"
        
        # Store normals для vertices
        store_normal_on_mesh = nodes.new('GeometryNodeStoreNamedAttribute')
        store_normal_on_mesh.location = (-500, 150)
        store_normal_on_mesh.name = "Store Normals on Mesh"
        store_normal_on_mesh.data_type = 'FLOAT_VECTOR'
        store_normal_on_mesh.domain = 'POINT'
        store_normal_on_mesh.inputs['Name'].default_value = "surface_normal"
        
        # Vertices with normals
        mesh_to_points_vertices_with_normals = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points_vertices_with_normals.location = (-350, 150)
        mesh_to_points_vertices_with_normals.mode = 'VERTICES'
        mesh_to_points_vertices_with_normals.name = "Vertices with Normals"
        
        # Store edge normals
        store_normal_edges = nodes.new('GeometryNodeStoreNamedAttribute')
        store_normal_edges.location = (-200, -50)
        store_normal_edges.name = "Store Edge Normals"
        store_normal_edges.data_type = 'FLOAT_VECTOR'
        store_normal_edges.domain = 'POINT'
        store_normal_edges.inputs['Name'].default_value = "surface_normal"
        
        # Подключения входных данных
        links.new(surface_source, mesh_to_points_vertices.inputs['Mesh'])
        links.new(surface_source, mesh_to_curve.inputs['Mesh'])
        links.new(surface_source, distribute_on_faces.inputs['Mesh'])
        links.new(surface_source, mesh_to_points_faces.inputs['Mesh'])
        
        # Подключения нормалей для vertices
        links.new(surface_source, store_normal_on_mesh.inputs['Geometry'])
        links.new(normal_input.outputs['Normal'], store_normal_on_mesh.inputs['Value'])
        links.new(store_normal_on_mesh.outputs['Geometry'], mesh_to_points_vertices_with_normals.inputs['Mesh'])
        
        # Подключения для edges
        links.new(mesh_to_curve.outputs['Curve'], curve_to_points.inputs['Curve'])
        
        # Безопасное подключение Count
        if 'Count' in curve_to_points.inputs:
            links.new(group_input.outputs['Instance Count'], curve_to_points.inputs['Count'])
        else:
            curve_to_points.inputs[1].default_value = 50
            
        # Подключения для faces
        # Безопасное подключение Density
        density_connected = False
        for density_name in ['Density', 'Density Max', 'Distance Min']:
            if density_name in distribute_on_faces.inputs:
                links.new(group_input.outputs['Density'], distribute_on_faces.inputs[density_name])
                density_connected = True
                break
        
        # Подключения нормалей для faces - БЕЗОПАСНЫЕ ПОДКЛЮЧЕНИЯ
        links.new(distribute_on_faces.outputs['Points'], store_normal_faces_random.inputs['Geometry'])
        # Безопасное подключение Normal для distribute_on_faces
        if 'Normal' in distribute_on_faces.outputs:
            links.new(distribute_on_faces.outputs['Normal'], store_normal_faces_random.inputs['Value'])
        else:
            # Fallback: используем input normal
            links.new(normal_input.outputs['Normal'], store_normal_faces_random.inputs['Value'])
        
        links.new(mesh_to_points_faces.outputs['Points'], store_normal_faces_centers.inputs['Geometry'])
        # Безопасное подключение Normal для mesh_to_points_faces
        if 'Normal' in mesh_to_points_faces.outputs:
            links.new(mesh_to_points_faces.outputs['Normal'], store_normal_faces_centers.inputs['Value'])
        else:
            # Fallback: используем input normal
            links.new(normal_input.outputs['Normal'], store_normal_faces_centers.inputs['Value'])
        
        # РЁБРА: Используем предварительно вычисленные правильные нормали рёбер из Python - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        # Нормали уже вычислены через calculate_edge_normals() и сохранены как "edge_normal" attribute
        
        # Читаем предвычисленные нормали рёбер из named attribute
        edge_normal_attr = nodes.new('GeometryNodeInputNamedAttribute')
        edge_normal_attr.location = (-300, -80)
        edge_normal_attr.name = "Read Edge Normals"
        edge_normal_attr.inputs['Name'].default_value = "edge_normal"
        edge_normal_attr.data_type = 'FLOAT_VECTOR'
        
        # Сохраняем предвычисленные нормали рёбер как "surface_normal" для единообразия с другими режимами
        links.new(curve_to_points.outputs['Points'], store_normal_edges.inputs['Geometry'])
        links.new(edge_normal_attr.outputs['Attribute'], store_normal_edges.inputs['Value'])
        
        # Подключения compare nodes
        links.new(group_input.outputs['Distribution Mode'], is_vertices.inputs['A'])
        links.new(group_input.outputs['Distribution Mode'], is_edges.inputs['A'])
        
        # Подключения switch nodes
        links.new(is_vertices.outputs['Result'], vertices_switch.inputs['Switch'])
        links.new(is_edges.outputs['Result'], edges_faces_switch.inputs['Switch'])
        
        # Face mode switch
        links.new(group_input.outputs['Face Center Mode'], face_mode_switch.inputs['Switch'])
        links.new(store_normal_faces_random.outputs['Geometry'], face_mode_switch.inputs['False'])
        links.new(store_normal_faces_centers.outputs['Geometry'], face_mode_switch.inputs['True'])
        
        # Финальные подключения геометрии - ТОЧНАЯ КОПИЯ ИЗ BACKUP!
        links.new(store_normal_edges.outputs['Geometry'], edges_faces_switch.inputs[2])      # EDGES mode с нормалями
        links.new(face_mode_switch.outputs[0], edges_faces_switch.inputs[1])                # FACES mode с выбранным режимом
        
        links.new(mesh_to_points_vertices_with_normals.outputs['Points'], vertices_switch.inputs[2])  # VERTICES mode с нормалями
        links.new(edges_faces_switch.outputs[0], vertices_switch.inputs[1])                          # EDGES/FACES modes
        
        return vertices_switch
    
    def _apply_offset_transform(self, nodes, links, instance_node, group_input, distribution_switch):
        """ТОЧНАЯ КОПИЯ функции offset из бэкапа"""
        
        # Set Position для offset
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.location = (250, 0)
        set_position.name = "Offset Transform"
        
        # Position node
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.location = (100, 50)
        position_node.name = "Current Position"
        
        # Получаем сохраненные нормали
        captured_normal = nodes.new('GeometryNodeInputNamedAttribute')
        captured_normal.location = (100, -50)
        captured_normal.name = "Get Captured Normal"
        captured_normal.inputs['Name'].default_value = "surface_normal"
        captured_normal.data_type = 'FLOAT_VECTOR'
        
        # Vector Math для масштабирования нормали на offset
        scale_normal = nodes.new('ShaderNodeVectorMath')
        scale_normal.location = (150, 0)
        scale_normal.operation = 'MULTIPLY'
        scale_normal.name = "Scale Normal by Offset"
        
        # Vector Math для добавления offset к позиции
        add_offset = nodes.new('ShaderNodeVectorMath')
        add_offset.location = (200, 25)
        add_offset.operation = 'ADD'
        add_offset.name = "Add Offset to Position"
        
        # Подключения
        links.new(instance_node.outputs['Instances'], set_position.inputs['Geometry'])
        links.new(captured_normal.outputs['Attribute'], scale_normal.inputs[0])
        links.new(group_input.outputs['Offset'], scale_normal.inputs[1])
        links.new(position_node.outputs['Position'], add_offset.inputs[0])
        links.new(scale_normal.outputs['Vector'], add_offset.inputs[1])
        links.new(add_offset.outputs['Vector'], set_position.inputs['Position'])
        
        return set_position.outputs['Geometry']
    
    def _apply_normal_alignment(self, nodes, links, geometry_input, group_input):
        """Применяет выравнивание экземпляров по нормалям поверхности - ТОЧНАЯ КОПИЯ ИЗ BACKUP"""
        
        # Получаем сохраненные нормали из именованного атрибута
        captured_normal = nodes.new('GeometryNodeInputNamedAttribute')
        captured_normal.location = (350, -100)
        captured_normal.name = "Get Normal for Alignment"
        captured_normal.inputs['Name'].default_value = "surface_normal"
        captured_normal.data_type = 'FLOAT_VECTOR'
        
        # Создаем узел Align Euler to Vector для выравнивания по нормалям
        align_euler = nodes.new('FunctionNodeAlignEulerToVector')
        align_euler.location = (400, -100)
        align_euler.name = "Align to Normal"
        align_euler.axis = 'Z'  # Выравниваем по оси Z
        align_euler.pivot_axis = 'AUTO'
        
        # Подключаем нормали к align_euler
        links.new(captured_normal.outputs['Attribute'], align_euler.inputs['Vector'])
        
        # Создаем Switch для условного выравнивания
        rotation_switch = nodes.new('GeometryNodeSwitch')
        rotation_switch.input_type = 'VECTOR'
        rotation_switch.location = (450, -50)
        rotation_switch.name = "Align Normal Switch"
        
        # Подключаем переключатель
        links.new(group_input.outputs['Align to Normal'], rotation_switch.inputs['Switch'])
        
        # False = без выравнивания (0,0,0), True = с выравниванием
        rotation_switch.inputs['False'].default_value = (0.0, 0.0, 0.0)
        links.new(align_euler.outputs['Rotation'], rotation_switch.inputs['True'])
        
        # Создаем Rotate Instances для применения выравнивания
        rotate_alignment = nodes.new('GeometryNodeRotateInstances')
        rotate_alignment.location = (500, 0)
        rotate_alignment.name = "Apply Normal Alignment"
        
        # Подключаем геометрию и rotation
        links.new(geometry_input, rotate_alignment.inputs['Instances'])
        links.new(rotation_switch.outputs[0], rotate_alignment.inputs['Rotation'])
        
        return rotate_alignment.outputs['Instances']
        
    def _apply_instance_transforms(self, nodes, links, geometry_input, group_input):
        """ТОЧНАЯ КОПИЯ instance transforms из бэкапа"""
        
        # Scale Instances - ПРАВИЛЬНЫЙ ТИП НОДЫ
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.location = (300, 0)
        scale_instances.name = "Scale Instances"
        
        # Rotate Instances - ПРАВИЛЬНЫЙ ТИП НОДЫ  
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.location = (350, 0)
        rotate_instances.name = "Rotate Instances"
        
        # Uniform Scale Switch
        uniform_scale_switch = nodes.new('GeometryNodeSwitch')
        uniform_scale_switch.input_type = 'VECTOR'
        uniform_scale_switch.location = (250, 50)
        uniform_scale_switch.name = "Uniform Scale Switch"
        
        # Separate XYZ для получения X компонента scale
        separate_scale = nodes.new('ShaderNodeSeparateXYZ')
        separate_scale.location = (200, 100)
        separate_scale.name = "Separate Scale XYZ"
        
        # Combine XYZ для uniform scale
        combine_uniform = nodes.new('ShaderNodeCombineXYZ')
        combine_uniform.location = (200, 150)
        combine_uniform.name = "Combine Uniform Scale"
        
        # Подключения для Uniform Scale
        links.new(group_input.outputs['Instance Scale'], separate_scale.inputs['Vector'])
        links.new(separate_scale.outputs['X'], combine_uniform.inputs['X'])
        links.new(separate_scale.outputs['X'], combine_uniform.inputs['Y'])
        links.new(separate_scale.outputs['X'], combine_uniform.inputs['Z'])
        
        links.new(group_input.outputs['Uniform Scale'], uniform_scale_switch.inputs['Switch'])
        links.new(group_input.outputs['Instance Scale'], uniform_scale_switch.inputs['False'])
        links.new(combine_uniform.outputs['Vector'], uniform_scale_switch.inputs['True'])
        
        # Подключения transforms - ИСПРАВЛЕНЫ ВХОДЫ
        links.new(geometry_input, scale_instances.inputs['Instances'])
        links.new(uniform_scale_switch.outputs[0], scale_instances.inputs['Scale'])  # ИСПРАВЛЕНО: outputs[0]
        
        links.new(scale_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(group_input.outputs['Instance Rotation'], rotate_instances.inputs['Rotation'])
        
        # RANDOM POSITION (случайная позиция) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        random_position = nodes.new('FunctionNodeRandomValue')
        random_position.location = (400, -150)
        random_position.name = "Random Position Value"
        random_position.data_type = 'FLOAT_VECTOR'
        
        # Подключаем Random Position range и seed
        links.new(group_input.outputs['Random Position'], random_position.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_position.inputs['Seed'])
        
        translate_instances = nodes.new('GeometryNodeTranslateInstances')
        translate_instances.location = (550, 0)
        translate_instances.name = "Random Position"
        
        links.new(rotate_instances.outputs['Instances'], translate_instances.inputs['Instances'])
        links.new(random_position.outputs[0], translate_instances.inputs['Translation'])
        
        # RANDOM ROTATION (случайный поворот) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        random_rotation = nodes.new('FunctionNodeRandomValue')
        random_rotation.location = (500, -150)
        random_rotation.name = "Random Rotation Value"
        random_rotation.data_type = 'FLOAT_VECTOR'
        
        # Подключаем Random Rotation range и seed
        links.new(group_input.outputs['Random Rotation'], random_rotation.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_rotation.inputs['Seed'])
        
        rotate_random = nodes.new('GeometryNodeRotateInstances')
        rotate_random.location = (650, 0)
        rotate_random.name = "Random Rotation"
        
        links.new(translate_instances.outputs['Instances'], rotate_random.inputs['Instances'])
        links.new(random_rotation.outputs[0], rotate_random.inputs['Rotation'])
        
        # RANDOM SCALE (случайный масштаб) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        random_scale_val = nodes.new('FunctionNodeRandomValue')
        random_scale_val.location = (650, -150)
        random_scale_val.name = "Random Scale Value"
        random_scale_val.data_type = 'FLOAT'
        
        # Создаем диапазон: (1.0 - Random Scale) до (1.0 + Random Scale)
        scale_min = nodes.new('ShaderNodeMath')
        scale_min.location = (600, -200)
        scale_min.operation = 'SUBTRACT'
        scale_min.inputs[0].default_value = 1.0
        scale_max = nodes.new('ShaderNodeMath')
        scale_max.location = (600, -250)
        scale_max.operation = 'ADD'
        scale_max.inputs[0].default_value = 1.0
        
        links.new(group_input.outputs['Random Scale'], scale_min.inputs[1])
        links.new(group_input.outputs['Random Scale'], scale_max.inputs[1])
        links.new(scale_min.outputs[0], random_scale_val.inputs['Min'])
        links.new(scale_max.outputs[0], random_scale_val.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_scale_val.inputs['Seed'])
        
        # Конвертируем Float в Vector для uniform случайного масштаба
        combine_random_scale = nodes.new('ShaderNodeCombineXYZ')
        combine_random_scale.location = (700, -150)
        combine_random_scale.name = "Combine Random Scale"
        
        links.new(random_scale_val.outputs['Value'], combine_random_scale.inputs['X'])
        links.new(random_scale_val.outputs['Value'], combine_random_scale.inputs['Y'])
        links.new(random_scale_val.outputs['Value'], combine_random_scale.inputs['Z'])
        
        scale_random = nodes.new('GeometryNodeScaleInstances')
        scale_random.location = (800, 0)
        scale_random.name = "Random Scale"
        
        links.new(rotate_random.outputs['Instances'], scale_random.inputs['Instances'])
        links.new(combine_random_scale.outputs['Vector'], scale_random.inputs['Scale'])
        
        return scale_random.outputs['Instances']
    
    def _apply_hide_original(self, nodes, links, geometry_input, group_input):
        """ТОЧНАЯ КОПИЯ hide original из бэкапа"""
        
        # Join Geometry для объединения
        join_geometry = nodes.new('GeometryNodeJoinGeometry')
        join_geometry.location = (450, 50)
        join_geometry.name = "Join with Original"
        
        # Switch для Hide Original
        hide_switch = nodes.new('GeometryNodeSwitch')
        hide_switch.input_type = 'GEOMETRY'
        hide_switch.location = (500, 0)
        hide_switch.name = "Hide Original Switch"
        
        # Подключения
        links.new(group_input.outputs['Geometry'], join_geometry.inputs['Geometry'])
        links.new(geometry_input, join_geometry.inputs['Geometry'])
        
        links.new(group_input.outputs['Hide Original'], hide_switch.inputs['Switch'])
        links.new(join_geometry.outputs['Geometry'], hide_switch.inputs['False'])  # Show original
        links.new(geometry_input, hide_switch.inputs['True'])  # Hide original
        
        return hide_switch.outputs[0]  # ИСПРАВЛЕНО: outputs[0] вместо outputs['Output']
    
    def _create_socket_mapping(self, node_group):
        """Socket mapping для UI"""
        socket_mapping = {}
        for item in node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT':
                socket_mapping[item.name.lower().replace(' ', '_')] = item.identifier
        return socket_mapping
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """Отрисовка UI для Object Cloner - ТОЧНАЯ КОПИЯ из старого класса"""
        # Source Settings
        box = layout.box()
        box.label(text="Source Settings", icon='OBJECT_DATA')
        box.prop(props_owner, "object_source_mode", text="Source Mode")
        
        # Distribution Settings
        box = layout.box()
        box.label(text="Distribution Settings", icon='MESH_DATA')
        box.prop(props_owner, "object_distribution_mode", text="Distribution")
        box.prop(props_owner, "object_instance_count", text="Count")
        box.prop(props_owner, "object_density", text="Density")
        box.prop(props_owner, "object_face_center_mode", text="Face Centers")
        
        # Transform Settings
        box = layout.box()
        box.label(text="Transform Settings", icon='CON_TRANSFORM')
        box.prop(props_owner, "object_offset", text="Offset")
        box.prop(props_owner, "object_align_to_normal", text="Align to Normal")
        
        # Instance Transform
        col = box.column(align=True)
        col.label(text="Instance Transform:")
        col.prop(props_owner, "object_instance_scale", text="Scale")
        col.prop(props_owner, "object_instance_rotation", text="Rotation")
        col.prop(props_owner, "object_uniform_scale", text="Uniform Scale")
        
        # Random Transform
        col = box.column(align=True)
        col.label(text="Random Transform:")
        col.prop(props_owner, "object_random_position", text="Position")
        col.prop(props_owner, "object_random_rotation", text="Rotation")
        col.prop(props_owner, "object_random_scale", text="Scale")
        col.prop(props_owner, "object_random_seed", text="Seed")
        
        # Collection Settings
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == 'Source Mode':
                        mode_value = modifier.get(socket.identifier, 0)
                        if mode_value == 1:  # Collection mode
                            box = layout.box()
                            box.label(text="Collection Settings", icon='OUTLINER_COLLECTION')
                            box.prop(props_owner, "object_pick_instance", text="Random Pick")
                            if not props_owner.object_pick_instance:
                                box.prop(props_owner, "object_instance_index", text="Instance Index")
                            box.prop(props_owner, "object_collection_seed", text="Collection Seed")
                        break
        
        # Visibility
        box = layout.box()
        box.label(text="Visibility", icon='HIDE_OFF')
        box.prop(props_owner, "object_hide_original", text="Hide Original")
        
        # Anti-recursion
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == 'Realize Instances':
                        box.prop(modifier, f'["{socket.identifier}"]', text="Realize Instances (Anti-Recursion)")
                        break
        
        # Базовые параметры клонера из BaseCloner (если есть)
        if hasattr(self, '_draw_base_cloner_ui'):
            self._draw_base_cloner_ui(layout, props_owner, modifier)
    
    def get_default_parameters(self):
        base_defaults = super().get_default_parameters()
        object_defaults = {
            "source_mode": 0,
            "distribution_mode": 2,
            "instance_count": 50,
            "density": 1.0,
            "face_center_mode": False,
            "offset": 0.0,
            "align_to_normal": False,
            "instance_scale": (1.0, 1.0, 1.0),
            "instance_rotation": (0.0, 0.0, 0.0),
            "uniform_scale": False,
            "random_position": (0.0, 0.0, 0.0),
            "random_rotation": (0.0, 0.0, 0.0),
            "random_scale": 0.0,
            "random_seed": 0,
            "collection_pick_instance": True,
            "collection_instance_index": 0,
            "collection_random_seed": 2,
            "hide_original": False
        }
        return {**base_defaults, **object_defaults}


# Экземпляр класса
object_cloner = ObjectCloner()

# Функции register/unregister
def register():
    print("✅ Object Cloner (Fixed): Using class-based architecture, no registration needed")
    pass

def unregister():
    print("✅ Object Cloner (Fixed): Using class-based architecture, no unregistration needed") 
    pass