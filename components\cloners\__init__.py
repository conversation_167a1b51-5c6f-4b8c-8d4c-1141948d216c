"""
Cloners module для ClonerPro
Прямая реализация клонеров без абстракций
"""

from . import grid
from . import linear
from . import circle
from . import object
from . import spline
from . import honeycomb


def register():
    """Регистрация клонеров"""
    grid.register()
    linear.register()
    circle.register()
    object.register()
    spline.register()
    honeycomb.register()


def unregister():
    """Отмена регистрации клонеров"""
    honeycomb.unregister()
    spline.unregister()
    object.unregister()
    circle.unregister()
    linear.unregister()
    grid.unregister()