"""
ПОЛНАЯ система защиты от крашей dependency graph для ClonerPro
Портировано из advanced_cloners/core/utils/dependency_safety/

КРИТИЧНО: Эта система ОБЯЗАТЕЛЬНА для предотвращения крашей при операциях Undo/Redo
"""

import bpy
import traceback
from typing import Optional, List, Dict, Set, Tuple, Any


# ======================================================================
# DEPENDENCY CHECKER - Проверка зависимостей и валидация связей
# ======================================================================

def is_effector_newer_than_cloner(obj: bpy.types.Object, cloner_mod: bpy.types.Modifier, 
                                 effector_mod: bpy.types.Modifier) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет, создан ли эффектор позже клонера.
    
    Это критично для предотвращения крашей при операциях Undo/Redo.
    Если эффектор создан раньше клонера, операция Undo может нарушить 
    порядок dependency graph в Blender.
    """
    try:
        modifiers = list(obj.modifiers)
        
        try:
            cloner_index = modifiers.index(cloner_mod)
            effector_index = modifiers.index(effector_mod)
            
            # Модификаторы, созданные позже, обычно находятся выше в стеке
            is_safe = effector_index < cloner_index
            
            print(f"[DEPENDENCY SAFETY] Проверка порядка: эффектор {effector_mod.name} (индекс {effector_index}) "
                  f"vs клонер {cloner_mod.name} (индекс {cloner_index}) - {'безопасно' if is_safe else 'ОПАСНО'}")
            
            return is_safe
            
        except ValueError as e:
            print(f"[ERROR] Модификатор не найден в объекте: {e}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Ошибка при проверке порядка создания: {e}")
        return False


def is_cloner_modifier(mod: bpy.types.Modifier) -> bool:
    """Определяет, является ли модификатор клонером."""
    if mod.type != 'NODES' or not mod.node_group:
        return False
    
    # Проверяем метаданные
    component_type = mod.node_group.get("component_type")
    if component_type == 'CLONER':
        return True
    
    # Fallback на проверку по имени
    node_group_name = mod.node_group.name.lower()
    cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral']
    return any(keyword in node_group_name for keyword in cloner_keywords)


def is_effector_modifier(mod: bpy.types.Modifier) -> bool:
    """Определяет, является ли модификатор эффектором."""
    if mod.type != 'NODES' or not mod.node_group:
        return False
    
    # Проверяем метаданные
    component_type = mod.node_group.get("component_type")
    if component_type == 'EFFECTOR':
        return True
    
    # Fallback на проверку по имени
    node_group_name = mod.node_group.name.lower()
    effector_keywords = ['effector', 'random', 'noise']
    return any(keyword in node_group_name for keyword in effector_keywords)


def is_node_properly_connected(node: bpy.types.Node) -> bool:
    """Проверяет, правильно ли подключен узел."""
    try:
        input_connected = any(socket.is_linked for socket in node.inputs)
        output_connected = any(socket.is_linked for socket in node.outputs)
        return input_connected and output_connected
    except Exception as e:
        print(f"[ERROR] Ошибка при проверке подключения узла: {e}")
        return False


def check_if_effector_chain_needs_rebuild_enhanced(cloner_group: bpy.types.NodeGroup) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Улучшенная проверка необходимости перестройки цепочки эффекторов.
    """
    try:
        if not cloner_group:
            return False
        
        # Получаем список связанных эффекторов из метаданных
        linked_effectors = cloner_group.get("linked_effectors", [])
        if not linked_effectors:
            return False
        
        print(f"[DEBUG] Проверяем цепочку эффекторов для клонера {cloner_group.name}")
        print(f"[DEBUG] Ожидаемые эффекторы: {linked_effectors}")
        
        # Ищем узлы эффекторов в группе
        effector_nodes = []
        found_effector_names = []
        
        for node in cloner_group.nodes:
            if ("Effector" in node.name and 
                hasattr(node, 'node_tree') and node.node_tree):
                effector_nodes.append(node)
                
                # Извлекаем имя эффектора из имени узла
                effector_name = extract_effector_name_from_node(node)
                if effector_name:
                    found_effector_names.append(effector_name)
        
        print(f"[DEBUG] Найденные узлы эффекторов: {[n.name for n in effector_nodes]}")
        print(f"[DEBUG] Извлеченные имена эффекторов: {found_effector_names}")
        
        # Проверяем соответствие количества
        if len(effector_nodes) != len(linked_effectors):
            print(f"[DEBUG] Несоответствие количества: {len(effector_nodes)} узлов vs {len(linked_effectors)} эффекторов")
            return True
        
        # Проверяем, что все ожидаемые эффекторы присутствуют
        for expected_effector in linked_effectors:
            if expected_effector not in found_effector_names:
                print(f"[DEBUG] Отсутствует эффектор: {expected_effector}")
                return True
        
        # Проверяем правильность подключения каждого узла
        for node in effector_nodes:
            if not is_node_properly_connected(node):
                print(f"[DEBUG] Узел {node.name} неправильно подключен")
                return True
        
        print(f"[DEBUG] Цепочка эффекторов в порядке")
        return False
        
    except Exception as e:
        print(f"[ERROR] Ошибка при улучшенной проверке цепочки эффекторов: {e}")
        return True


def extract_effector_name_from_node(node: bpy.types.Node) -> Optional[str]:
    """Извлекает имя эффектора из узла."""
    try:
        # Пытаемся извлечь из имени узла
        if "Effector_" in node.name:
            return node.name.replace("Effector_", "")
        
        # Пытаемся извлечь из node_tree
        if hasattr(node, 'node_tree') and node.node_tree:
            tree_name = node.node_tree.name
            # Ищем соответствующий модификатор по имени node_tree
            for obj in bpy.data.objects:
                for mod in obj.modifiers:
                    if (mod.type == 'NODES' and mod.node_group and 
                        mod.node_group.name == tree_name and
                        is_effector_modifier(mod)):
                        return mod.name
        
        return None
        
    except Exception as e:
        print(f"[ERROR] Ошибка при извлечении имени эффектора: {e}")
        return None


def synchronize_metadata_with_physical_links_enhanced(cloner_group: bpy.types.NodeGroup) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Улучшенная синхронизация метаданных клонера с физическими связями.
    """
    try:
        if not cloner_group:
            return False
        
        print(f"[DEBUG] Начинаем улучшенную синхронизацию метаданных для {cloner_group.name}")
        
        # Получаем текущие метаданные
        current_linked_effectors = list(cloner_group.get("linked_effectors", []))
        print(f"[DEBUG] Текущие метаданные: {current_linked_effectors}")
        
        # Ищем физически присутствующие узлы эффекторов
        physical_effector_names = []
        disconnected_nodes = []
        
        for node in cloner_group.nodes:
            if ("Effector" in node.name and 
                hasattr(node, 'node_tree') and node.node_tree):
                
                effector_name = extract_effector_name_from_node(node)
                if effector_name:
                    if is_node_properly_connected(node):
                        physical_effector_names.append(effector_name)
                        print(f"[DEBUG] Найден подключенный эффектор: {effector_name}")
                    else:
                        disconnected_nodes.append((node, effector_name))
                        print(f"[DEBUG] Найден отключенный эффектор: {effector_name}")
        
        print(f"[DEBUG] Физически подключенные эффекторы: {physical_effector_names}")
        print(f"[DEBUG] Отключенные узлы: {[name for _, name in disconnected_nodes]}")
        
        # Определяем, какие эффекторы нужно удалить из метаданных
        effectors_to_remove = [name for name in current_linked_effectors 
                              if name not in physical_effector_names]
        
        # Определяем, какие эффекторы нужно добавить в метаданные
        effectors_to_add = [name for name in physical_effector_names 
                           if name not in current_linked_effectors]
        
        # Формируем обновленные метаданные
        updated_metadata = [name for name in current_linked_effectors 
                           if name not in effectors_to_remove]
        updated_metadata.extend(effectors_to_add)
        
        # Обновляем метаданные если они изменились
        metadata_changed = False
        if updated_metadata != current_linked_effectors:
            cloner_group["linked_effectors"] = updated_metadata
            print(f"[DEBUG] Обновлены метаданные: {current_linked_effectors} -> {updated_metadata}")
            metadata_changed = True
        
        # Очищаем отключенные узлы если они не должны быть в метаданных
        nodes_removed = False
        for node, effector_name in disconnected_nodes:
            if effector_name not in updated_metadata:
                try:
                    cloner_group.nodes.remove(node)
                    print(f"[DEBUG] Удален отключенный узел эффектора: {effector_name}")
                    nodes_removed = True
                except Exception as e:
                    print(f"[WARNING] Не удалось удалить узел {effector_name}: {e}")
        
        return metadata_changed or nodes_removed
        
    except Exception as e:
        print(f"[ERROR] Ошибка при улучшенной синхронизации метаданных: {e}")
        return False


# ======================================================================
# DEPENDENCY HANDLER - Обработчики событий и управление зависимостями  
# ======================================================================

def create_safe_dependency_handler(node_group: bpy.types.NodeGroup, effector_name: str, 
                                  effector_type: str) -> Optional[bpy.types.Node]:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Создает или возвращает существующий безопасный прокси-узел внутри клонера.
    """
    try:
        # Проверяем, есть ли уже прокси для этого эффектора
        proxy_name = f"SafeProxy_{effector_name}"
        
        # Ищем существующий прокси-узел
        for node in node_group.nodes:
            if node.name == proxy_name and node.type == 'GROUP_INPUT':
                print(f"[DEBUG] Найден существующий прокси-узел для эффектора {effector_name}")
                return node
        
        # Создаем новый прокси-узел
        proxy_node = node_group.nodes.new('NodeGroupInput')
        proxy_node.name = proxy_name
        
        # Добавляем метаданные
        proxy_node["is_effector_proxy"] = True
        proxy_node["safe_proxy_for_effector"] = effector_name
        proxy_node["proxy_for_effector_type"] = effector_type
        
        # Сохраняем информацию о прокси в метаданных клонера
        key = f"safe_proxy_for_{effector_name}"
        node_group[key] = True
        
        # Инициализируем список безопасных обработчиков, если его еще нет
        if "safe_dependency_handlers" not in node_group:
            node_group["safe_dependency_handlers"] = [proxy_name]
        else:
            # Преобразуем IDPropertyArray в обычный список Python
            handlers = list(node_group["safe_dependency_handlers"])
            
            # Добавляем новый элемент, если его еще нет
            if proxy_name not in handlers:
                handlers.append(proxy_name)
                node_group["safe_dependency_handlers"] = handlers
        
        # Добавляем свойство для отслеживания состояния при Undo/Redo
        node_group["undo_safe_state"] = node_group.get("undo_safe_state", 0) + 1
        
        # Добавляем специальные метаданные для восстановления после Undo/Redo
        proxy_node["rebuild_after_undo"] = True
        
        print(f"[DEBUG] Создан безопасный прокси-узел {proxy_name} для эффектора {effector_name}")
        return proxy_node
        
    except Exception as e:
        print(f"[ERROR] Ошибка при создании безопасного прокси-узла: {e}")
        traceback.print_exc()
        return None


def temporarily_disconnect_safe_proxies(cloner_group: bpy.types.NodeGroup):
    """Временно отключает безопасные прокси для операций."""
    try:
        print(f"[DEBUG] Временное отключение прокси в {cloner_group.name}")
        
        disconnected_proxies = []
        
        for node in cloner_group.nodes:
            if node.get("is_safe_proxy", False):
                # Сохраняем информацию о соединениях
                connections = {
                    'inputs': [],
                    'outputs': []
                }
                
                # Сохраняем входные соединения
                for socket in node.inputs:
                    if socket.is_linked:
                        for link in socket.links:
                            connections['inputs'].append((link.from_node, link.from_socket, socket))
                
                # Сохраняем выходные соединения
                for socket in node.outputs:
                    if socket.is_linked:
                        for link in socket.links:
                            connections['outputs'].append((socket, link.to_node, link.to_socket))
                
                # Отключаем узел
                for socket in node.inputs:
                    for link in socket.links[:]:
                        cloner_group.links.remove(link)
                
                for socket in node.outputs:
                    for link in socket.links[:]:
                        cloner_group.links.remove(link)
                
                # Сохраняем информацию для восстановления
                node["temp_connections"] = str(connections)  # Конвертируем в строку для безопасности
                disconnected_proxies.append(node.name)
        
        print(f"[DEBUG] Временно отключено прокси: {disconnected_proxies}")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при временном отключении прокси: {e}")


def rebuild_effector_chain_improved(cloner_group: bpy.types.NodeGroup, linked_effectors: List[str]):
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Улучшенная перестройка цепочки эффекторов.
    """
    try:
        print(f"[DEBUG] Улучшенная перестройка цепочки эффекторов")
        
        # Очищаем существующие узлы эффекторов
        nodes_to_remove = []
        for node in cloner_group.nodes:
            if "Effector" in node.name and hasattr(node, 'node_tree'):
                nodes_to_remove.append(node)
        
        for node in nodes_to_remove:
            cloner_group.nodes.remove(node)
            print(f"[DEBUG] Удален старый узел эффектора: {node.name}")
        
        # Создаем новые узлы эффекторов используя ClonerPro менеджеры
        from ..managers.effector_linking import create_effector_node_in_cloner
        
        # Находим объект, содержащий этот клонер
        cloner_obj = None
        for obj in bpy.data.objects:
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group == cloner_group:
                    cloner_obj = obj
                    break
            if cloner_obj:
                break
        
        if cloner_obj:
            # Перестраиваем цепочку для каждого эффектора
            for effector_name in linked_effectors:
                # Находим модификатор эффектора
                effector_mod = None
                for obj in bpy.data.objects:
                    if effector_name in obj.modifiers:
                        mod = obj.modifiers[effector_name]
                        if mod.type == 'NODES' and mod.node_group:
                            effector_mod = mod
                            break
                
                if effector_mod:
                    create_effector_node_in_cloner(cloner_obj, cloner_group, effector_name, effector_mod.node_group)
                    print(f"[DEBUG] Восстановлен узел эффектора: {effector_name}")
            
            print(f"[DEBUG] Цепочка эффекторов перестроена")
        else:
            print(f"[WARNING] Не найден объект для клонера {cloner_group.name}")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при улучшенной перестройке цепочки: {e}")


# ======================================================================
# DEPENDENCY RECOVERY - Восстановление состояния и очистка кэшей
# ======================================================================

def complete_effector_memory_reset_on_undo():
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Полная очистка памяти от всех эффекторов при Undo.
    
    Эта функция выполняет агрессивную очистку всех кэшей, метаданных и связей
    для всех эффекторов в сцене, как если бы они были удалены и созданы заново.
    """
    print("[DEBUG] === ПОЛНАЯ ОЧИСТКА ПАМЯТИ ОТ ВСЕХ ЭФФЕКТОРОВ ===")
    
    try:
        # Получаем список всех эффекторов в сцене
        all_effector_names = []
        for obj in bpy.data.objects:
            if not hasattr(obj, 'modifiers'):
                continue
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    component_type = mod.node_group.get("component_type")
                    if component_type == 'EFFECTOR':
                        all_effector_names.append(mod.name)
                    elif any(keyword in mod.node_group.name.lower() 
                           for keyword in ['effector', 'random', 'noise']):
                        all_effector_names.append(mod.name)
        
        print(f"[DEBUG] Найдено эффекторов для очистки: {len(all_effector_names)}")
        print(f"[DEBUG] Список эффекторов: {all_effector_names}")
        
        # Выполняем полную очистку памяти для каждого эффектора
        for effector_name in all_effector_names:
            try:
                # КРИТИЧЕСКИ ВАЖНО: Очищаем эффектор из кэша ПЕРЕД очисткой памяти
                try:
                    from .cache_manager import EffectorCacheManager
                    EffectorCacheManager.clear_effector_from_cache(effector_name)
                except Exception as cache_error:
                    print(f"[WARNING] Ошибка очистки кэша для {effector_name}: {cache_error}")
                
                # Используем полную очистку памяти эффектора
                complete_effector_memory_cleanup(effector_name)
                print(f"[DEBUG] ✓ Память очищена для эффектора: {effector_name}")
            except Exception as e:
                print(f"[WARNING] Ошибка очистки памяти для {effector_name}: {e}")
        
        print("[DEBUG] === ПОЛНАЯ ОЧИСТКА ПАМЯТИ ЗАВЕРШЕНА ===")
        
    except Exception as e:
        print(f"[ERROR] Критическая ошибка при полной очистке памяти: {e}")
        traceback.print_exc()


def aggressive_cache_cleanup():
    """Агрессивная очистка всех кэшей системы."""
    print("[DEBUG] Начинаем агрессивную очистку кэшей")
    
    try:
        # КРИТИЧЕСКИ ВАЖНО: Очищаем кэш эффекторов
        from .cache_manager import EffectorCacheManager, clear_all_caches
        EffectorCacheManager.invalidate_cache()
        clear_all_caches()
        print("[DEBUG] ✓ Кэш эффекторов очищен")
        
        # Принудительная очистка системных кэшей
        force_clear_all_caches()
        
        print("[DEBUG] Агрессивная очистка кэшей завершена")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при агрессивной очистке кэшей: {e}")
        traceback.print_exc()


def force_clear_all_caches():
    """Принудительная очистка всех возможных кэшей."""
    try:
        # Очистка внутренних кэшей Blender
        bpy.context.view_layer.update()
        
        # Принудительное обновление dependency graph
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            depsgraph = bpy.context.evaluated_depsgraph_get()
            depsgraph.update()
        
        print("[DEBUG] ✓ Системные кэши принудительно очищены")
        
    except Exception as e:
        print(f"[WARNING] Ошибка при принудительной очистке кэшей: {e}")


def global_effector_cloner_sync():
    """Глобальная синхронизация всех связей эффектор-клонер в сцене."""
    print("[DEBUG] Начинаем глобальную синхронизацию эффектор-клонер")
    
    try:
        synced_cloners = 0
        
        for obj in bpy.data.objects:
            if not hasattr(obj, 'modifiers'):
                continue
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    # Проверяем, является ли это клонером
                    if is_cloner_modifier(mod):
                        try:
                            if synchronize_metadata_with_physical_links_enhanced(mod.node_group):
                                synced_cloners += 1
                                print(f"[DEBUG] Синхронизирован клонер: {mod.name}")
                        except Exception as e:
                            print(f"[WARNING] Ошибка синхронизации клонера {mod.name}: {e}")
        
        print(f"[DEBUG] Глобальная синхронизация завершена: {synced_cloners} клонеров обновлено")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при глобальной синхронизации: {e}")
        traceback.print_exc()


def final_system_cleanup_and_update():
    """Финальная очистка и обновление системы."""
    print("[DEBUG] Выполняем финальную очистку системы")
    
    try:
        # Обновляем view layer
        bpy.context.view_layer.update()
        
        # Принудительное обновление всех объектов
        for obj in bpy.data.objects:
            obj.update_tag()
        
        # Обновляем сцену
        if hasattr(bpy.context, 'scene'):
            bpy.context.scene.update_tag()
        
        print("[DEBUG] ✓ Финальная очистка системы завершена")
        
    except Exception as e:
        print(f"[WARNING] Ошибка при финальной очистке: {e}")


def emergency_system_reset():
    """Аварийный сброс состояния системы."""
    print("[DEBUG] === АВАРИЙНЫЙ СБРОС СИСТЕМЫ ===")
    
    try:
        # 1. Полная очистка всех кэшей
        aggressive_cache_cleanup()
        
        # 2. Сброс всех флагов состояния
        try:
            import bpy
            if hasattr(bpy.context, 'scene'):
                bpy.context.scene['clonerpro_undo_reset'] = True
                bpy.context.scene['clonerpro_forced_refresh'] = True
                bpy.context.scene['clonerpro_metadata_reset'] = True
                bpy.context.scene['clonerpro_emergency_reset'] = True
                print("[DEBUG] ✓ Установлены флаги аварийного сброса")
        except Exception as e:
            print(f"[WARNING] Ошибка установки флагов: {e}")
        
        # 3. Принудительное обновление всей сцены
        final_system_cleanup_and_update()
        
        print("[DEBUG] === АВАРИЙНЫЙ СБРОС ЗАВЕРШЕН ===")
        
    except Exception as e:
        print(f"[ERROR] Критическая ошибка при аварийном сбросе: {e}")
        traceback.print_exc()


def force_refresh_all_link_checks():
    """Принудительное обновление всех проверок связей."""
    print("[DEBUG] Принудительное обновление всех проверок связей")
    
    try:
        # Устанавливаем флаги принудительного обновления
        import bpy
        if hasattr(bpy.context, 'scene'):
            bpy.context.scene['clonerpro_forced_refresh'] = True
            bpy.context.scene['clonerpro_link_refresh'] = True
        
        # Глобальная синхронизация
        global_effector_cloner_sync()
        
        print("[DEBUG] Принудительное обновление проверок связей завершено")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при принудительном обновлении проверок связей: {e}")


def full_reset_addon_state():
    """Полный сброс состояния аддона."""
    print("[DEBUG] === ПОЛНЫЙ СБРОС СОСТОЯНИЯ АДДОНА ===")
    
    try:
        # 1. Аварийный сброс системы
        emergency_system_reset()
        
        # 2. Полная очистка памяти от эффекторов
        complete_effector_memory_reset_on_undo()
        
        # 3. Принудительное обновление всех проверок
        force_refresh_all_link_checks()
        
        # 4. Финальная очистка
        final_system_cleanup_and_update()
        
        print("[DEBUG] === ПОЛНЫЙ СБРОС СОСТОЯНИЯ ЗАВЕРШЕН ===")
        
    except Exception as e:
        print(f"[ERROR] Критическая ошибка при полном сбросе состояния: {e}")
        traceback.print_exc()


# ======================================================================
# КРИТИЧЕСКИЕ ОБРАБОТЧИКИ СОБЫТИЙ
# ======================================================================

@bpy.app.handlers.persistent
def undo_pre_safety_handler(scene, *args):
    """
    КРИТИЧЕСКИЙ ОБРАБОТЧИК: Событие undo_pre для безопасной обработки операций Undo.
    
    Временно отключает опасные зависимости перед операцией Undo для предотвращения крашей.
    """
    global _UNDO_OPERATION_IN_PROGRESS, _HANDLER_BLOCKED, _RECURSIVE_CALL_DEPTH
    
    # КРИТИЧЕСКАЯ ЗАЩИТА ОТ РЕКУРСИИ
    if _HANDLER_BLOCKED:
        print("[SAFETY] undo_pre заблокирован - предотвращение рекурсии")
        return
    
    _RECURSIVE_CALL_DEPTH += 1
    if _RECURSIVE_CALL_DEPTH > _MAX_RECURSIVE_DEPTH:
        print(f"[SAFETY] Превышена максимальная глубина рекурсии: {_RECURSIVE_CALL_DEPTH}")
        _RECURSIVE_CALL_DEPTH = 0
        return
    
    _HANDLER_BLOCKED = True
    print("[DEPENDENCY SAFETY] Подготовка к безопасной операции Undo")
    
    try:
        # Устанавливаем флаги подготовки к Undo
        _UNDO_OPERATION_IN_PROGRESS = True
        
        import bpy
        if hasattr(bpy.context, 'scene'):
            bpy.context.scene['clonerpro_undo_in_progress'] = True
            bpy.context.scene['clonerpro_handler_blocked'] = True
        
        # Временно отключаем прокси-узлы во всех клонерах
        for obj in bpy.data.objects:
            if not hasattr(obj, 'modifiers'):
                continue
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    temporarily_disconnect_safe_proxies(mod.node_group)
        
    except Exception as e:
        print(f"[ERROR] Ошибка в обработчике undo_pre: {e}")
        traceback.print_exc()
        
    finally:
        # КРИТИЧНО: Всегда разблокируем handler
        _HANDLER_BLOCKED = False
        _RECURSIVE_CALL_DEPTH = max(0, _RECURSIVE_CALL_DEPTH - 1)


@bpy.app.handlers.persistent
def undo_post_safety_handler(scene, *args):
    """
    КРИТИЧЕСКИЙ ОБРАБОТЧИК: Событие после Undo/Redo.
    Полностью восстанавливает целостность системы после операций отмены.

    КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Теперь выполняет полную очистку памяти от всех эффекторов,
    как при их удалении, чтобы предотвратить проблему с повторной привязкой.
    """
    global _UNDO_OPERATION_IN_PROGRESS, _HANDLER_BLOCKED, _RECURSIVE_CALL_DEPTH, _LAST_UPDATE_TIME
    import time
    
    # КРИТИЧЕСКАЯ ЗАЩИТА ОТ РЕКУРСИИ И RATE LIMITING
    current_time = time.time()
    if current_time - _LAST_UPDATE_TIME < _UPDATE_COOLDOWN:
        print(f"[SAFETY] undo_post пропущен - rate limiting ({current_time - _LAST_UPDATE_TIME:.3f}s)")
        return
    
    if _HANDLER_BLOCKED:
        print("[SAFETY] undo_post заблокирован - предотвращение рекурсии")
        return
    
    _RECURSIVE_CALL_DEPTH += 1
    if _RECURSIVE_CALL_DEPTH > _MAX_RECURSIVE_DEPTH:
        print(f"[SAFETY] Превышена максимальная глубина рекурсии: {_RECURSIVE_CALL_DEPTH}")
        _RECURSIVE_CALL_DEPTH = 0
        return
    
    _HANDLER_BLOCKED = True
    _LAST_UPDATE_TIME = current_time
    
    print(f"[DEBUG] === ЗАПУСК КРИТИЧЕСКИ ИСПРАВЛЕННОГО UNDO POST SAFETY HANDLER ===")
    try:
        # НОВОЕ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Полная очистка памяти от всех эффекторов
        print(f"[DEBUG] Шаг 1: ПОЛНАЯ ОЧИСТКА ПАМЯТИ ОТ ВСЕХ ЭФФЕКТОРОВ")
        complete_effector_memory_reset_on_undo()

        # КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Полная очистка всех кэшей в первую очередь
        print(f"[DEBUG] Шаг 2: Полная очистка всех системных кэшей")
        aggressive_cache_cleanup()

        # ИСПРАВЛЕНИЕ: Глобальная синхронизация всех связей эффектор-клонер
        print(f"[DEBUG] Шаг 3: Глобальная синхронизация связей эффектор-клонер")
        global_effector_cloner_sync()

        # ИСПРАВЛЕНИЕ: Финальное обновление системы
        print(f"[DEBUG] Шаг 4: Финальное обновление и очистка системы")
        final_system_cleanup_and_update()

        # НОВОЕ: Принудительное обновление всех функций проверки связей
        print(f"[DEBUG] Шаг 5: Принудительное обновление функций проверки связей")
        force_refresh_all_link_checks()

        # КРИТИЧЕСКИ ВАЖНО: Устанавливаем ВСЕ флаги принудительного обновления
        print(f"[DEBUG] Шаг 6: Установка флагов принудительного обновления")
        if hasattr(bpy.context, 'scene'):
            bpy.context.scene['clonerpro_forced_refresh'] = True
            bpy.context.scene['clonerpro_undo_reset'] = True
            bpy.context.scene['clonerpro_metadata_reset'] = True
            bpy.context.scene['clonerpro_emergency_reset'] = True
            bpy.context.scene['clonerpro_link_refresh'] = True
            bpy.context.scene['clonerpro_cache_invalidated'] = True
            print("[DEBUG] ✓ ВСЕ флаги принудительного обновления установлены")

        print(f"[DEBUG] === КРИТИЧЕСКИ ИСПРАВЛЕННЫЙ UNDO POST SAFETY HANDLER ЗАВЕРШЕН ===")

    except Exception as e:
        print(f"[ERROR] КРИТИЧЕСКАЯ ОШИБКА в undo_post_safety_handler: {e}")
        traceback.print_exc()
        
        # Аварийный сброс в случае критической ошибки
        try:
            emergency_system_reset()
        except Exception as emergency_error:
            print(f"[CRITICAL] Ошибка даже при аварийном сбросе: {emergency_error}")
    
    finally:
        # КРИТИЧНО: Всегда разблокируем handler и сбрасываем флаги
        _UNDO_OPERATION_IN_PROGRESS = False
        _HANDLER_BLOCKED = False
        _RECURSIVE_CALL_DEPTH = max(0, _RECURSIVE_CALL_DEPTH - 1)
        
        # Сбрасываем блокировки в сцене
        try:
            if hasattr(bpy.context, 'scene'):
                bpy.context.scene['clonerpro_undo_in_progress'] = False
                bpy.context.scene['clonerpro_handler_blocked'] = False
        except:
            pass


@bpy.app.handlers.persistent
def depsgraph_update_post_handler(scene, depsgraph):
    """
    Обработчик изменений depsgraph, который отслеживает состояние клонеров и эффекторов.
    Этот обработчик помогает восстановить состояние после глубоких Undo операций.
    """
    global _HANDLER_BLOCKED, _LAST_UPDATE_TIME, _RECURSIVE_CALL_DEPTH
    import time
    
    try:
        # КРИТИЧЕСКАЯ ЗАЩИТА ОТ РЕКУРСИИ И RATE LIMITING
        current_time = time.time()
        if current_time - _LAST_UPDATE_TIME < _UPDATE_COOLDOWN:
            return
        
        if _HANDLER_BLOCKED:
            return
        
        # Проверяем, не заблокированы ли мы на уровне сцены
        try:
            if hasattr(bpy.context, 'scene'):
                handler_blocked = bpy.context.scene.get('clonerpro_handler_blocked', False)
                undo_in_progress = bpy.context.scene.get('clonerpro_undo_in_progress', False)
                if handler_blocked or undo_in_progress:
                    return
        except:
            pass
        
        # Ограничиваем частоту выполнения
        if not hasattr(depsgraph_update_post_handler, "counter"):
            depsgraph_update_post_handler.counter = 0
        
        depsgraph_update_post_handler.counter += 1
        
        # Выполняем проверки только каждые 10 вызовов для производительности
        if depsgraph_update_post_handler.counter % 10 != 0:
            return
        
        # Проверяем, нужно ли восстановление после Undo
        if hasattr(bpy.context, 'scene'):
            undo_reset = bpy.context.scene.get('clonerpro_undo_reset', False)
            if undo_reset:
                print("[DEPENDENCY SAFETY] Обнаружен флаг undo_reset, выполняем восстановление")
                
                # Восстанавливаем все связи
                global_effector_cloner_sync()
                
                # Сбрасываем флаг
                bpy.context.scene['clonerpro_undo_reset'] = False
                print("[DEPENDENCY SAFETY] Восстановление после Undo завершено")
        
    except Exception as e:
        print(f"[ERROR] Ошибка в depsgraph_update_post_handler: {e}")


@bpy.app.handlers.persistent  
def on_load_post_handler(_scene):
    """
    Обработчик события загрузки файла.
    Выполняет очистку и восстановление состояния при загрузке .blend файла.
    """
    print("[DEPENDENCY SAFETY] Обработка загрузки файла")
    
    try:
        # Полная очистка кэшей при загрузке файла
        aggressive_cache_cleanup()
        
        # Глобальная синхронизация
        global_effector_cloner_sync()
        
        print("[DEPENDENCY SAFETY] Загрузка файла обработана")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при обработке загрузки файла: {e}")


# ======================================================================
# ОСНОВНЫЕ КООРДИНИРУЮЩИЕ ФУНКЦИИ
# ======================================================================

def diagnose_effector_cloner_relationships():
    """
    Диагностирует все связи эффектор-клонер в сцене.
    
    Главная функция для анализа состояния системы.
    """
    print("[DEPENDENCY SAFETY] === ДИАГНОСТИКА СВЯЗЕЙ ЭФФЕКТОР-КЛОНЕР ===")
    
    try:
        relationships_found = 0
        problems_detected = 0
        
        for obj in bpy.data.objects:
            if not hasattr(obj, 'modifiers'):
                continue
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    if is_cloner_modifier(mod):
                        linked_effectors = mod.node_group.get("linked_effectors", [])
                        if linked_effectors:
                            relationships_found += len(linked_effectors)
                            print(f"[DEBUG] Клонер {mod.name}: {linked_effectors}")
                            
                            # Проверяем, нужна ли перестройка цепочки
                            if check_if_effector_chain_needs_rebuild_enhanced(mod.node_group):
                                problems_detected += 1
                                print(f"[WARNING] Клонер {mod.name} нуждается в перестройке цепочки")
        
        print(f"[DEPENDENCY SAFETY] Найдено связей: {relationships_found}")
        print(f"[DEPENDENCY SAFETY] Обнаружено проблем: {problems_detected}")
        print("[DEPENDENCY SAFETY] === ДИАГНОСТИКА ЗАВЕРШЕНА ===")
        
        return {
            "relationships_found": relationships_found,
            "problems_detected": problems_detected
        }
        
    except Exception as e:
        print(f"[ERROR] Ошибка при диагностике связей: {e}")
        traceback.print_exc()
        return {"relationships_found": 0, "problems_detected": -1}


def repair_all_effector_cloner_relationships():
    """
    Восстанавливает все связи эффектор-клонер в сцене.
    
    Главная функция для восстановления системы.
    """
    print("[DEPENDENCY SAFETY] === ВОССТАНОВЛЕНИЕ ВСЕХ СВЯЗЕЙ ===")
    
    try:
        # 1. Диагностика текущего состояния
        diagnosis = diagnose_effector_cloner_relationships()
        
        if diagnosis["problems_detected"] == 0:
            print("[DEPENDENCY SAFETY] Проблем не обнаружено")
            return True
        
        # 2. Агрессивная очистка кэшей
        aggressive_cache_cleanup()
        
        # 3. Глобальная синхронизация
        global_effector_cloner_sync()
        
        # 4. Принудительное обновление проверок связей
        force_refresh_all_link_checks()
        
        # 5. Финальная очистка
        final_system_cleanup_and_update()
        
        # 6. Повторная диагностика
        final_diagnosis = diagnose_effector_cloner_relationships()
        
        success = final_diagnosis["problems_detected"] == 0
        print(f"[DEPENDENCY SAFETY] Восстановление {'успешно' if success else 'частично'}")
        print("[DEPENDENCY SAFETY] === ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО ===")
        
        return success
        
    except Exception as e:
        print(f"[ERROR] Ошибка при восстановлении связей: {e}")
        traceback.print_exc()
        return False


def emergency_repair_system():
    """
    Аварийное восстановление системы.
    
    Использует самые агрессивные методы восстановления.
    """
    print("[DEPENDENCY SAFETY] === АВАРИЙНОЕ ВОССТАНОВЛЕНИЕ СИСТЕМЫ ===")
    
    try:
        # Полный сброс состояния аддона
        full_reset_addon_state()
        
        # Повторная попытка восстановления
        success = repair_all_effector_cloner_relationships()
        
        print(f"[DEPENDENCY SAFETY] Аварийное восстановление {'успешно' if success else 'не удалось'}")
        print("[DEPENDENCY SAFETY] === АВАРИЙНОЕ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО ===")
        
        return success
        
    except Exception as e:
        print(f"[ERROR] Критическая ошибка при аварийном восстановлении: {e}")
        traceback.print_exc()
        return False


# ======================================================================
# SAFE EXECUTION WRAPPERS - безопасные обертки для операций
# ======================================================================

# Глобальные флаги состояния
_UNDO_OPERATION_IN_PROGRESS = False
_SYSTEM_CLEANUP_IN_PROGRESS = False
_EFFECTOR_OPERATION_BLOCKED = False

# КРИТИЧЕСКИ ВАЖНЫЕ ФЛАГИ ДЛЯ ПРЕДОТВРАЩЕНИЯ РЕКУРСИИ
_HANDLER_BLOCKED = False
_LAST_UPDATE_TIME = 0.0
_UPDATE_COOLDOWN = 0.1  # 100ms минимум между обновлениями
_RECURSIVE_CALL_DEPTH = 0
_MAX_RECURSIVE_DEPTH = 3

def safe_execute_effector_operation(operation_func, *args, **kwargs):
    """Безопасное выполнение операций с эффекторами"""
    global _EFFECTOR_OPERATION_BLOCKED
    
    # Проверяем безопасность операции
    if _UNDO_OPERATION_IN_PROGRESS or _SYSTEM_CLEANUP_IN_PROGRESS:
        print("[SAFETY] Операция заблокирована - система в процессе очистки")
        return False, "System cleanup in progress"
    
    if _EFFECTOR_OPERATION_BLOCKED:
        print("[SAFETY] Операция заблокирована - другая операция выполняется")
        return False, "Another operation in progress"
    
    try:
        _EFFECTOR_OPERATION_BLOCKED = True
        
        # Проверяем состояние Blender
        if not bpy.context.scene or not bpy.context.active_object:
            return False, "Invalid Blender context"
        
        # Выполняем операцию
        result = operation_func(*args, **kwargs)
        return True, result
        
    except Exception as e:
        print(f"[SAFETY] Ошибка операции с эффектором: {e}")
        
        # В случае ошибки выполняем экстренную очистку
        try:
            emergency_system_reset()
        except:
            pass
        
        return False, str(e)
        
    finally:
        _EFFECTOR_OPERATION_BLOCKED = False


def is_safe_to_perform_effector_operations() -> bool:
    """Проверяет, безопасно ли выполнять операции с эффекторами"""
    global _UNDO_OPERATION_IN_PROGRESS, _SYSTEM_CLEANUP_IN_PROGRESS, _EFFECTOR_OPERATION_BLOCKED
    
    if _UNDO_OPERATION_IN_PROGRESS:
        return False
    
    if _SYSTEM_CLEANUP_IN_PROGRESS:
        return False
        
    if _EFFECTOR_OPERATION_BLOCKED:
        return False
    
    # Проверяем доступность Blender контекста
    try:
        if not bpy.context.scene:
            return False
        if not hasattr(bpy.context, 'active_object'):
            return False
        return True
    except:
        return False


def complete_effector_memory_cleanup(effector_name: str):
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Полная очистка памяти конкретного эффектора.
    Удаляет эффектор из всех кэшей, метаданных и связей.
    """
    try:
        print(f"[MEMORY CLEANUP] Полная очистка памяти эффектора: {effector_name}")
        
        # 1. БЕЗОПАСНО очищаем эффектор из всех клонеров
        from .safe_operations import safe_get_object, safe_get_modifier, safe_access_node_group, safe_remove_node
        
        for obj in bpy.data.objects:
            # КРИТИЧНО: Безопасная проверка объекта
            safe_obj = safe_get_object(obj.name)
            if not safe_obj or not hasattr(safe_obj, 'modifiers'):
                continue
                
            for mod in safe_obj.modifiers:
                # КРИТИЧНО: Безопасная проверка модификатора
                safe_mod = safe_get_modifier(safe_obj, mod.name)
                if not safe_mod:
                    continue
                
                # КРИТИЧНО: Безопасный доступ к node group
                node_group = safe_access_node_group(safe_mod)
                if not node_group:
                    continue
                
                if is_cloner_modifier(safe_mod):
                    try:
                        # Удаляем из метаданных
                        linked_effectors = list(node_group.get("linked_effectors", []))
                        if effector_name in linked_effectors:
                            linked_effectors.remove(effector_name)
                            node_group["linked_effectors"] = linked_effectors
                            print(f"[MEMORY CLEANUP] Удален {effector_name} из метаданных {safe_mod.name}")
                        
                        # БЕЗОПАСНО удаляем узлы эффектора
                        nodes_to_remove = []
                        for node in node_group.nodes:
                            try:
                                if (hasattr(node, 'name') and 
                                    "Effector" in node.name and 
                                    effector_name in node.name):
                                    nodes_to_remove.append(node)
                            except ReferenceError:
                                print(f"[MEMORY CLEANUP] Узел уже удален, пропускаем")
                                continue
                        
                        for node in nodes_to_remove:
                            if safe_remove_node(node_group, node):
                                print(f"[MEMORY CLEANUP] Безопасно удален узел эффектора из {safe_mod.name}")
                        
                        # КРИТИЧНО: Очищаем метаданные цепочки эффекторов после удаления узлов
                        if nodes_to_remove:
                            print(f"[MEMORY CLEANUP] Очищаем метаданные цепочки для {safe_mod.name}")
                            node_group["effector_chain_order"] = []
                            node_group["effector_nodes_map"] = {}
                            node_group["last_effector_index"] = 0
                            
                    except ReferenceError as ref_error:
                        print(f"[MEMORY CLEANUP] ReferenceError при очистке {safe_mod.name}: {ref_error}")
                        continue
                    except Exception as cleanup_error:
                        print(f"[MEMORY CLEANUP] Ошибка очистки {safe_mod.name}: {cleanup_error}")
                        continue
        
        # 2. Очищаем обработчики live синхронизации
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, 'effector_name') and 
                handler.effector_name == effector_name):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
            print(f"[MEMORY CLEANUP] Удален handler для {effector_name}")
        
        # 3. Также очищаем из frame_change handlers
        handlers_to_remove = []
        for handler in bpy.app.handlers.frame_change_post:
            if (hasattr(handler, 'effector_name') and 
                handler.effector_name == effector_name):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.frame_change_post.remove(handler)
        
        # 4. Принудительное обновление dependency graph
        force_clear_all_caches()
        
        # 5. КРИТИЧЕСКИ ВАЖНО: Экстренная очистка невалидных ссылок
        try:
            from .safe_operations import emergency_cleanup_invalid_references
            emergency_cleanup_invalid_references()
        except Exception as emergency_error:
            print(f"[WARNING] Ошибка экстренной очистки: {emergency_error}")
        
        print(f"[MEMORY CLEANUP] ✅ Полная очистка памяти для {effector_name} завершена")
        
    except Exception as e:
        print(f"[ERROR] Ошибка очистки памяти эффектора {effector_name}: {e}")
        traceback.print_exc()
        
        # КРИТИЧНО: При ошибке очистки выполняем экстренные меры
        try:
            from .safe_operations import emergency_cleanup_invalid_references
            emergency_cleanup_invalid_references()
            print(f"[EMERGENCY] Выполнена экстренная очистка после ошибки")
        except Exception as final_error:
            print(f"[CRITICAL] Даже экстренная очистка не удалась: {final_error}")


def is_effector_already_linked_properly(cloner_group, effector_name):
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет И метаданные И физические связи для решения проблемы Undo операций.
    После Undo приоритет отдается физическим связям, а флаги позволяют принудительную повторную привязку.
    """
    try:
        # 1. Проверяем ВСЕ флаги принудительного обновления после Undo
        undo_reset = bpy.context.scene.get('clonerpro_undo_reset', False)
        forced_refresh = bpy.context.scene.get('clonerpro_forced_refresh', False)
        metadata_reset = bpy.context.scene.get('clonerpro_metadata_reset', False)
        emergency_reset = bpy.context.scene.get('clonerpro_emergency_reset', False)
        link_refresh = bpy.context.scene.get('clonerpro_link_refresh', False)
        cache_invalidated = bpy.context.scene.get('clonerpro_cache_invalidated', False)
        
        if any([undo_reset, forced_refresh, metadata_reset, emergency_reset, link_refresh, cache_invalidated]):
            print(f"[DEBUG] Принудительное обновление включено, разрешаем повторную привязку {effector_name}")
            print(f"[DEBUG] Флаги: undo={undo_reset}, forced={forced_refresh}, meta={metadata_reset}, emergency={emergency_reset}, link={link_refresh}, cache={cache_invalidated}")
            
            # КРИТИЧНО: Сбрасываем флаги после использования
            try:
                bpy.context.scene['clonerpro_undo_reset'] = False
                bpy.context.scene['clonerpro_forced_refresh'] = False
                bpy.context.scene['clonerpro_metadata_reset'] = False
                bpy.context.scene['clonerpro_emergency_reset'] = False
                bpy.context.scene['clonerpro_link_refresh'] = False
                bpy.context.scene['clonerpro_cache_invalidated'] = False
                print(f"[DEBUG] Флаги принудительного обновления сброшены")
            except Exception as flag_error:
                print(f"[WARNING] Ошибка сброса флагов: {flag_error}")
            
            return False  # Принудительно разрешаем повторную привязку
        
        # 2. Проверяем физические узлы эффекторов
        has_physical_node = False
        for node in cloner_group.nodes:
            if "Effector" in node.name and effector_name in node.name:
                input_connected = any(socket.is_linked for socket in node.inputs)
                output_connected = any(socket.is_linked for socket in node.outputs)
                if input_connected and output_connected:
                    has_physical_node = True
                    print(f"[DEBUG] Найден подключенный физический узел для {effector_name}")
                    break
        
        # 3. Проверяем метаданные
        linked_effectors = cloner_group.get("linked_effectors", [])
        has_metadata = effector_name in linked_effectors
        
        print(f"[DEBUG] {effector_name}: физ.узел={has_physical_node}, метаданные={has_metadata}")
        
        # 4. КРИТИЧНО: Исправляем рассинхронизацию
        if has_metadata and not has_physical_node:
            print(f"[DEBUG] Обнаружена рассинхронизация: метаданные есть, физического узла нет")
            # Очищаем устаревшие метаданные
            current_linked = list(linked_effectors)
            current_linked.remove(effector_name)
            cloner_group["linked_effectors"] = current_linked
            print(f"[DEBUG] Очищены устаревшие метаданные для {effector_name}")
            
            # Очищаем ВСЕ кэши
            force_clear_all_caches()
            return False
        
        # 5. Если оба условия выполнены - эффектор действительно связан
        return has_metadata and has_physical_node
        
    except Exception as e:
        print(f"[ERROR] Ошибка проверки связи эффектора {effector_name}: {e}")
        return False


def emergency_reset_all_locks():
    """Экстренный сброс всех блокировок"""
    global _UNDO_OPERATION_IN_PROGRESS, _SYSTEM_CLEANUP_IN_PROGRESS, _EFFECTOR_OPERATION_BLOCKED
    
    print("🚨 [EMERGENCY] Сброс всех блокировок системы безопасности")
    
    old_undo = _UNDO_OPERATION_IN_PROGRESS
    old_cleanup = _SYSTEM_CLEANUP_IN_PROGRESS  
    old_blocked = _EFFECTOR_OPERATION_BLOCKED
    
    _UNDO_OPERATION_IN_PROGRESS = False
    _SYSTEM_CLEANUP_IN_PROGRESS = False
    _EFFECTOR_OPERATION_BLOCKED = False
    
    print(f"[EMERGENCY] Сброшены блокировки: undo={old_undo}, cleanup={old_cleanup}, blocked={old_blocked}")
    print("✅ [EMERGENCY] Система разблокирована")
    
    # Выполняем принудительную очистку
    try:
        emergency_system_reset()
    except Exception as e:
        print(f"[EMERGENCY] Ошибка очистки: {e}")


# ======================================================================
# РЕГИСТРАЦИЯ/ОТМЕНА РЕГИСТРАЦИИ ОБРАБОТЧИКОВ
# ======================================================================

def register_dependency_safety_handlers():
    """Регистрирует обработчики безопасности dependency graph."""
    print("[DEPENDENCY SAFETY] Регистрация обработчиков безопасности dependency graph")
    
    if undo_pre_safety_handler not in bpy.app.handlers.undo_pre:
        bpy.app.handlers.undo_pre.append(undo_pre_safety_handler)

    if undo_post_safety_handler not in bpy.app.handlers.undo_post:
        bpy.app.handlers.undo_post.append(undo_post_safety_handler)

    if depsgraph_update_post_handler not in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.append(depsgraph_update_post_handler)

    if on_load_post_handler not in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.append(on_load_post_handler)
    
    print("[DEPENDENCY SAFETY] Обработчики безопасности dependency graph зарегистрированы")


def unregister_dependency_safety_handlers():
    """Отменяет регистрацию обработчиков безопасности dependency graph."""
    print("[DEPENDENCY SAFETY] Отмена регистрации обработчиков безопасности dependency graph")
    
    if undo_pre_safety_handler in bpy.app.handlers.undo_pre:
        bpy.app.handlers.undo_pre.remove(undo_pre_safety_handler)

    if undo_post_safety_handler in bpy.app.handlers.undo_post:
        bpy.app.handlers.undo_post.remove(undo_post_safety_handler)

    if depsgraph_update_post_handler in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(depsgraph_update_post_handler)

    if on_load_post_handler in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.remove(on_load_post_handler)
    
    print("[DEPENDENCY SAFETY] Обработчики безопасности dependency graph отменены")


# ======================================================================
# ПУБЛИЧНЫЙ API
# ======================================================================

__all__ = [
    # Основные функции координации
    'diagnose_effector_cloner_relationships',
    'repair_all_effector_cloner_relationships', 
    'emergency_repair_system',
    
    # КРИТИЧЕСКИЕ НОВЫЕ ФУНКЦИИ
    'is_effector_already_linked_properly',
    'complete_effector_memory_cleanup',
    
    # Функции проверки зависимостей
    'is_effector_newer_than_cloner',
    'is_cloner_modifier',
    'is_effector_modifier',
    'is_node_properly_connected',
    'check_if_effector_chain_needs_rebuild_enhanced',
    'extract_effector_name_from_node',
    'synchronize_metadata_with_physical_links_enhanced',
    
    # Функции обработки зависимостей
    'create_safe_dependency_handler',
    'temporarily_disconnect_safe_proxies',
    'rebuild_effector_chain_improved',
    
    # Функции восстановления
    'complete_effector_memory_reset_on_undo',
    'aggressive_cache_cleanup',
    'force_clear_all_caches',
    'global_effector_cloner_sync',
    'final_system_cleanup_and_update',
    'emergency_system_reset',
    'force_refresh_all_link_checks',
    'full_reset_addon_state',
    
    # Критические обработчики событий
    'undo_pre_safety_handler',
    'undo_post_safety_handler',
    'depsgraph_update_post_handler',
    'on_load_post_handler',
    'register_dependency_safety_handlers',
    'unregister_dependency_safety_handlers'
]