# Справочник API ClonerPro

## Обзор API

ClonerPro предоставляет **единый API** для создания и управления всех типов клонеров с автоматической маршрутизацией между unified и mesh системами. Основная философия: **один API для всех клонеров**.

## Единый API (рекомендуемый)

### core.templates.cloner_creation - Единая точка входа

#### `create_cloner_unified(cloner_type, mode, source, use_anti_recursion=True, mesh_stacked=False)`

**Главная функция для создания ВСЕХ типов клонеров с автоматической маршрутизацией.**

**Параметры:**
- `cloner_type` (str): Тип клонера ("GRID", "LINEAR", "CIRCLE", "OBJECT", "SPLINE")
- `mode` (str): Режим создания ("OBJECT", "STACKED", "COLLECTION") 
- `source` (Object/str): Исходный объект или имя коллекции
- `use_anti_recursion` (bool, optional): Применять анти-рекурсию. По умолчанию True
- `mesh_stacked` (bool, optional): Для mesh клонеров - прямое создание. По умолчанию False

**Возвращает:**
- Object: Созданный объект клонера (режим OBJECT)
- Modifier: Созданный модификатор (режим STACKED)  
- None: При ошибке

**Примеры:**
```python
from core.templates.cloner_creation import create_cloner_unified

# Grid клонер (unified система автоматически)
grid_cloner = create_cloner_unified("GRID", "OBJECT", source_obj)

# Object клонер (mesh система автоматически)  
object_cloner = create_cloner_unified("OBJECT", "STACKED", surface_obj)

# Linear клонер в Collection режиме
linear_cloner = create_cloner_unified("LINEAR", "COLLECTION", "MyCollection")
```

## Основные модули API

### core.core - Центральная логика

#### Константы

```python
# Типы клонеров
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE", "SPIRAL"}
MESH_CLONERS = {"OBJECT", "CURVES", "VOLUME", "SPLINE"}

# Режимы создания
CREATION_MODES = {
    'OBJECT': 'object',     # Новый объект клонера
    'STACKED': 'stacked',   # Модификатор на объекте
    'COLLECTION': 'collection'  # Клонирование коллекций
}

# Ограничения безопасности
SAFETY_LIMITS = {
    'max_recursion_depth': 32,
    'max_cloner_chain_length': 10,
    'max_effectors_per_cloner': 20
}
```

#### Основные функции

##### `get_cloner_system(cloner_type: str) -> str`
Определяет к какой системе относится клонер.

**Параметры:**
- `cloner_type` (str): Тип клонера ("GRID", "OBJECT", etc.)

**Возвращает:**
- str: "unified", "mesh" или "unknown"

**Пример:**
```python
from core.core import get_cloner_system

system = get_cloner_system("GRID")     # "unified"
system = get_cloner_system("OBJECT")   # "mesh"
system = get_cloner_system("INVALID")  # "unknown"
```

##### `get_cloner_modifier(obj) -> bpy.types.Modifier | None`
Получает модификатор клонера из объекта.

**Параметры:**
- `obj` (bpy.types.Object): Blender объект

**Возвращает:**
- bpy.types.Modifier или None: Модификатор клонера или None если не найден

**Пример:**
```python
from core.core import get_cloner_modifier

modifier = get_cloner_modifier(bpy.context.active_object)
if modifier:
    print(f"Найден клонер: {modifier.get('cloner_type')}")
```

##### `set_cloner_metadata(modifier, cloner_type: str, mode: str, **kwargs)`
Устанавливает метаданные клонера с UUID поддержкой.

**Параметры:**
- `modifier` (bpy.types.Modifier): Модификатор клонера
- `cloner_type` (str): Тип клонера
- `mode` (str): Режим создания
- `**kwargs`: Дополнительные метаданные

**Ключевые kwargs:**
- `original_object` (str): Имя исходного объекта
- `target_object` (str): Имя целевого объекта
- `cloner_collection` (str): Имя коллекции клонера
- `chain_uuid` (str): UUID цепочки
- `use_uuid` (bool): Использовать UUID систему (по умолчанию True)

**Пример:**
```python
from core.core import set_cloner_metadata

set_cloner_metadata(
    modifier,
    "GRID",
    "OBJECT",
    original_object="Cube",
    cloner_collection="CLONERS_Cube"
)
```

##### `get_cloner_info(modifier) -> dict`
Извлекает информацию о клонере из метаданных.

**Параметры:**
- `modifier` (bpy.types.Modifier): Модификатор клонера

**Возвращает:**
- dict: Словарь с информацией о клонере

**Структура возвращаемого словаря:**
```python
{
    "type": str,              # Тип клонера
    "mode": str,              # Режим создания
    "system": str,            # Система (unified/mesh)
    "has_uuid": bool,         # Наличие UUID
    "cloner_uuid": str,       # UUID клонера
    "chain_uuid": str,        # UUID цепочки
    "is_chained": bool,       # Является ли частью цепочки
    "original_object": str,   # Исходный объект
    # ... другие поля
}
```

**Пример:**
```python
from core.core import get_cloner_info

info = get_cloner_info(modifier)
print(f"Клонер: {info['type']} в режиме {info['mode']}")
if info['has_uuid']:
    print(f"UUID: {info['cloner_uuid']}")
```

##### `is_valid_object(obj) -> bool`
Проверяет, подходит ли объект для клонирования.

**Параметры:**
- `obj` (bpy.types.Object): Blender объект

**Возвращает:**
- bool: True если объект подходит для клонирования

##### `load_cloner_config(cloner_type: str)`
Загружает конфигурацию клонера.

**Параметры:**
- `cloner_type` (str): Тип клонера

**Возвращает:**
- module: Модуль с конфигурацией или None

### core.templates.unified_creation - Unified система (DEPRECATED)

> **⚠️ DEPRECATED**: Используйте `core.templates.cloner_creation.create_cloner_unified()` для единого API

#### `create_cloner_unified(cloner_type, mode, source, use_anti_recursion=True, name_suffix="")`
~~Главная функция создания unified клонеров.~~ **УСТАРЕЛО**

**Параметры:**
- `cloner_type` (str): Тип клонера из UNIFIED_CLONERS
- `mode` (str): Режим создания ("OBJECT", "STACKED", "COLLECTION")
- `source` (bpy.types.Object | str): Исходный объект или имя коллекции
- `use_anti_recursion` (bool): Применить anti-recursion защиту
- `name_suffix` (str): Суффикс для уникальности имён

**Возвращает:**
- bpy.types.Object | bpy.types.Modifier: Созданный объект клонера или модификатор

**Пример:**
```python
from core.templates.unified_creation import create_cloner_unified

# Object режим - создание нового объекта
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=bpy.context.active_object
)

# Stacked режим - модификатор на объекте
linear_modifier = create_cloner_unified(
    cloner_type="LINEAR", 
    mode="STACKED",
    source=bpy.context.active_object
)

# Collection режим - клонирование коллекций
circle_cloner = create_cloner_unified(
    cloner_type="CIRCLE",
    mode="COLLECTION", 
    source="MyCollection"
)
```

#### CLONER_LOGIC_REGISTRY
Реестр всех unified клонеров.

```python
CLONER_LOGIC_REGISTRY = {
    "GRID": "create_grid_cloner_logic_group",
    "LINEAR": "create_linear_cloner_logic_group",
    "CIRCLE": "create_circle_cloner_logic_group"
}
```

### core.templates.mesh_creation - Mesh система (DEPRECATED)

> **⚠️ DEPRECATED**: Используйте `core.templates.cloner_creation.create_cloner_unified()` для единого API

#### `create_mesh_cloner(cloner_type, mode, target_obj=None, collection_name=None, **kwargs)`
~~Главная функция создания mesh клонеров.~~ **УСТАРЕЛО**

**Параметры:**
- `cloner_type` (str): Тип клонера из MESH_CLONERS
- `mode` (str): Режим создания ("OBJECT", "STACKED", "COLLECTION")
- `target_obj` (bpy.types.Object): Целевой объект (поверхность для клонирования)
- `collection_name` (str): Имя коллекции для COLLECTION режима
- `**kwargs`: Дополнительные параметры

**Возвращает:**
- bpy.types.Modifier | bpy.types.Object: Модификатор или объект клонера

**Пример:**
```python
from core.templates.mesh_creation import create_mesh_cloner

# Stacked режим - типичный для mesh клонеров
object_modifier = create_mesh_cloner(
    cloner_type="OBJECT",
    mode="STACKED",
    target_obj=surface_object
)

# Collection режим
object_modifier = create_mesh_cloner(
    cloner_type="OBJECT",
    mode="COLLECTION", 
    target_obj=surface_object,
    collection_name="MyCollection"
)
```

#### MESH_CLONER_REGISTRY
Реестр всех mesh клонеров.

```python
MESH_CLONER_REGISTRY = {
    "OBJECT": "create_object_cloner_logic_group",
    "SPLINE": "create_spline_cloner_logic_group"
}
```

### core.uuid.manager - UUID система

#### BlenderClonerUUIDManager

##### `BlenderClonerUUIDManager.generate_cloner_uuid() -> str`
Генерирует UUID для клонера.

**Возвращает:**
- str: Уникальный UUID

##### `BlenderClonerUUIDManager.set_cloner_uuid_metadata(modifier, cloner_type, mode, **kwargs)`
Устанавливает UUID метаданные в модификатор.

**Параметры:**
- `modifier` (bpy.types.Modifier): Модификатор клонера
- `cloner_type` (str): Тип клонера
- `mode` (str): Режим создания
- `**kwargs`: Дополнительные параметры

##### `BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid: str) -> Tuple[Object, Modifier]`
Находит клонер по UUID.

**Параметры:**
- `cloner_uuid` (str): UUID клонера

**Возвращает:**
- Tuple[bpy.types.Object, bpy.types.Modifier]: Объект и модификатор или (None, None)

**Пример:**
```python
from core.uuid.manager import BlenderClonerUUIDManager

obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(
    "12345678-1234-5678-9abc-123456789def"
)

if obj:
    print(f"Найден клонер: {obj.name}")
```

##### `BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid: str) -> List[Tuple]`
Находит всех клонеров в цепочке.

**Параметры:**
- `chain_uuid` (str): UUID цепочки

**Возвращает:**
- List[Tuple[Object, Modifier]]: Список пар (объект, модификатор)

##### `BlenderClonerUUIDManager.scan_all_cloners_with_uuid() -> Dict`
Сканирует все клонеры в сцене.

**Возвращает:**
- Dict: Словарь с результатами сканирования

**Структура результата:**
```python
{
    "cloners_by_uuid": dict,     # UUID -> (obj, modifier)
    "chains_by_uuid": dict,      # chain_uuid -> [(obj, modifier), ...]
    "legacy_cloners": list,      # [(obj, modifier), ...] без UUID
    "orphaned_cloners": list,    # [(obj, modifier), ...] со сломанными связями
    "session_uid_cloners": dict  # session_uid -> (obj, modifier)
}
```

## BaseCloner архитектура

### components.base_cloner.BaseCloner - Базовый класс

#### Класс `BaseCloner(BaseComponent)`

**Базовый класс для всех клонеров ClonerPro. Обеспечивает единообразное создание и функциональность.**

#### Основные методы

##### `create_node_group(self, mode="OBJECT") -> Tuple[NodeGroup, Dict]`

**Единый метод создания node group для всех клонеров с встроенной анти-рекурсией.**

**Параметры:**
- `mode` (str): Режим создания ("OBJECT", "STACKED", "COLLECTION")

**Возвращает:**
- Tuple[bpy.types.NodeGroup, Dict]: Node group и socket mapping

**Пример:**
```python
# НЕ вызывайте напрямую - используйте create_cloner_unified()
from components.cloners.grid import grid_cloner
node_group, socket_mapping = grid_cloner.create_node_group("OBJECT")
```

##### `get_specific_sockets(self) -> List[Tuple]`

**Переопределяется в дочерних классах для добавления специфичных сокетов.**

**Возвращает:**
- List[Tuple]: [(name, socket_type, in_out, default_value), ...]

**Пример:**
```python
class GridCloner(BaseCloner):
    def get_specific_sockets(self):
        return [
            ("Count X", "NodeSocketInt", "INPUT", 3),
            ("Count Y", "NodeSocketInt", "INPUT", 3),
            ("Spacing", "NodeSocketFloat", "INPUT", 2.0)
        ]
```

##### `_create_cloner_logic(self, base_nodes, mode) -> NodeSocket`

**ДОЛЖНО БЫТЬ ПЕРЕОПРЕДЕЛЕНО в дочерних классах. Создает специфичную логику клонера.**

**Параметры:**
- `base_nodes` (Dict): Словарь с базовыми нодами
- `mode` (str): Режим создания

**Возвращает:**
- NodeSocket: Финальный выход геометрии перед анти-рекурсией

#### Встроенная функциональность BaseCloner

##### Базовые сокеты (автоматически для всех клонеров)
```python
# Входы
"Geometry"           # Входная геометрия
"Instance Source"    # Объект для инстансирования
"Collection"         # Коллекция для клонирования

# Трансформации
"Instance Scale"     # Масштаб инстансов
"Instance Rotation"  # Поворот инстансов

# Рандомизация
"Random Position"    # Случайные позиции
"Random Rotation"    # Случайные повороты  
"Random Scale"       # Случайный масштаб
"Random Seed"        # Сид рандома

# Глобальные трансформации
"Global Position"    # Глобальная позиция
"Global Rotation"    # Глобальный поворот

# Анти-рекурсия
"Realize Instances"  # Переключатель анти-рекурсии

# Выход
"Geometry"           # Результирующая геометрия
```

##### Встроенные методы BaseCloner
- `get_geometry_input()` - Правильный ввод геометрии для режима
- `apply_instance_transforms()` - Применение базовых трансформаций
- `apply_random_transforms()` - Применение случайных трансформаций
- `apply_global_transforms()` - Применение глобальных трансформаций
- `apply_anti_recursion()` - Встроенная защита от рекурсии

### Реализованные клонеры

#### Grid клонер
```python
from components.cloners.grid import grid_cloner
# grid_cloner.bl_idname = "GRID"
# Автоматически найден через create_cloner_unified("GRID", ...)
```

#### Linear клонер  
```python
from components.cloners.linear import linear_cloner
# linear_cloner.bl_idname = "LINEAR"
```

#### Circle клонер
```python  
from components.cloners.circle import circle_cloner
# circle_cloner.bl_idname = "CIRCLE"
```

#### Object клонер
```python
from components.cloners.object import object_cloner
# object_cloner.bl_idname = "OBJECT"
```

#### Spline клонер
```python
from components.cloners.spline import spline_cloner
# spline_cloner.bl_idname = "SPLINE"
```

**Пример:**
```python
result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()

print(f"UUID клонеры: {len(result['cloners_by_uuid'])}")
print(f"Цепочки: {len(result['chains_by_uuid'])}")
print(f"Legacy клонеры: {len(result['legacy_cloners'])}")

# Обход всех цепочек
for chain_uuid, cloners in result['chains_by_uuid'].items():
    print(f"Цепочка {chain_uuid}:")
    for obj, modifier in cloners:
        print(f"  - {obj.name}")
```

### core.chain.detection - Chain Management

#### `setup_cloner_chain_links_only(context, new_cloner, source_cloner, source_collection)`
Автоматическое связывание клонеров в цепочки.

**Параметры:**
- `context` (bpy.types.Context): Blender контекст
- `new_cloner` (bpy.types.Object): Новый клонер
- `source_cloner` (bpy.types.Object): Исходный клонер (может быть None)
- `source_collection` (bpy.types.Collection): Исходная коллекция (может быть None)

**Пример:**
```python
from core.chain.detection import setup_cloner_chain_links_only

# Автоматическое связывание при создании клонера
setup_cloner_chain_links_only(
    bpy.context,
    new_cloner_obj,
    source_cloner_obj,
    None
)
```

### core.managers.effector_creation - Эффекторы

#### `create_effector(effector_type, target_cloner, **kwargs)`
Создание и применение эффектора к клонеру.

**Параметры:**
- `effector_type` (str): Тип эффектора ("RANDOM", "NOISE", etc.)
- `target_cloner` (bpy.types.Object): Целевой клонер
- `**kwargs`: Параметры эффектора

**Пример:**
```python
from core.managers.effector_creation import create_effector

# Создание Random эффектора
random_effector = create_effector(
    effector_type="RANDOM",
    target_cloner=grid_cloner,
    strength=0.5,
    seed=42
)
```

#### EFFECTOR_REGISTRY
Реестр всех эффекторов.

```python
EFFECTOR_REGISTRY = {
    "RANDOM": "create_random_effector_logic_group",
    "NOISE": "create_noise_effector_logic_group"
}
```

## Конфигурационные модули

### config.cloners.*_config - Параметры клонеров

Каждый клонер имеет свой конфигурационный файл с параметрами:

#### Структура конфигурации
```python
# config/cloners/grid_config.py

GRID_CLONER_PARAMETERS = {
    "Count X": {
        "type": "INT",
        "default": 3,
        "min": 1,
        "max": 100,
        "description": "Количество копий по оси X"
    },
    "Count Y": {
        "type": "INT", 
        "default": 3,
        "min": 1,
        "max": 100,
        "description": "Количество копий по оси Y"
    },
    "Spacing": {
        "type": "FLOAT",
        "default": 2.0,
        "min": 0.1,
        "max": 100.0,
        "description": "Расстояние между копиями"
    }
}

def get_grid_cloner_parameters():
    """Возвращает параметры Grid клонера"""
    return GRID_CLONER_PARAMETERS
```

## UI API

### ui.operators.creation - Операторы создания

#### Базовые операторы клонеров

```python
# Операторы для unified клонеров
CLONERPRO_OT_CreateGridCloner
CLONERPRO_OT_CreateLinearCloner
CLONERPRO_OT_CreateCircleCloner

# Операторы для mesh клонеров
CLONERPRO_OT_CreateObjectCloner
CLONERPRO_OT_CreateSplineCloner
```

#### Пример оператора
```python
class CLONERPRO_OT_CreateGridCloner(bpy.types.Operator):
    bl_idname = "clonerpro.create_grid_cloner"
    bl_label = "Create Grid Cloner"
    bl_options = {'REGISTER', 'UNDO'}
    
    mode: bpy.props.EnumProperty(
        items=[
            ('OBJECT', 'Object', 'Create new cloner object'),
            ('STACKED', 'Stacked', 'Add modifier to current object'),
            ('COLLECTION', 'Collection', 'Clone collections')
        ],
        default='OBJECT'
    )
    
    def execute(self, context):
        # Реализация создания клонера
        pass
```

### ui.panels - Панели интерфейса

#### CLONERPRO_PT_ClonersPanel
Основная панель клонеров в 3D Viewport.

**Расположение:** 3D Viewport > Sidebar (N) > ClonerPro > Cloners

#### CLONERPRO_PT_EffectorsPanel  
Панель эффекторов.

**Расположение:** 3D Viewport > Sidebar (N) > ClonerPro > Effectors

#### CLONERPRO_PT_UUIDBrowserPanel
Браузер клонеров с UUID навигацией.

**Расположение:** 3D Viewport > Sidebar (N) > ClonerPro > Browser

## Утилиты и вспомогательные функции

### core.managers.object_creation

#### `create_cloner_collection(context, source, cloner_type) -> bpy.types.Collection`
Создаёт коллекцию для клонера.

**Параметры:**
- `context` (bpy.types.Context): Blender контекст
- `source` (bpy.types.Object | Collection): Исходный объект или коллекция
- `cloner_type` (str): Тип клонера

#### `setup_collection_visibility(context, collection)`
Настраивает видимость коллекции клонера.

#### `create_object_copy_for_cloning(context, source_obj) -> bpy.types.Object`
Создаёт копию объекта для клонирования.

### core.system.validation

#### `validate_cloner_parameters(cloner_type, parameters) -> bool`
Валидирует параметры клонера.

#### `check_recursion_safety(source_obj, target_obj) -> bool`
Проверяет безопасность от рекурсии.

## Обработка ошибок

### Исключения ClonerPro

```python
class ClonerProError(Exception):
    """Базовое исключение ClonerPro"""
    pass

class InvalidClonerTypeError(ClonerProError):
    """Недопустимый тип клонера"""
    pass

class RecursionDetectedError(ClonerProError):
    """Обнаружена рекурсия"""
    pass

class UUIDNotFoundError(ClonerProError):
    """UUID не найден"""
    pass
```

### Обработка ошибок в API

```python
try:
    cloner = create_cloner_unified("INVALID_TYPE", "OBJECT", source)
except InvalidClonerTypeError as e:
    print(f"Ошибка типа клонера: {e}")
except RecursionDetectedError as e:
    print(f"Обнаружена рекурсия: {e}")
except Exception as e:
    print(f"Общая ошибка: {e}")
```

## Примеры использования API

### Создание простого клонера

```python
import bpy
from core.templates.unified_creation import create_cloner_unified

# Создание исходного объекта
bpy.ops.mesh.primitive_cube_add()
source_cube = bpy.context.active_object

# Создание Grid клонера
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT", 
    source=source_cube
)

print(f"Создан клонер: {grid_cloner.name}")
```

### Создание цепочки клонеров

```python
from core.templates.unified_creation import create_cloner_unified
from core.templates.mesh_creation import create_mesh_cloner

# 1. Исходный объект
bpy.ops.mesh.primitive_cube_add()
original = bpy.context.active_object

# 2. Grid клонер
grid_cloner = create_cloner_unified("GRID", "OBJECT", original)

# 3. Object клонер от Grid клонера (автоматическая цепочка)
object_modifier = create_mesh_cloner("OBJECT", "STACKED", grid_cloner)

# 4. Linear клонер от Object клонера
linear_cloner = create_cloner_unified("LINEAR", "OBJECT", grid_cloner)

print("Цепочка клонеров создана")
```

### Работа с UUID системой

```python
from core.uuid.manager import BlenderClonerUUIDManager
from core.core import get_cloner_modifier, get_cloner_info

# Получение UUID активного клонера
obj = bpy.context.active_object
modifier = get_cloner_modifier(obj)

if modifier:
    info = get_cloner_info(modifier)
    cloner_uuid = info.get('cloner_uuid')
    
    if cloner_uuid:
        print(f"UUID клонера: {cloner_uuid}")
        
        # Поиск всех клонеров в цепочке
        chain_uuid = info.get('chain_uuid')
        if chain_uuid:
            chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
            print(f"Клонеров в цепочке: {len(chain_cloners)}")
```

### Создание и применение эффектора

```python
from core.managers.effector_creation import create_effector

# Создание клонера
grid_cloner = create_cloner_unified("GRID", "OBJECT", source_cube)

# Применение Random эффектора
random_effector = create_effector(
    effector_type="RANDOM",
    target_cloner=grid_cloner,
    position_strength=0.5,
    rotation_strength=30.0,
    scale_strength=0.2,
    seed=42
)

print("Random эффектор применён")
```

### Диагностика и отладка

```python
from core.uuid.manager import BlenderClonerUUIDManager
from core.core import get_cloner_info

# Полное сканирование сцены
result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()

print("=== Диагностика ClonerPro ===")
print(f"UUID клонеры: {len(result['cloners_by_uuid'])}")
print(f"Legacy клонеры: {len(result['legacy_cloners'])}")
print(f"Цепочки: {len(result['chains_by_uuid'])}")
print(f"Сломанные связи: {len(result['orphaned_cloners'])}")

# Детальная информация о каждом клонере
for uuid, (obj, modifier) in result['cloners_by_uuid'].items():
    info = get_cloner_info(modifier)
    print(f"  {obj.name}: {info['type']} ({info['mode']})")
```

Этот API обеспечивает полный контроль над функциональностью ClonerPro и позволяет создавать сложные сценарии клонирования с минимальным кодом.