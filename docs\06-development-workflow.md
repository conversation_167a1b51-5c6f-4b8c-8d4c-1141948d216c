# Рабочий процесс разработки ClonerPro

## Введение в разработку

ClonerPro спроектирован для лёгкого расширения и модификации. Этот документ описывает процессы разработки, тестирования и внесения изменений в аддон.

## Настройка среды разработки

### Требования для разработчиков

#### Минимальные требования
- **Blender**: 4.0+ (рекомендуется 4.1+)
- **Python**: 3.10+ (встроенный в Blender)
- **Git**: Для версионного контроля
- **IDE**: VS Code, PyCharm или любой Python IDE
- **Blender Development Tools**: Для отладки

#### Рекомендуемые инструменты
- **Blender Development addon** - Для hot reload
- **Python debugger** - pdb или IDE debugger
- **Geometry Nodes Tester** - Для тестирования node groups
- **Scene complexity analyzer** - Для performance testing

### Установка для разработки

#### 1. Клонирование проекта
```bash
# Клонирование репозитория
git clone https://github.com/your-repo/ClonerPro.git
cd ClonerPro

# Создание ветки для разработки
git checkout -b feature/new-cloner-type
```

#### 2. Символическая ссылка в Blender
```bash
# Linux/macOS
ln -s /path/to/ClonerPro ~/.config/blender/4.0/scripts/addons/ClonerPro

# Windows (PowerShell как администратор)
New-Item -ItemType SymbolicLink -Path "$env:APPDATA\Blender Foundation\Blender\4.0\scripts\addons\ClonerPro" -Target "C:\path\to\ClonerPro"
```

#### 3. Настройка IDE

**VS Code с Python extension**:
```json
// .vscode/settings.json
{
    "python.defaultInterpreter": "/path/to/blender/python/bin/python",
    "python.analysis.extraPaths": [
        "/path/to/blender/scripts/modules",
        "/path/to/blender/scripts/addons"
    ],
    "files.associations": {
        "*.py": "python"
    }
}
```

**Blender Console настройки**:
```python
# В Blender Python Console для быстрой перезагрузки
import importlib
import sys

def reload_clonerpro():
    """Быстрая перезагрузка ClonerPro для разработки"""
    modules_to_reload = [name for name in sys.modules.keys() if name.startswith('ClonerPro')]
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            importlib.reload(sys.modules[module_name])
    
    # Перерегистрация аддона
    import addon_utils
    addon_utils.disable("ClonerPro")
    addon_utils.enable("ClonerPro")
    print("✅ ClonerPro reloaded")

# Быстрый вызов: reload_clonerpro()
```

## Архитектурные принципы разработки

### Принцип разделения систем

**КРИТИЧЕСКИ ВАЖНО**: Всегда соблюдайте разделение между unified и mesh системами.

```python
# ✅ ПРАВИЛЬНО - соблюдение разделения
def add_new_unified_cloner():
    # 1. Добавляем в UNIFIED_CLONERS
    # 2. Создаём компонент в components/cloners/
    # 3. Регистрируем в CLONER_LOGIC_REGISTRY
    # 4. Используем unified_creation.py
    pass

def add_new_mesh_cloner():
    # 1. Добавляем в MESH_CLONERS
    # 2. Создаём компонент в components/cloners/
    # 3. Регистрируем в MESH_CLONER_REGISTRY  
    # 4. Используем mesh_creation.py
    pass

# ❌ НЕПРАВИЛЬНО - смешивание систем
def create_mixed_cloner():
    # НЕ используйте unified функции для mesh клонеров!
    pass
```

### Принцип единого API

```python
# ✅ ВСЕГДА используйте единый API для всех клонеров
from core.templates.cloner_creation import create_cloner_unified

# Единый API автоматически выберет unified/mesh систему
any_cloner = create_cloner_unified(cloner_type, mode, source)

# Дополнительные API (если нужны)
from core.core import set_cloner_metadata, get_cloner_info

# ❌ НЕ используйте старые разделенные API
from core.templates.unified_creation import create_cloner_unified  # DEPRECATED
from core.templates.mesh_creation import create_mesh_cloner  # DEPRECATED

# ❌ НЕ вызывайте BaseCloner классы напрямую  
from components.cloners.grid import grid_cloner  # НЕ ДЕЛАЙТЕ ТАК
```

### Принцип UUID-first

```python
# ✅ Всегда используйте UUID систему
def create_any_cloner():
    # UUID метаданные устанавливаются автоматически через set_cloner_metadata()
    set_cloner_metadata(modifier, cloner_type, mode, original_object=obj.name)

# ❌ НЕ отключайте UUID систему
def broken_cloner():
    set_cloner_metadata(modifier, cloner_type, mode, use_uuid=False)  # НЕ ДЕЛАЙТЕ ТАК
```

## Добавление новых компонентов

### Добавление нового клонера (BaseCloner архитектура)

#### Шаг 1: Создание BaseCloner наследника
```python
# components/cloners/spiral.py
from ..base_cloner import BaseCloner
import bpy

class SpiralCloner(BaseCloner):
    """Spiral клонер для спиральных распределений"""
    
    bl_idname = "SPIRAL"
    
    def get_specific_sockets(self):
        """Специфичные сокеты Spiral клонера (добавляются к базовым)"""
        return [
            ("Count", "NodeSocketInt", "INPUT", 20),
            ("Turns", "NodeSocketFloat", "INPUT", 3.0),
            ("Height", "NodeSocketFloat", "INPUT", 5.0),
            ("Radius", "NodeSocketFloat", "INPUT", 2.0),
        ]
    
    def _create_cloner_logic(self, base_nodes, mode):
        """Специфичная логика Spiral клонера"""
        # Получение правильного входа геометрии (Object/Stacked/Collection)
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создание спиральной логики
        spiral_instances = self._create_spiral_distribution(base_nodes, geometry_input)
        
        # Применение стандартных трансформаций (из BaseCloner)
        transformed = self.apply_instance_transforms(base_nodes, spiral_instances)
        
        # Применение глобальных трансформаций (из BaseCloner)
        final_geometry = self.apply_global_transforms(base_nodes, transformed)
        
        return final_geometry
    
    def _create_spiral_distribution(self, base_nodes, geometry_input):
        """Создание спиральной логики"""
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Spiral distribution logic
        curve_circle = nodes.new(type='GeometryNodeCurvePrimitiveCircle')
        curve_to_mesh = nodes.new(type='GeometryNodeCurveToMesh')
        mesh_to_points = nodes.new(type='GeometryNodeMeshToPoints')
        instance_on_points = nodes.new(type='GeometryNodeInstanceOnPoints')
        
        # Настройка спиральных параметров
        # Связь с сокетами: Count, Turns, Height, Radius
        links.new(group_input.outputs['Count'], mesh_to_points.inputs['Count'])
        links.new(group_input.outputs['Radius'], curve_circle.inputs['Radius'])
        # ... остальные связи для спиральной логики
        
        # Подключение geometry input
        links.new(geometry_input, instance_on_points.inputs['Instance'])
        
        return instance_on_points.outputs['Instances']

# Экспорт экземпляра для использования в системе
spiral_cloner = SpiralCloner()

def setup_spiral_interface(node_group):
    """Настройка интерфейса Spiral клонера"""
    # Входы
    node_group.interface.new_socket(name="Instance Source", in_out='INPUT', socket_type='NodeSocketObject')
    node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
    node_group.interface.new_socket(name="Turns", in_out='INPUT', socket_type='NodeSocketFloat')
    node_group.interface.new_socket(name="Height", in_out='INPUT', socket_type='NodeSocketFloat')
    node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
    
    # Выход
    node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

def setup_spiral_nodes(node_group):
    """Создание geometry nodes для спиральной логики"""
    nodes = node_group.nodes
    links = node_group.links
    
    # Создание основных нодов
    input_node = nodes.new(type='NodeGroupInput')
    output_node = nodes.new(type='NodeGroupOutput')
    
    # Spiral distribution logic
    curve_circle = nodes.new(type='GeometryNodeCurvePrimitiveCircle')
    curve_to_mesh = nodes.new(type='GeometryNodeCurveToMesh')
    mesh_to_points = nodes.new(type='GeometryNodeMeshToPoints')
    instance_on_points = nodes.new(type='GeometryNodeInstanceOnPoints')
    
    # Настройка параметров и связей
    # ... детальная настройка geometry nodes
    
    # Связывание нодов
    links.new(input_node.outputs['Instance Source'], instance_on_points.inputs['Instance'])
    # ... остальные связи
    links.new(instance_on_points.outputs['Instances'], output_node.inputs['Geometry'])
```

#### Шаг 2: Добавление в константы системы
```python
# В core/core.py добавить в константы
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE", "SPIRAL"}  # Добавить SPIRAL

# ИЛИ для mesh клонера
MESH_CLONERS = {"OBJECT", "SPLINE", "VOLUME"}  # Добавить VOLUME
```

#### Шаг 3: Создание конфигурации (опционально)
```python
# config/cloners/spiral_config.py

SPIRAL_CLONER_PARAMETERS = {
    "Count": {
        "type": "INT",
        "default": 20,
        "min": 1,
        "max": 1000,
        "description": "Количество инстансов в спирали"
    },
    "Turns": {
        "type": "FLOAT", 
        "default": 3.0,
        "min": 0.1,
        "max": 20.0,
        "description": "Количество оборотов спирали"
    },
    "Height": {
        "type": "FLOAT",
        "default": 5.0,
        "min": 0.0,
        "max": 100.0,
        "description": "Высота спирали"
    },
    "Radius": {
        "type": "FLOAT",
        "default": 2.0, 
        "min": 0.1,
        "max": 50.0,
        "description": "Радиус спирали"
    }
}

def get_spiral_cloner_parameters():
    """Возвращает параметры Spiral клонера"""
    return SPIRAL_CLONER_PARAMETERS
```

#### Шаг 4: Автоматическое обнаружение
```python
# ✅ НИКАКИХ ИЗМЕНЕНИЙ В КОДЕ НЕ ТРЕБУЕТСЯ!
# Система автоматически найдет SpiralCloner по bl_idname

# Единый API автоматически найдет и использует новый клонер
spiral_result = create_cloner_unified("SPIRAL", "OBJECT", source)

# Система:
# 1. Ищет классы наследники BaseCloner с bl_idname="SPIRAL" 
# 2. Находит SpiralCloner в components/cloners/spiral.py
# 3. Создает экземпляр и вызывает create_node_group()
# 4. SpiralCloner._create_cloner_logic() создает специфичную логику
# 5. BaseCloner.apply_anti_recursion() добавляет защиту
```

#### Шаг 5: Тестирование
```python
# Простой тест нового клонера
import bpy
from core.templates.cloner_creation import create_cloner_unified

# Создание тестового объекта
bpy.ops.mesh.primitive_cube_add()
test_cube = bpy.context.active_object

# Создание Spiral клонера через единый API
spiral_cloner = create_cloner_unified("SPIRAL", "OBJECT", test_cube)

if spiral_cloner:
    print("✅ Spiral клонер создан успешно!")
    # Проверка параметров
    modifier = spiral_cloner.modifiers[0] if spiral_cloner.modifiers else None
    if modifier and modifier.node_group:
        print(f"✅ Node group: {modifier.node_group.name}")
        print(f"✅ Сокеты: {[s.name for s in modifier.node_group.interface.items_tree]}")
else:
    print("❌ Ошибка создания Spiral клонера")
```

#### Шаг 5: Добавление в UI
```python
# В ui/operators/creation.py
class CLONERPRO_OT_CreateSpiralCloner(bpy.types.Operator):
    """Оператор создания Spiral клонера"""
    bl_idname = "clonerpro.create_spiral_cloner"
    bl_label = "Create Spiral Cloner"
    bl_description = "Create a Spiral cloner from selected object"
    bl_options = {'REGISTER', 'UNDO'}
    
    mode: bpy.props.EnumProperty(
        items=[
            ('OBJECT', 'Object', 'Create new cloner object'),
            ('STACKED', 'Stacked', 'Add modifier to current object'),
            ('COLLECTION', 'Collection', 'Clone collections')
        ],
        default='OBJECT'
    )
    
    def execute(self, context):
        from core.templates.unified_creation import create_cloner_unified
        
        if not context.active_object:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}
        
        try:
            result = create_cloner_unified(
                cloner_type="SPIRAL",
                mode=self.mode,
                source=context.active_object
            )
            
            if result:
                self.report({'INFO'}, f"Spiral Cloner created in {self.mode} mode")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create Spiral Cloner")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating Spiral Cloner: {str(e)}")
            return {'CANCELLED'}

# Регистрация в UI панели будет автоматической через генераторы
```

### Добавление нового mesh клонера

#### Шаг 1: Создание компонента
```python
# components/cloners/volume.py
import bpy

def create_volume_cloner_logic_group():
    """Создание logic для Volume клонера"""
    
    # Создание geometry nodes группы
    node_group = bpy.data.node_groups.new(name="VolumeCloner", type='GeometryNodeTree')
    
    # Настройка интерфейса
    setup_volume_interface(node_group)
    
    # Создание нодов для volume distribution
    setup_volume_nodes(node_group)
    
    # Socket mapping для параметров
    socket_mapping = {
        "target_object": "Target_Object_Socket_ID",
        "density": "Density_Socket_ID",
        "distribution_mode": "Distribution_Mode_Socket_ID"
    }
    
    return node_group, socket_mapping

def setup_volume_interface(node_group):
    """Настройка интерфейса Volume клонера"""
    # Входы
    node_group.interface.new_socket(name="Target Object", in_out='INPUT', socket_type='NodeSocketObject')
    node_group.interface.new_socket(name="Instance Source", in_out='INPUT', socket_type='NodeSocketObject')
    node_group.interface.new_socket(name="Density", in_out='INPUT', socket_type='NodeSocketFloat')
    node_group.interface.new_socket(name="Distribution Mode", in_out='INPUT', socket_type='NodeSocketInt')
    
    # Выход
    node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

def setup_volume_nodes(node_group):
    """Создание geometry nodes для volume logic"""
    # Реализация volume-based distribution
    # ... детальная настройка для клонирования по объёму
    pass
```

#### Шаг 2: Регистрация mesh клонера
```python
# В core/templates/mesh_creation.py

# Добавляем в реестр mesh клонеров
MESH_CLONER_REGISTRY = {
    "OBJECT": "create_object_cloner_logic_group",
    "SPLINE": "create_spline_cloner_logic_group",
    "VOLUME": "create_volume_cloner_logic_group"  # ← ДОБАВИТЬ
}

# Добавляем в функцию маршрутизации
def _get_mesh_cloner_logic_function(cloner_type):
    # ... существующие клонеры ...
    
    elif cloner_type == "VOLUME":  # ← ДОБАВИТЬ
        from ...components.cloners.volume import create_volume_cloner_logic_group
        return create_volume_cloner_logic_group
```

#### Шаг 3: Обновление констант
```python
# В core/core.py
MESH_CLONERS = {"OBJECT", "CURVES", "VOLUME", "SPLINE"}  # ← ДОБАВИТЬ VOLUME
```

### Добавление нового эффектора

#### Шаг 1: Создание компонента
```python
# components/effectors/step.py
import bpy

def create_step_effector_logic_group(name_suffix=""):
    """Создание geometry nodes группы для Step эффектора"""
    
    group_name = f"StepEffector{name_suffix}"
    node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')
    
    # Настройка интерфейса
    setup_step_interface(node_group)
    
    # Создание нодов
    setup_step_nodes(node_group)
    
    return node_group

def setup_step_interface(node_group):
    """Настройка интерфейса Step эффектора"""
    # Входы
    node_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    node_group.interface.new_socket(name="Step Size", in_out='INPUT', socket_type='NodeSocketFloat')
    node_group.interface.new_socket(name="Direction", in_out='INPUT', socket_type='NodeSocketVector')
    node_group.interface.new_socket(name="Falloff", in_out='INPUT', socket_type='NodeSocketFloat')
    
    # Выход
    node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

def setup_step_nodes(node_group):
    """Создание geometry nodes для step logic"""
    # Реализация ступенчатых изменений
    # ... детальная настройка
    pass
```

#### Шаг 2: Регистрация эффектора
```python
# В core/managers/effector_creation.py

EFFECTOR_REGISTRY = {
    "RANDOM": "create_random_effector_logic_group",
    "NOISE": "create_noise_effector_logic_group", 
    "STEP": "create_step_effector_logic_group"  # ← ДОБАВИТЬ
}
```

## Процесс тестирования

### Unit тестирование

#### Создание тестов
```python
# tests/test_unified_creation.py
import unittest
import bpy
from core.templates.unified_creation import create_cloner_unified

class TestUnifiedCreation(unittest.TestCase):
    
    def setUp(self):
        """Подготовка для каждого теста"""
        # Очистка сцены
        bpy.ops.object.select_all(action='SELECT')
        bpy.ops.object.delete()
        
        # Создание тестового объекта
        bpy.ops.mesh.primitive_cube_add()
        self.test_cube = bpy.context.active_object
    
    def test_grid_cloner_object_mode(self):
        """Тест создания Grid клонера в Object режиме"""
        result = create_cloner_unified("GRID", "OBJECT", self.test_cube)
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(result, 'modifiers'))
        self.assertTrue(len(result.modifiers) > 0)
        
        # Проверка метаданных
        modifier = result.modifiers[0]
        self.assertEqual(modifier.get("cloner_type"), "GRID")
        self.assertEqual(modifier.get("cloner_mode"), "OBJECT")
    
    def test_linear_cloner_stacked_mode(self):
        """Тест создания Linear клонера в Stacked режиме"""
        result = create_cloner_unified("LINEAR", "STACKED", self.test_cube)
        
        self.assertIsNotNone(result)
        # result должен быть модификатором, а не объектом
        self.assertEqual(result.type, 'NODES')
        self.assertEqual(result.get("cloner_type"), "LINEAR")
    
    def tearDown(self):
        """Очистка после каждого теста"""
        # Очистка созданных node groups
        for ng in bpy.data.node_groups:
            if "Cloner" in ng.name:
                bpy.data.node_groups.remove(ng)

if __name__ == '__main__':
    unittest.main()
```

#### Запуск тестов
```python
# В Blender Python Console
import sys
sys.path.append('/path/to/ClonerPro/tests')

import unittest
from test_unified_creation import TestUnifiedCreation

# Запуск конкретного теста
suite = unittest.TestLoader().loadTestsFromTestCase(TestUnifiedCreation)
runner = unittest.TextTestRunner(verbosity=2)
runner.run(suite)
```

### Интеграционные тесты

#### Тестирование цепочек клонеров
```python
# tests/test_chain_management.py
import unittest
import bpy
from core.templates.unified_creation import create_cloner_unified
from core.templates.mesh_creation import create_mesh_cloner
from core.uuid.manager import BlenderClonerUUIDManager

class TestChainManagement(unittest.TestCase):
    
    def test_automatic_chain_detection(self):
        """Тест автоматического обнаружения цепочек"""
        # Создание исходного объекта
        bpy.ops.mesh.primitive_cube_add()
        original = bpy.context.active_object
        
        # Создание Grid клонера
        grid_cloner = create_cloner_unified("GRID", "OBJECT", original)
        
        # Создание Object клонера от Grid клонера (должна создаться цепочка)
        object_modifier = create_mesh_cloner("OBJECT", "STACKED", grid_cloner)
        
        # Проверка что цепочка создалась
        result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        self.assertGreater(len(result['chains_by_uuid']), 0)
        
        # Проверка связей
        grid_modifier = grid_cloner.modifiers[0]
        chain_uuid = grid_modifier.get("chain_uuid")
        self.assertEqual(object_modifier.get("chain_uuid"), chain_uuid)
```

### Performance тестирование

#### Тест производительности
```python
# tests/test_performance.py
import time
import bpy
from core.templates.unified_creation import create_cloner_unified

def test_cloner_creation_performance():
    """Тест скорости создания клонеров"""
    
    # Подготовка
    bpy.ops.mesh.primitive_cube_add()
    source = bpy.context.active_object
    
    # Измерение времени создания
    start_time = time.time()
    
    for i in range(10):
        cloner = create_cloner_unified("GRID", "OBJECT", source)
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / 10
    
    print(f"Среднее время создания клонера: {avg_time:.3f} сек")
    
    # Проверка что время разумное (< 1 сек на клонер)
    assert avg_time < 1.0, f"Создание клонера слишком медленное: {avg_time:.3f} сек"

def test_large_instance_count_performance():
    """Тест производительности с большим количеством инстансов"""
    
    bpy.ops.mesh.primitive_cube_add()
    source = bpy.context.active_object
    
    cloner = create_cloner_unified("GRID", "OBJECT", source)
    modifier = cloner.modifiers[0]
    
    # Установка большого количества инстансов
    for socket in modifier.node_group.interface.items_tree:
        if socket.name == "Count X":
            modifier[socket.identifier] = 100
        elif socket.name == "Count Y": 
            modifier[socket.identifier] = 100
    
    # Измерение времени viewport update
    start_time = time.time()
    bpy.context.view_layer.update()
    end_time = time.time()
    
    update_time = end_time - start_time
    print(f"Время обновления 10k инстансов: {update_time:.3f} сек")
    
    # Проверка что обновление быстрое (< 5 сек)
    assert update_time < 5.0, f"Обновление слишком медленное: {update_time:.3f} сек"
```

## Отладка и диагностика

### Включение debug режима

```python
# В Blender Python Console или в коде
import bpy

# Включение debug режима
bpy.context.scene.clonerpro_debug_mode = True

# Verbose логирование
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Diagnostic функции

```python
# utils/debug_helpers.py
def diagnose_cloner_state(obj):
    """Полная диагностика состояния клонера"""
    from core.core import get_cloner_modifier, get_cloner_info
    
    print(f"🔍 Диагностика объекта: {obj.name}")
    
    # Проверка модификатора
    modifier = get_cloner_modifier(obj)
    if not modifier:
        print("❌ Объект не является клонером")
        return
    
    # Информация о клонере
    info = get_cloner_info(modifier)
    print(f"✅ Клонер найден:")
    print(f"  - Тип: {info['type']}")
    print(f"  - Режим: {info['mode']}")
    print(f"  - Система: {info['system']}")
    
    # UUID информация
    if info.get('has_uuid'):
        print(f"  - UUID: {info['cloner_uuid']}")
        print(f"  - Chain UUID: {info.get('chain_uuid', 'НЕТ')}")
    else:
        print("  - UUID: НЕТ (legacy клонер)")
    
    # Node group информация
    if modifier.node_group:
        print(f"  - Node Group: {modifier.node_group.name}")
        print(f"  - Входных сокетов: {len([s for s in modifier.node_group.interface.items_tree if s.in_out == 'INPUT'])}")
    
    # Параметры
    print("  - Параметры:")
    for socket in modifier.node_group.interface.items_tree:
        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
            try:
                value = modifier[socket.identifier]
                print(f"    • {socket.name}: {value}")
            except:
                print(f"    • {socket.name}: <не установлено>")

def scan_scene_cloners():
    """Сканирование всех клонеров в сцене"""
    from core.uuid.manager import BlenderClonerUUIDManager
    
    result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
    
    print(f"🔍 Сканирование сцены:")
    print(f"  - UUID клонеры: {len(result['cloners_by_uuid'])}")
    print(f"  - Цепочки: {len(result['chains_by_uuid'])}")
    print(f"  - Legacy клонеры: {len(result['legacy_cloners'])}")
    print(f"  - Сломанные связи: {len(result['orphaned_cloners'])}")
    
    # Детальная информация по цепочкам
    for chain_uuid, cloners in result['chains_by_uuid'].items():
        print(f"  📎 Цепочка {chain_uuid[:8]}...:")
        for obj, modifier in cloners:
            info = get_cloner_info(modifier)
            print(f"    → {obj.name} ({info['type']})")

def validate_cloner_integrity(obj):
    """Валидация целостности клонера"""
    from core.core import get_cloner_modifier
    
    modifier = get_cloner_modifier(obj)
    if not modifier:
        return False, "Не является клонером"
    
    issues = []
    
    # Проверка node group
    if not modifier.node_group:
        issues.append("Отсутствует node group")
    
    # Проверка UUID
    if not modifier.get("cloner_uuid"):
        issues.append("Отсутствует cloner_uuid")
    
    # Проверка метаданных
    required_metadata = ["cloner_type", "cloner_mode"]
    for key in required_metadata:
        if not modifier.get(key):
            issues.append(f"Отсутствует {key}")
    
    # Проверка цепочек
    chain_uuid = modifier.get("chain_uuid")
    if chain_uuid:
        # Валидация связей в цепочке
        prev_uuid = modifier.get("previous_cloner_uuid")
        if prev_uuid:
            from core.uuid.manager import BlenderClonerUUIDManager
            prev_obj, prev_mod = BlenderClonerUUIDManager.find_cloner_by_uuid(prev_uuid)
            if not prev_obj:
                issues.append(f"Сломанная связь с предыдущим клонером: {prev_uuid}")
    
    if issues:
        return False, "; ".join(issues)
    else:
        return True, "Клонер в порядке"
```

### Профилирование производительности

```python
# utils/performance_profiler.py
import cProfile
import pstats
import time

def profile_cloner_creation(cloner_type, mode, iterations=10):
    """Профилирование создания клонеров"""
    
    def create_test_cloners():
        import bpy
        from core.templates.unified_creation import create_cloner_unified
        
        for i in range(iterations):
            # Создание тестового объекта
            bpy.ops.mesh.primitive_cube_add(location=(i*3, 0, 0))
            source = bpy.context.active_object
            
            # Создание клонера
            cloner = create_cloner_unified(cloner_type, mode, source)
    
    # Профилирование
    profiler = cProfile.Profile()
    profiler.enable()
    
    start_time = time.time()
    create_test_cloners()
    end_time = time.time()
    
    profiler.disable()
    
    # Анализ результатов
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)  # Топ 20 функций
    
    total_time = end_time - start_time
    avg_time = total_time / iterations
    
    print(f"\n📊 Результаты профилирования:")
    print(f"Общее время: {total_time:.3f} сек")
    print(f"Среднее время на клонер: {avg_time:.3f} сек")
    print(f"Клонеров в секунду: {1/avg_time:.1f}")

def benchmark_viewport_performance():
    """Бенчмарк производительности viewport"""
    import bpy
    
    # Создание клонера с большим количеством инстансов
    bpy.ops.mesh.primitive_cube_add()
    source = bpy.context.active_object
    
    from core.templates.unified_creation import create_cloner_unified
    cloner = create_cloner_unified("GRID", "OBJECT", source)
    
    modifier = cloner.modifiers[0]
    
    # Тестирование разных количеств инстансов
    counts = [10, 50, 100, 500, 1000]
    
    for count in counts:
        # Установка количества
        for socket in modifier.node_group.interface.items_tree:
            if socket.name in ["Count X", "Count Y"]:
                modifier[socket.identifier] = int(count**0.5)  # квадратная сетка
        
        # Измерение времени обновления
        start_time = time.time()
        bpy.context.view_layer.update()
        bpy.context.view_layer.update()  # Двойное обновление для стабильности
        end_time = time.time()
        
        update_time = end_time - start_time
        print(f"{count:4d} инстансов: {update_time:.3f} сек ({count/update_time:.0f} inst/sec)")
```

## Релизный процесс

### Подготовка к релизу

#### 1. Проверка готовности
```bash
# Запуск всех тестов
python -m pytest tests/ -v

# Проверка стиля кода
flake8 --max-line-length=120 ClonerPro/

# Проверка документации
# Убедиться что все новые функции задокументированы
```

#### 2. Обновление версии
```python
# В __init__.py
bl_info = {
    "name": "ClonerPro",
    "version": (1, 1, 0),  # ← Обновить версию
    # ...
}

# В core/core.py
ADDON_VERSION = "1.1.0"  # ← Обновить версию
```

#### 3. Создание changelog
```markdown
# CHANGELOG.md

## [1.1.0] - 2024-12-XX

### Добавлено
- Spiral клонер (unified система)
- Volume клонер (mesh система)
- Step эффектор
- Улучшенная производительность viewport

### Изменено
- Оптимизирована UUID система
- Улучшена стабильность chain detection

### Исправлено
- Исправлена проблема с коллекциями в Collection режиме
- Устранены memory leaks в node group создании
```

#### 4. Тестирование релиза
```python
# Финальное тестирование в чистой среде
# 1. Установка в чистый Blender
# 2. Тестирование основных сценариев
# 3. Проверка производительности
# 4. Тестирование на разных версиях Blender
```

### Git workflow

```bash
# Создание feature ветки
git checkout -b feature/spiral-cloner

# Коммиты с описательными сообщениями
git commit -m "feat: add spiral cloner basic structure"
git commit -m "feat: implement spiral geometry nodes logic"
git commit -m "feat: add spiral cloner UI integration"
git commit -m "test: add tests for spiral cloner"
git commit -m "docs: update documentation for spiral cloner"

# Merge в main
git checkout main
git merge feature/spiral-cloner

# Создание тега релиза
git tag -a v1.1.0 -m "Release version 1.1.0"
git push origin v1.1.0
```

Этот рабочий процесс обеспечивает качественную разработку и поддержание ClonerPro с минимальными рисками внесения багов и максимальной расширяемостью.