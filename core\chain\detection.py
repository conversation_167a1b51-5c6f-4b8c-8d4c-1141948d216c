"""
Автоматическое обнаружение и создание цепочек клонеров
Обновлено для поддержки UUID системы с fallback на legacy метод
"""

import bpy
from ..core import (
    get_cloner_modifier, get_cloner_info, set_cloner_metadata
)

# Упрощенное логирование - всегда включено
CHAIN_DEBUG_LEVEL = 1

# Попытка импорта UUID системы
try:
    from ..uuid.manager import BlenderClonerUUIDManager
    from ..uuid.chain_management import BlenderUUIDChainManager
    UUID_SYSTEM_AVAILABLE = True
    if CHAIN_DEBUG_LEVEL >= 1:
        print("✓ UUID Chain System: Available")
except ImportError:
    UUID_SYSTEM_AVAILABLE = False
    if CHAIN_DEBUG_LEVEL >= 1:
        print("⚠️ UUID Chain System: Not available, using legacy chain management")


def _chain_debug_print(level, message):
    """Print chain debug messages based on debug level"""
    if CHAIN_DEBUG_LEVEL >= level:
        print(message)


def detect_and_setup_cloner_chain(context, new_cloner_obj, source_obj=None, source_collection=None):
    """
    Автоматически обнаружить цепочку клонеров и настроить метаданные
    Теперь поддерживает UUID систему с fallback на legacy
    
    Args:
        context: Blender context
        new_cloner_obj: Новый созданный клонер
        source_obj: Источник объект (если есть)
        source_collection: Источник коллекция (если есть)
    """
    _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Analyzing chain for new cloner: {new_cloner_obj.name}")
    _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Source object: {source_obj.name if source_obj else 'None'}")
    _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Source collection: {source_collection.name if source_collection else 'None'}")
    
    # Получаем модификатор нового клонера
    new_modifier = get_cloner_modifier(new_cloner_obj)
    if not new_modifier:
        _chain_debug_print(1, "⚠️ [CHAIN_DETECT] No cloner modifier found")
        return
    
    new_cloner_info = get_cloner_info(new_modifier)
    
    # Используем только UUID систему
    if UUID_SYSTEM_AVAILABLE:
        _chain_debug_print(2, "🔗 [CHAIN_DETECT] Using UUID-based chain detection")
        _setup_uuid_chain(context, new_cloner_obj, new_modifier, source_obj, source_collection)
    else:
        _chain_debug_print(1, "⚠️ [CHAIN_DETECT] UUID system not available - chain detection disabled")


def _setup_uuid_chain(context, new_cloner_obj, new_modifier, source_obj, source_collection):
    """Настройка цепи используя UUID систему"""
    new_cloner_info = get_cloner_info(new_modifier)
    
    # Определяем источник для анализа цепи
    chain_source = _determine_chain_source(source_obj, source_collection, new_cloner_info)
    if not chain_source:
        _chain_debug_print(2, "✅ [CHAIN_DETECT] No chain source - standalone UUID cloner")
        return
    
    # Проверяем, является ли источник клонером с UUID
    is_source_cloner = _is_cloner_object(chain_source)
    
    if is_source_cloner:
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Source is a cloner: {chain_source.name}")
        source_modifier = get_cloner_modifier(chain_source)
        source_info = get_cloner_info(source_modifier)
        
        if source_info.get("has_uuid", False):
            # UUID клонер к UUID клонеру
            _setup_uuid_chained_cloner(new_modifier, new_cloner_info, source_modifier, source_info)
        else:
            # UUID клонер к legacy клонеру - конвертируем legacy в UUID
            _chain_debug_print(1, "🔄 [CHAIN_DETECT] Converting legacy cloner to UUID for chain")
            BlenderClonerUUIDManager.migrate_legacy_cloner(chain_source, source_modifier)
            source_info = get_cloner_info(source_modifier)  # Обновляем информацию
            _setup_uuid_chained_cloner(new_modifier, new_cloner_info, source_modifier, source_info)
    else:
        _chain_debug_print(2, f"✅ [CHAIN_DETECT] Source is original object: {chain_source.name}")
        # Первый клонер в цепи
        _setup_uuid_first_cloner_in_chain(new_modifier, new_cloner_info, chain_source)


def _setup_uuid_chained_cloner(new_modifier, new_cloner_info, source_modifier, source_info):
    """Настроить UUID клонер как часть цепи"""
    chain_uuid = source_info.get("chain_uuid", "")
    if not chain_uuid:
        # Создаем новую цепь если у источника её нет
        chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
        source_modifier["chain_uuid"] = chain_uuid
        source_modifier["chain_sequence"] = 0
    
    # Определяем последовательность в цепи
    next_sequence = source_info.get("chain_sequence", 0) + 1
    
    # Устанавливаем UUID метаданные для нового клонера
    source_uuid = source_info.get("cloner_uuid", "")
    new_uuid = new_cloner_info.get("cloner_uuid", "")
    
    # Обновляем связи
    new_modifier["chain_uuid"] = chain_uuid
    new_modifier["previous_cloner_uuid"] = source_uuid
    new_modifier["chain_sequence"] = next_sequence
    
    # ВАЖНО: Наследуем chain_source от предыдущего клонера
    new_modifier["chain_source_object"] = source_info.get("chain_source_object", "")
    new_modifier["chain_source_collection"] = source_info.get("chain_source_collection", "")
    
    # НОВОЕ: Сохраняем UUID источника для независимости от имен
    chain_source_uuid = source_info.get("chain_source_uuid", "")
    if chain_source_uuid:
        new_modifier["chain_source_uuid"] = chain_source_uuid
    
    # Обновляем связи у источника
    current_next = source_modifier.get("next_cloner_uuids", "")
    if current_next:
        source_modifier["next_cloner_uuids"] = f"{current_next},{new_uuid}"
    else:
        source_modifier["next_cloner_uuids"] = new_uuid
    
    _chain_debug_print(1, f"✅ [CHAIN_DETECT] UUID chained cloner setup complete")
    _chain_debug_print(2, f"    Chain UUID: {chain_uuid}")
    _chain_debug_print(2, f"    Previous UUID: {source_uuid}")
    _chain_debug_print(2, f"    Sequence: {next_sequence}")
    _chain_debug_print(2, f"    Chain source: {new_modifier.get('chain_source_object', '')} / {new_modifier.get('chain_source_collection', '')}")


def _get_or_create_source_uuid(source_obj):
    """Получить или создать UUID для источника (объект или коллекция)"""
    # Проверяем, есть ли уже UUID у источника
    if hasattr(source_obj, 'get'):
        existing_uuid = source_obj.get("source_uuid")
        if existing_uuid:
            return existing_uuid
    
    # Создаем новый UUID для источника
    source_uuid = BlenderClonerUUIDManager.generate_cloner_uuid()
    
    # Сохраняем UUID в объект/коллекцию
    try:
        source_obj["source_uuid"] = source_uuid
        _chain_debug_print(2, f"✅ [CHAIN_DETECT] Created source UUID {source_uuid} for {source_obj.name}")
    except Exception as e:
        _chain_debug_print(1, f"⚠️ [CHAIN_DETECT] Could not save UUID to source: {e}")
    
    return source_uuid


def _setup_uuid_first_cloner_in_chain(modifier, cloner_info, original_source):
    """Настроить UUID клонер как потенциальное начало цепи"""
    chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
    
    modifier["chain_uuid"] = chain_uuid
    modifier["chain_sequence"] = 0
    modifier["previous_cloner_uuid"] = ""
    modifier["next_cloner_uuids"] = ""
    
    # ВАЖНО: Сохраняем информацию об источнике цепи для восстановления
    if hasattr(original_source, 'name'):
        if hasattr(original_source, 'type'):  # Это объект
            modifier["chain_source_object"] = original_source.name
            # НОВОЕ: Создаем и сохраняем UUID источника
            source_uuid = _get_or_create_source_uuid(original_source)
            modifier["chain_source_uuid"] = source_uuid
            _chain_debug_print(2, f"✅ [CHAIN_DETECT] Set chain source object: {original_source.name} (UUID: {source_uuid})")
        elif hasattr(original_source, 'objects'):  # Это коллекция
            modifier["chain_source_collection"] = original_source.name
            # НОВОЕ: Создаем и сохраняем UUID источника коллекции
            source_uuid = _get_or_create_source_uuid(original_source)
            modifier["chain_source_uuid"] = source_uuid
            _chain_debug_print(2, f"✅ [CHAIN_DETECT] Set chain source collection: {original_source.name} (UUID: {source_uuid})")
    
    _chain_debug_print(1, f"✅ [CHAIN_DETECT] UUID chain start setup complete")
    _chain_debug_print(2, f"    Chain UUID: {chain_uuid}")
    _chain_debug_print(2, f"    Source: {original_source.name if hasattr(original_source, 'name') else 'Unknown'}")




def _determine_chain_source(source_obj, source_collection, cloner_info):
    """Определить источник для анализа цепи"""
    
    # Приоритет 1: Явно переданный источник
    if source_obj:
        return source_obj
    
    if source_collection:
        return source_collection
    
    # Приоритет 2: Источник из метаданных клонера
    original_obj_name = cloner_info.get("original_object", "")
    if original_obj_name and original_obj_name in bpy.data.objects:
        return bpy.data.objects[original_obj_name]
    
    # Приоритет 3: Источник коллекции из метаданных клонера
    original_collection_name = cloner_info.get("original_collection", "")
    if original_collection_name and original_collection_name in bpy.data.collections:
        return bpy.data.collections[original_collection_name]
    
    return None


def _is_cloner_object(obj):
    """Проверить является ли объект клонером"""
    if not obj:
        return False
    
    # Проверяем, что это объект, а не коллекция
    if not hasattr(obj, 'modifiers'):
        return False
    
    # Проверяем по наличию модификатора клонера
    return get_cloner_modifier(obj) is not None


def rebuild_chain_metadata_for_scene(context):
    """
    Перестроить метаданные цепей для всей сцены
    Полезно для восстановления после проблем
    """
    _chain_debug_print(1, "🔍 [CHAIN_DETECT] Rebuilding chain metadata for entire scene")
    
    # Находим все объекты клонеров
    all_cloners = []
    for obj in context.scene.objects:
        if _is_cloner_object(obj):
            all_cloners.append(obj)
    
    if not all_cloners:
        _chain_debug_print(1, "✅ [CHAIN_DETECT] No cloners found")
        return
    
    _chain_debug_print(1, f"🔍 [CHAIN_DETECT] Found {len(all_cloners)} cloners")
    
    # Группируем клонеры по возможным цепям
    # Анализируем паттерны имён и источники
    chains = _analyze_potential_chains(all_cloners)
    
    # Перестраиваем метаданные для каждой цепи
    for chain_source, chain_cloners in chains.items():
        _rebuild_chain_metadata(chain_source, chain_cloners)
    
    _chain_debug_print(1, "✅ [CHAIN_DETECT] Chain metadata rebuild complete")


def _analyze_potential_chains(cloners):
    """Анализировать потенциальные цепи клонеров"""
    chains = {}
    
    for cloner_obj in cloners:
        modifier = get_cloner_modifier(cloner_obj)
        if not modifier:
            continue
        
        cloner_info = get_cloner_info(modifier)
        
        # Определяем источник цепи
        chain_source = cloner_info.get("chain_source_object", "")
        if not chain_source:
            chain_source = cloner_info.get("chain_source_collection", "")
        
        if not chain_source:
            # Пытаемся определить по original_object
            chain_source = cloner_info.get("original_object", "")
        
        if chain_source:
            if chain_source not in chains:
                chains[chain_source] = []
            chains[chain_source].append(cloner_obj)
    
    # Сортируем каждую цепь по именам/индексам
    for chain_source in chains:
        chains[chain_source].sort(key=lambda obj: extract_cloner_index(obj.name))
    
    return chains


def _rebuild_chain_metadata(chain_source, chain_cloners):
    """Перестроить метаданные для одной цепи"""
    _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Rebuilding chain for source: {chain_source}")
    
    if len(chain_cloners) == 1:
        # Одиночный клонер
        cloner_obj = chain_cloners[0]
        modifier = get_cloner_modifier(cloner_obj)
        cloner_info = get_cloner_info(modifier)
        
        set_cloner_metadata(
            modifier,
            cloner_info.get("type", "UNKNOWN"),
            cloner_info.get("mode", "OBJECT"),
            is_chained=False,
            chain_index=0,
            chain_source_object=chain_source if chain_source in bpy.data.objects else "",
            chain_source_collection=chain_source if chain_source in bpy.data.collections else ""
        )
        return
    
    # Цепь из нескольких клонеров
    for i, cloner_obj in enumerate(chain_cloners):
        modifier = get_cloner_modifier(cloner_obj)
        cloner_info = get_cloner_info(modifier)
        
        # Определяем соседей
        previous_cloner = chain_cloners[i-1] if i > 0 else None
        next_cloners = chain_cloners[i+1:i+2]  # Только следующий
        
        set_cloner_metadata(
            modifier,
            cloner_info.get("type", "UNKNOWN"),
            cloner_info.get("mode", "OBJECT"),
            is_chained=True,
            previous_cloner=previous_cloner.name if previous_cloner else "",
            chain_index=i,
            next_cloners=next_cloners[0].name if next_cloners else "",
            chain_source_object=chain_source if chain_source in bpy.data.objects else "",
            chain_source_collection=chain_source if chain_source in bpy.data.collections else ""
        )
        
        _chain_debug_print(2, f"  ✓ Updated {cloner_obj.name} (index {i})")


# Автоматический хук для интеграции с созданием клонеров
def migrate_existing_cloners_to_uuid_sources():
    """Мигрировать существующие клонеры для добавления UUID источников"""
    print("🔄 [CHAIN_DETECT] Migrating existing cloners to UUID source system...")
    
    migrated_count = 0
    
    for obj in bpy.data.objects:
        cloner_modifier = None
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                if (mod.node_group.name.endswith("Cloner3D_Advanced") or 
                    "Cloner" in mod.node_group.name):
                    cloner_modifier = mod
                    break
        
        if cloner_modifier:
            from ...core.core import get_cloner_info
            cloner_info = get_cloner_info(cloner_modifier)
            
            # Проверяем, есть ли уже UUID источника
            if not cloner_info.get("chain_source_uuid"):
                # Получаем текущий источник клонера
                try:
                    from .chain_recovery import ChainRecoveryManager
                    current_source = ChainRecoveryManager._get_current_cloner_source(obj, cloner_modifier)
                except ImportError:
                    print("⚠️ [CHAIN_DETECT] Chain recovery not available for migration")
                    continue
                
                if current_source:
                    # Создаем или получаем UUID для источника
                    source_uuid = _get_or_create_source_uuid(current_source)
                    
                    # Сохраняем UUID в метаданных клонера
                    cloner_modifier["chain_source_uuid"] = source_uuid
                    
                    print(f"✅ [CHAIN_DETECT] Migrated {obj.name} to UUID source: {current_source.name} ({source_uuid})")
                    migrated_count += 1
    
    if migrated_count > 0:
        print(f"✅ [CHAIN_DETECT] Migration completed: {migrated_count} cloners updated")
    else:
        print("ℹ️ [CHAIN_DETECT] No cloners needed migration")


def setup_cloner_chain_links_only(context, new_cloner_obj, source_obj, source_collection):
    """
    Устанавливает только связи между клонерами в цепи БЕЗ перезаписи UUID
    Предполагается что UUID уже правильно установлены в unified_creation.py
    """
    try:
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Setting up chain links for: {new_cloner_obj.name}")
        
        # Получаем модификатор нового клонера
        new_modifier = get_cloner_modifier(new_cloner_obj)
        if not new_modifier:
            _chain_debug_print(1, "⚠️ [CHAIN_DETECT] No cloner modifier found")
            return
        
        new_cloner_info = get_cloner_info(new_modifier)
        
        # Определяем источник для анализа цепи
        chain_source = _determine_chain_source(source_obj, source_collection, new_cloner_info)
        if not chain_source:
            _chain_debug_print(2, "✅ [CHAIN_DETECT] No chain source - standalone cloner")
            return
        
        # Проверяем, является ли источник клонером (объект или коллекция от клонера)
        is_source_cloner = _is_cloner_object(chain_source)
        source_cloner_obj = None
        
        if is_source_cloner:
            source_cloner_obj = chain_source
        elif hasattr(chain_source, 'objects'):  # Это коллекция
            # Ищем клонер который создал эту коллекцию
            source_cloner_obj = _find_cloner_that_created_collection(chain_source)
            is_source_cloner = source_cloner_obj is not None
        
        if is_source_cloner and source_cloner_obj:
            _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Source is a cloner: {source_cloner_obj.name}")
            source_modifier = get_cloner_modifier(source_cloner_obj)
            source_info = get_cloner_info(source_modifier)
            
            if source_info.get("has_uuid", False):
                # Устанавливаем связи между UUID клонерами БЕЗ изменения chain_source_uuid
                _setup_uuid_chained_cloner_links_only(new_modifier, new_cloner_info, source_modifier, source_info)
            else:
                # Конвертируем legacy клонер в UUID и устанавливаем связи
                _chain_debug_print(1, "🔄 [CHAIN_DETECT] Converting legacy cloner to UUID for chain")
                BlenderClonerUUIDManager.migrate_legacy_cloner(chain_source, source_modifier)
                source_info = get_cloner_info(source_modifier)
                _setup_uuid_chained_cloner_links_only(new_modifier, new_cloner_info, source_modifier, source_info)
        else:
            _chain_debug_print(2, f"✅ [CHAIN_DETECT] Source is original object: {chain_source.name}")
            # Первый клонер в цепи - устанавливаем только chain UUID и sequence БЕЗ chain_source_uuid
            _setup_uuid_first_cloner_links_only(new_modifier, new_cloner_info)
            
    except Exception as e:
        _chain_debug_print(1, f"⚠️ [CHAIN_DETECT] Chain linking failed: {e}")


def auto_chain_setup_hook(context, new_cloner_obj, creation_params=None):
    """
    Автоматический хук для вызова после создания клонера
    
    Args:
        context: Blender context
        new_cloner_obj: Созданный клонер
        creation_params: Параметры создания (source_obj, source_collection, etc.)
    """
    try:
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Auto chain setup hook called for {new_cloner_obj.name}")
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Creation params: {creation_params}")
        
        if creation_params:
            source_obj = creation_params.get("source_obj")
            source_collection = creation_params.get("source_collection")
        else:
            source_obj = context.active_object
            source_collection = None
        
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Using source_obj: {source_obj.name if source_obj else 'None'}")
        _chain_debug_print(2, f"🔗 [CHAIN_DETECT] Using source_collection: {source_collection.name if source_collection else 'None'}")
        
        detect_and_setup_cloner_chain(context, new_cloner_obj, source_obj, source_collection)
        
    except Exception as e:
        _chain_debug_print(1, f"⚠️ [CHAIN_DETECT] Auto chain setup failed: {e}")
        import traceback
        traceback.print_exc()
        # Не прерываем создание клонера из-за ошибки в цепях


def _setup_uuid_chained_cloner_links_only(new_modifier, new_cloner_info, source_modifier, source_info):
    """
    Настроить UUID клонер как часть цепи БЕЗ изменения chain_source_uuid
    Только устанавливает связи между клонерами
    """
    chain_uuid = source_info.get("chain_uuid", "")
    if not chain_uuid:
        # Создаем новую цепь если у источника её нет
        chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
        source_modifier["chain_uuid"] = chain_uuid
        source_modifier["chain_sequence"] = 0

    source_uuid = source_info.get("cloner_uuid", "")
    next_sequence = source_info.get("chain_sequence", 0) + 1
    new_uuid = new_cloner_info.get("cloner_uuid", "")

    # Обновляем связи
    new_modifier["chain_uuid"] = chain_uuid
    new_modifier["previous_cloner_uuid"] = source_uuid
    new_modifier["chain_sequence"] = next_sequence

    # ВАЖНО: НЕ изменяем chain_source_uuid - он уже правильно установлен в unified_creation.py
    # ТОЛЬКО наследуем legacy поля для совместимости
    new_modifier["chain_source_object"] = source_info.get("chain_source_object", "")
    new_modifier["chain_source_collection"] = source_info.get("chain_source_collection", "")

    # Обновляем связи у источника
    current_next = source_modifier.get("next_cloner_uuids", "")
    if current_next:
        source_modifier["next_cloner_uuids"] = f"{current_next},{new_uuid}"
    else:
        source_modifier["next_cloner_uuids"] = new_uuid

    _chain_debug_print(1, f"✅ [CHAIN_DETECT] UUID chained cloner links setup complete")
    _chain_debug_print(2, f"    Chain UUID: {chain_uuid}")
    _chain_debug_print(2, f"    Previous UUID: {source_uuid}")
    _chain_debug_print(2, f"    Sequence: {next_sequence}")


def _setup_uuid_first_cloner_links_only(modifier, cloner_info):
    """
    Настроить UUID клонер как потенциальное начало цепи БЕЗ изменения chain_source_uuid
    Только устанавливает chain UUID и sequence
    """
    chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
    
    modifier["chain_uuid"] = chain_uuid
    modifier["chain_sequence"] = 0
    modifier["previous_cloner_uuid"] = ""
    modifier["next_cloner_uuids"] = ""
    
    # НЕ устанавливаем chain_source_uuid - он уже правильно установлен в unified_creation.py
    
    _chain_debug_print(1, f"✅ [CHAIN_DETECT] UUID chain start links setup complete")
    _chain_debug_print(2, f"    Chain UUID: {chain_uuid}")


def _find_cloner_that_created_collection(collection):
    """
    Найти объект клонера который создал данную коллекцию
    """
    if not collection:
        return None
    
    # Ищем объекты клонеров, которые могли создать эту коллекцию
    for obj in bpy.data.objects:
        cloner_modifier = get_cloner_modifier(obj)
        if cloner_modifier:
            cloner_collection_name = cloner_modifier.get("cloner_collection", "")
            if cloner_collection_name == collection.name:
                _chain_debug_print(2, f"🔍 [CHAIN_DETECT] Found cloner {obj.name} that created collection {collection.name}")
                return obj
    
    return None