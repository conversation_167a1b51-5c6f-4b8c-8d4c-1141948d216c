"""
Grid Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
from ..base_cloner import BaseCloner


class GridCloner(BaseCloner):
    """
    Grid Cloner - создает 2D/3D сетку клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "GRID"
    bl_label = "Grid Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Grid Cloner
        Заменяет старый grid_config.py
        """
        # Grid Settings
        props_owner.grid_count_x = bpy.props.IntProperty(
            name="Count X",
            description="Number of instances along X axis",
            default=3,
            min=1,
            max=100
        )
        props_owner.grid_count_y = bpy.props.IntProperty(
            name="Count Y", 
            description="Number of instances along Y axis",
            default=3,
            min=1,
            max=100
        )
        props_owner.grid_count_z = bpy.props.IntProperty(
            name="Count Z",
            description="Number of instances along Z axis", 
            default=1,
            min=1,
            max=100
        )
        props_owner.grid_spacing = bpy.props.FloatVectorProperty(
            name="Spacing",
            description="Spacing between instances",
            default=(3.0, 3.0, 3.0),
            size=3
        )
        
        # Instance Transform (базовые свойства уже определены в BaseCloner)
        # Randomization (базовые свойства уже определены в BaseCloner)
        # Global Transform (базовые свойства уже определены в BaseCloner)
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Grid Cloner
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Count X", "NodeSocketInt", "INPUT", 3),
            ("Count Y", "NodeSocketInt", "INPUT", 3), 
            ("Count Z", "NodeSocketInt", "INPUT", 1),
            ("Spacing", "NodeSocketVector", "INPUT", (3.0, 3.0, 3.0)),
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Grid Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем сетку точек
        grid_points = self._create_grid_points(base_nodes)
        
        # Применяем центрирование
        centered_points = self._apply_grid_centering(base_nodes, grid_points)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Grid"
        instance_node.location = (400, 0)
        links.new(centered_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])
        
        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instance_node.outputs['Instances'])
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_grid_points(self, base_nodes):
        """
        Создание сетки точек с 2D/3D логикой
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход с сеткой точек
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Множитель для spacing
        spacing_multiplier = nodes.new('ShaderNodeVectorMath')
        spacing_multiplier.operation = 'MULTIPLY'
        spacing_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        spacing_multiplier.location = (-500, 300)
        links.new(group_input.outputs['Spacing'], spacing_multiplier.inputs[0])
        
        # Разделяем spacing по компонентам
        separate_xyz_spacing = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz_spacing.location = (-400, 300)
        links.new(spacing_multiplier.outputs['Vector'], separate_xyz_spacing.inputs['Vector'])
        
        # Step 1: Линия X
        line_x = nodes.new('GeometryNodeMeshLine')
        line_x.name = "Line X Points"
        line_x.mode = 'OFFSET'
        line_x.count_mode = 'TOTAL'
        line_x.location = (-300, 400)
        links.new(group_input.outputs['Count X'], line_x.inputs['Count'])
        
        # Офсет для X оси
        combine_x_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_x_offset.location = (-400, 400)
        links.new(separate_xyz_spacing.outputs['X'], combine_x_offset.inputs['X'])
        combine_x_offset.inputs['Y'].default_value = 0.0
        combine_x_offset.inputs['Z'].default_value = 0.0
        links.new(combine_x_offset.outputs['Vector'], line_x.inputs['Offset'])
        
        # Step 2: Линия Y
        line_y = nodes.new('GeometryNodeMeshLine')
        line_y.name = "Line Y Points"
        line_y.mode = 'OFFSET'
        line_y.count_mode = 'TOTAL'
        line_y.location = (-300, 300)
        links.new(group_input.outputs['Count Y'], line_y.inputs['Count'])
        
        # Офсет для Y оси
        combine_y_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_y_offset.location = (-400, 300)
        combine_y_offset.inputs['X'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Y'], combine_y_offset.inputs['Y'])
        combine_y_offset.inputs['Z'].default_value = 0.0
        links.new(combine_y_offset.outputs['Vector'], line_y.inputs['Offset'])
        
        # Step 3: Инстансирование X на Y для 2D сетки
        instance_x_on_y = nodes.new('GeometryNodeInstanceOnPoints')
        instance_x_on_y.name = "Instance X on Y"
        instance_x_on_y.location = (-200, 350)
        links.new(line_y.outputs['Mesh'], instance_x_on_y.inputs['Points'])
        links.new(line_x.outputs['Mesh'], instance_x_on_y.inputs['Instance'])
        
        # Реализация 2D сетки
        realize_2d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_2d_grid.name = "Realize 2D Grid"
        realize_2d_grid.location = (-100, 350)
        links.new(instance_x_on_y.outputs['Instances'], realize_2d_grid.inputs['Geometry'])
        
        # Step 4: Линия Z
        line_z = nodes.new('GeometryNodeMeshLine')
        line_z.name = "Line Z Points"
        line_z.mode = 'OFFSET'
        line_z.count_mode = 'TOTAL'
        line_z.location = (-300, 200)
        links.new(group_input.outputs['Count Z'], line_z.inputs['Count'])
        
        # Офсет для Z оси
        combine_z_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_z_offset.location = (-400, 200)
        combine_z_offset.inputs['X'].default_value = 0.0
        combine_z_offset.inputs['Y'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Z'], combine_z_offset.inputs['Z'])
        links.new(combine_z_offset.outputs['Vector'], line_z.inputs['Offset'])
        
        # Step 5: Инстансирование 2D на Z для 3D сетки
        instance_2d_on_z = nodes.new('GeometryNodeInstanceOnPoints')
        instance_2d_on_z.name = "Instance 2D on Z"
        instance_2d_on_z.location = (0, 300)
        links.new(line_z.outputs['Mesh'], instance_2d_on_z.inputs['Points'])
        links.new(realize_2d_grid.outputs['Geometry'], instance_2d_on_z.inputs['Instance'])
        
        # Реализация 3D сетки
        realize_3d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_3d_grid.name = "Realize 3D Grid"
        realize_3d_grid.location = (100, 300)
        links.new(instance_2d_on_z.outputs['Instances'], realize_3d_grid.inputs['Geometry'])
        
        # Переключение между 2D и 3D
        compare_z_count = nodes.new('FunctionNodeCompare')
        compare_z_count.data_type = 'INT'
        compare_z_count.operation = 'GREATER_THAN'
        compare_z_count.inputs[3].default_value = 1
        compare_z_count.location = (0, 200)
        links.new(group_input.outputs['Count Z'], compare_z_count.inputs[2])
        
        switch_points = nodes.new('GeometryNodeSwitch')
        switch_points.name = "Switch 2D/3D Points"
        switch_points.input_type = 'GEOMETRY'
        switch_points.location = (200, 250)
        links.new(compare_z_count.outputs['Result'], switch_points.inputs['Switch'])
        links.new(realize_2d_grid.outputs['Geometry'], switch_points.inputs[False])
        links.new(realize_3d_grid.outputs['Geometry'], switch_points.inputs[True])
        
        # Сохраняем separate_xyz_spacing в классе для использования в centering
        self._separate_xyz_spacing = separate_xyz_spacing
        
        return switch_points.outputs['Output']
    
    def _apply_grid_centering(self, base_nodes, grid_points):
        """
        Применение центрирования к сетке
        
        Args:
            base_nodes: Словарь с базовыми нодами
            grid_points: Входной сокет с точками сетки
            
        Returns:
            NodeSocket: Выход с отцентрированной сеткой
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        separate_xyz_spacing = self._separate_xyz_spacing
        
        # Вычисляем X размер: (Count X - 1) * Spacing X
        count_x_minus_one = nodes.new('ShaderNodeMath')
        count_x_minus_one.operation = 'SUBTRACT'
        count_x_minus_one.inputs[1].default_value = 1.0
        count_x_minus_one.location = (-200, 100)
        links.new(group_input.outputs['Count X'], count_x_minus_one.inputs[0])
        
        total_size_x = nodes.new('ShaderNodeMath')
        total_size_x.operation = 'MULTIPLY'
        total_size_x.location = (-100, 100)
        links.new(count_x_minus_one.outputs['Value'], total_size_x.inputs[0])
        links.new(separate_xyz_spacing.outputs['X'], total_size_x.inputs[1])
        
        # Делим на -2 для получения половинного офсета
        half_offset_x = nodes.new('ShaderNodeMath')
        half_offset_x.operation = 'DIVIDE'
        half_offset_x.inputs[1].default_value = -2.0
        half_offset_x.location = (0, 100)
        links.new(total_size_x.outputs['Value'], half_offset_x.inputs[0])
        
        # То же самое для Y
        count_y_minus_one = nodes.new('ShaderNodeMath')
        count_y_minus_one.operation = 'SUBTRACT'
        count_y_minus_one.inputs[1].default_value = 1.0
        count_y_minus_one.location = (-200, 0)
        links.new(group_input.outputs['Count Y'], count_y_minus_one.inputs[0])
        
        total_size_y = nodes.new('ShaderNodeMath')
        total_size_y.operation = 'MULTIPLY'
        total_size_y.location = (-100, 0)
        links.new(count_y_minus_one.outputs['Value'], total_size_y.inputs[0])
        links.new(separate_xyz_spacing.outputs['Y'], total_size_y.inputs[1])
        
        half_offset_y = nodes.new('ShaderNodeMath')
        half_offset_y.operation = 'DIVIDE'
        half_offset_y.inputs[1].default_value = -2.0
        half_offset_y.location = (0, 0)
        links.new(total_size_y.outputs['Value'], half_offset_y.inputs[0])
        
        # То же самое для Z
        count_z_minus_one = nodes.new('ShaderNodeMath')
        count_z_minus_one.operation = 'SUBTRACT'
        count_z_minus_one.inputs[1].default_value = 1.0
        count_z_minus_one.location = (-200, -100)
        links.new(group_input.outputs['Count Z'], count_z_minus_one.inputs[0])
        
        total_size_z = nodes.new('ShaderNodeMath')
        total_size_z.operation = 'MULTIPLY'
        total_size_z.location = (-100, -100)
        links.new(count_z_minus_one.outputs['Value'], total_size_z.inputs[0])
        links.new(separate_xyz_spacing.outputs['Z'], total_size_z.inputs[1])
        
        half_offset_z = nodes.new('ShaderNodeMath')
        half_offset_z.operation = 'DIVIDE'
        half_offset_z.inputs[1].default_value = -2.0
        half_offset_z.location = (0, -100)
        links.new(total_size_z.outputs['Value'], half_offset_z.inputs[0])
        
        # Объединяем в вектор центрирования
        combine_center_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_center_offset.location = (100, 0)
        links.new(half_offset_x.outputs['Value'], combine_center_offset.inputs['X'])
        links.new(half_offset_y.outputs['Value'], combine_center_offset.inputs['Y'])
        links.new(half_offset_z.outputs['Value'], combine_center_offset.inputs['Z'])
        
        # Применяем центрирование
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Center Grid Points"
        set_position.location = (200, 200)
        links.new(grid_points, set_position.inputs['Geometry'])
        links.new(combine_center_offset.outputs['Vector'], set_position.inputs['Offset'])
        
        return set_position.outputs['Geometry']
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Grid Cloner
        
        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        # Grid Settings
        box = layout.box()
        box.label(text="Grid Settings", icon='GRID')
        
        row = box.row(align=True)
        row.prop(props_owner, "grid_count_x", text="X")
        row.prop(props_owner, "grid_count_y", text="Y") 
        row.prop(props_owner, "grid_count_z", text="Z")
        
        box.prop(props_owner, "grid_spacing", text="Spacing")
        
        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, modifier)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        
        # Instance Transform
        box = layout.box()
        box.label(text="Instance Transform", icon='CON_TRANSFORM')
        
        if modifier:
            # Получаем сокеты из модификатора если есть
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name in ['Instance Scale', 'Instance Rotation']:
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Randomization
        box = layout.box()
        box.label(text="Randomization", icon='RNDCURVE')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Random') or socket.name == 'Random Seed':
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Global Transform
        box = layout.box()
        box.label(text="Global Transform", icon='OBJECT_ORIGIN')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Global'):
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Grid Cloner"""
        base_defaults = super().get_default_parameters()
        grid_defaults = {
            "count_x": 3,
            "count_y": 3,
            "count_z": 1,
            "spacing": (3.0, 3.0, 3.0)
        }
        return {**base_defaults, **grid_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        grid_groups = {
            "Grid Settings": ["Count X", "Count Y", "Count Z", "Spacing"]
        }
        return {**grid_groups, **base_groups}


# Экземпляр класса для использования в других модулях
grid_cloner = GridCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Grid Cloner (в новой архитектуре не требуется)"""
    print("✅ Grid Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Grid Cloner (в новой архитектуре не требуется)"""
    print("✅ Grid Cloner: Using class-based architecture, no unregistration needed") 
    pass


