"""
Система обработки ошибок для ClonerPro
Портировано из advanced_cloners/core/utils/error_handling/error_manager.py

КРИТИЧНО: Эта система предотвращает краши при некорректных операциях
"""

import traceback
from typing import Any, Callable, Optional, Dict, List
from functools import wraps
from datetime import datetime


class ErrorManager:
    """Централизованная система обработки ошибок и логирования."""
    
    def __init__(self):
        self._error_history: List[Dict[str, Any]] = []
        self._max_history = 100
        
    # Категории ошибок для лучшей организации
    CATEGORIES = {
        'NODE_OPERATION': 'Node Operation',
        'MODIFIER_OPERATION': 'Modifier Operation', 
        'PARAMETER_OPERATION': 'Parameter Operation',
        'UI_OPERATION': 'UI Operation',
        'COMPONENT_CREATION': 'Component Creation',
        'VALIDATION': 'Validation',
        'BLENDER_INTEGRATION': 'Blender Integration'
    }
    
    def safe_execute(self, func: Callable, *args, error_context: str = "Operation", 
                    return_on_error: Any = None, log_traceback: bool = False, **kwargs) -> Any:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасное выполнение функции с комплексной обработкой ошибок.
        
        Args:
            func: Функция для выполнения
            *args: Аргументы для функции
            error_context: Описание контекста для логирования ошибок
            return_on_error: Значение для возврата при ошибке
            log_traceback: Нужно ли логировать полный traceback
            **kwargs: Ключевые аргументы для функции
            
        Returns:
            Результат функции или return_on_error при ошибке
        """
        try:
            result = func(*args, **kwargs)
            self.log_operation_result(error_context, True, f"Executed successfully")
            return result
            
        except Exception as e:
            error_details = f"{type(e).__name__}: {str(e)}"
            if log_traceback:
                error_details += f"\nTraceback:\n{traceback.format_exc()}"
            
            self.log_operation_result(error_context, False, error_details)
            self.handle_exception(e, error_context)
            return return_on_error
    
    def log_operation_result(self, operation_name: str, success: bool, details: Optional[str] = None):
        """
        Логирует результат операции с единообразным форматированием.
        
        Args:
            operation_name: Имя/описание операции
            success: Успешно ли выполнена операция
            details: Дополнительные детали о результате
        """
        message = operation_name
        if details:
            message += f": {details}"
        
        # Записываем в историю
        error_entry = {
            'timestamp': datetime.now(),
            'operation': operation_name,
            'success': success,
            'details': details
        }
        self._error_history.append(error_entry)
        
        # Оставляем только недавние записи
        if len(self._error_history) > self._max_history:
            self._error_history = self._error_history[-self._max_history:]
        
        # Логируем
        if success:
            print(f"[SUCCESS] Operation completed: {message}")
        else:
            print(f"[ERROR] Operation failed: {message}")
    
    def handle_exception(self, exception: Exception, context: str = "Unknown"):
        """
        Обрабатывает исключения с правильным логированием и категоризацией.
        
        Args:
            exception: Произошедшее исключение
            context: Контекст, где произошло исключение
        """
        error_message = f"Exception in {context}: {type(exception).__name__}: {str(exception)}"
        
        # Добавляем в историю ошибок
        error_entry = {
            'timestamp': datetime.now(),
            'operation': context,
            'success': False,
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc()
        }
        self._error_history.append(error_entry)
        
        # Оставляем только недавние записи
        if len(self._error_history) > self._max_history:
            self._error_history = self._error_history[-self._max_history:]
        
        # Логируем исключение
        print(f"[EXCEPTION] {error_message}")
        print(f"[TRACEBACK] {traceback.format_exc()}")
    
    def get_error_history(self, category: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        Получает историю ошибок с опциональной фильтрацией.
        
        Args:
            category: Опциональная категория для фильтрации
            limit: Опциональный лимит количества записей
            
        Returns:
            Список записей истории ошибок
        """
        history = self._error_history.copy()
        
        if category:
            history = [entry for entry in history if category.lower() in entry.get('operation', '').lower()]
        
        if limit:
            history = history[-limit:]
            
        return history
    
    def clear_error_history(self):
        """Очищает историю ошибок"""
        self._error_history.clear()
        print("[INFO] Error history cleared")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """
        Получает статистику ошибок для анализа.
        
        Returns:
            Словарь со статистикой ошибок
        """
        total_entries = len(self._error_history)
        if total_entries == 0:
            return {'total': 0, 'success_rate': 1.0, 'categories': {}}
        
        successful = sum(1 for entry in self._error_history if entry.get('success', False))
        failed = total_entries - successful
        
        # Категоризируем ошибки
        categories = {}
        for entry in self._error_history:
            if not entry.get('success', False):
                operation = entry.get('operation', 'unknown')
                # Простая категоризация по имени операции
                for category_key, category_name in self.CATEGORIES.items():
                    if category_key.lower().replace('_', ' ') in operation.lower():
                        if category_name not in categories:
                            categories[category_name] = 0
                        categories[category_name] += 1
                        break
                else:
                    if 'Other' not in categories:
                        categories['Other'] = 0
                    categories['Other'] += 1
        
        return {
            'total': total_entries,
            'successful': successful,
            'failed': failed,
            'success_rate': successful / total_entries if total_entries > 0 else 1.0,
            'categories': categories
        }
    
    def handle_modifier_error(self, modifier_name: str, error: Exception, 
                            context: str = "modifier operation") -> None:
        """
        Обрабатывает ошибки, специфичные для модификаторов, с контекстом.
        
        Args:
            modifier_name: Имя модификатора
            error: Произошедшее исключение
            context: Контекст операции
        """
        error_msg = f"Modifier '{modifier_name}' {context} failed: {type(error).__name__}: {str(error)}"
        self.log_operation_result(f"Modifier {context}", False, error_msg)
        self.handle_exception(error, f"Modifier {context}")
    
    def handle_component_error(self, component_type: str, component_id: str, 
                             error: Exception, context: str = "operation") -> None:
        """
        Обрабатывает ошибки, специфичные для компонентов, с контекстом.
        
        Args:
            component_type: Тип компонента (CLONER, EFFECTOR, FIELD)
            component_id: ID компонента
            error: Произошедшее исключение
            context: Контекст операции
        """
        error_msg = f"{component_type} '{component_id}' {context} failed: {type(error).__name__}: {str(error)}"
        self.log_operation_result(f"{component_type} {context}", False, error_msg)
        self.handle_exception(error, f"{component_type} {context}")
    
    def handle_parameter_error(self, parameter_name: str, error: Exception, 
                             context: str = "parameter operation") -> None:
        """
        Обрабатывает ошибки, специфичные для параметров, с контекстом.
        
        Args:
            parameter_name: Имя параметра
            error: Произошедшее исключение
            context: Контекст операции
        """
        error_msg = f"Parameter '{parameter_name}' {context} failed: {type(error).__name__}: {str(error)}"
        self.log_operation_result(f"Parameter {context}", False, error_msg)
        self.handle_exception(error, f"Parameter {context}")
    
    def validate_and_execute(self, validation_func: Callable, operation_func: Callable,
                           validation_context: str = "Validation",
                           operation_context: str = "Operation",
                           *args, **kwargs) -> Any:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Валидирует входы перед выполнением операции.
        
        Args:
            validation_func: Функция для валидации входов (должна возвращать bool)
            operation_func: Функция для выполнения, если валидация прошла
            validation_context: Контекст для логирования валидации
            operation_context: Контекст для логирования операции
            *args: Аргументы для обеих функций
            **kwargs: Ключевые аргументы для обеих функций
            
        Returns:
            Результат операции или None если валидация не прошла
        """
        # Запускаем валидацию
        is_valid = self.safe_execute(
            validation_func, *args, 
            error_context=validation_context,
            return_on_error=False,
            **kwargs
        )
        
        if not is_valid:
            self.log_operation_result(validation_context, False, "Validation failed")
            return None
        
        # Запускаем операцию если валидация прошла
        return self.safe_execute(
            operation_func, *args,
            error_context=operation_context,
            return_on_error=None,
            **kwargs
        )
    
    def create_error_context_decorator(self, context_name: str, log_traceback: bool = False):
        """
        Создает декоратор для единообразной обработки ошибок в определенных контекстах.
        
        Args:
            context_name: Имя контекста для логирования ошибок
            log_traceback: Нужно ли логировать полный traceback при ошибках
            
        Returns:
            Функция-декоратор
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                return self.safe_execute(
                    func, *args,
                    error_context=f"{context_name}: {func.__name__}",
                    log_traceback=log_traceback,
                    **kwargs
                )
            return wrapper
        return decorator
    
    def collect_operation_errors(self) -> Dict[str, list]:
        """
        Собирает и категоризирует недавние ошибки операций для отладки.
        
        Returns:
            Словарь категорий ошибок со списками ошибок
        """
        categorized_errors = {
            'node_operations': [],
            'modifier_operations': [],
            'parameter_operations': [],
            'ui_operations': [],
            'component_creation': [],
            'validation_errors': [],
            'blender_integration': []
        }
        
        # Категоризируем реальные ошибки из истории
        for error_entry in self._error_history:
            if not error_entry.get('success', True):
                operation = error_entry.get('operation', '').lower()
                
                # Категоризируем по имени операции
                if 'node' in operation:
                    categorized_errors['node_operations'].append(error_entry)
                elif 'modifier' in operation:
                    categorized_errors['modifier_operations'].append(error_entry)
                elif 'parameter' in operation:
                    categorized_errors['parameter_operations'].append(error_entry)
                elif 'ui' in operation or 'interface' in operation:
                    categorized_errors['ui_operations'].append(error_entry)
                elif 'component' in operation or 'creation' in operation:
                    categorized_errors['component_creation'].append(error_entry)
                elif 'validation' in operation:
                    categorized_errors['validation_errors'].append(error_entry)
                elif 'blender' in operation:
                    categorized_errors['blender_integration'].append(error_entry)
        
        return categorized_errors


# Глобальный экземпляр менеджера ошибок
_error_manager: Optional[ErrorManager] = None


def get_error_manager() -> ErrorManager:
    """Получает глобальный экземпляр менеджера ошибок"""
    global _error_manager
    if _error_manager is None:
        _error_manager = ErrorManager()
    return _error_manager


# Удобные функции и декораторы для общих контекстов ошибок
def create_error_context_decorator(context_name: str, log_traceback: bool = False):
    """Создает декоратор, используя глобальный менеджер ошибок"""
    return get_error_manager().create_error_context_decorator(context_name, log_traceback)


# Удобные декораторы для общих контекстов ошибок
node_operation_safe = create_error_context_decorator("Node Operation")
modifier_operation_safe = create_error_context_decorator("Modifier Operation")
parameter_operation_safe = create_error_context_decorator("Parameter Operation")
ui_operation_safe = create_error_context_decorator("UI Operation")
component_creation_safe = create_error_context_decorator("Component Creation", log_traceback=True)


# Функции для безопасного выполнения критических операций
def safe_execute_operation(func: Callable, *args, context: str = "Operation", **kwargs) -> Any:
    """Безопасно выполняет операцию используя глобальный менеджер ошибок"""
    return get_error_manager().safe_execute(func, *args, error_context=context, **kwargs)


def safe_execute_with_fallback(func: Callable, fallback_value: Any, *args, context: str = "Operation", **kwargs) -> Any:
    """Безопасно выполняет операцию с fallback значением"""
    return get_error_manager().safe_execute(func, *args, error_context=context, return_on_error=fallback_value, **kwargs)


def handle_critical_exception(exception: Exception, context: str = "Critical Operation"):
    """Обрабатывает критические исключения"""
    get_error_manager().handle_exception(exception, context)


def get_recent_errors(limit: int = 10) -> List[Dict[str, Any]]:
    """Получает недавние ошибки для отладки"""
    return get_error_manager().get_error_history(limit=limit)


def clear_error_history():
    """Очищает историю ошибок"""
    get_error_manager().clear_error_history()


def get_error_statistics() -> Dict[str, Any]:
    """Получает статистику ошибок"""
    return get_error_manager().get_error_statistics()


# Публичный API
__all__ = [
    'ErrorManager',
    'get_error_manager',
    'create_error_context_decorator',
    'node_operation_safe',
    'modifier_operation_safe', 
    'parameter_operation_safe',
    'ui_operation_safe',
    'component_creation_safe',
    'safe_execute_operation',
    'safe_execute_with_fallback',
    'handle_critical_exception',
    'get_recent_errors',
    'clear_error_history',
    'get_error_statistics'
]