# Установка и настройка ClonerPro

## Системные требования

### Минимальные требования
- **Blender**: 4.0.0 или выше
- **Python**: 3.10+ (встроенный в Blender)
- **Операционная система**: Windows 10/11, macOS 12+, Linux Ubuntu 20.04+
- **Память**: 8 GB RAM (рекомендуется 16 GB)
- **Место на диске**: 50 MB для аддона

### Рекомендуемые требования
- **Blender**: 4.1+ для лучшей совместимости с Geometry Nodes
- **Память**: 16 GB RAM для работы с большими сценами
- **GPU**: Поддержка OpenGL 4.3+ для viewport отображения
- **Место на диске**: 200 MB с кэшем node groups

## Установка аддона

### Метод 1: Установка через Blender UI

1. **Скачивание**
   - Получите архив `ClonerPro.zip` (не распаковывайте)
   
2. **Открытие Blender**
   - Запустите Blender 4.0+
   - Откройте `Edit > Preferences` (Ctrl+Alt+U)

3. **Установка аддона**
   - Перейдите в раздел `Add-ons`
   - Нажмите `Install...`
   - Выберите файл `ClonerPro.zip`
   - Нажмите `Install Add-on`

4. **Активация**
   - Найдите "ClonerPro" в списке аддонов
   - Поставьте галочку для активации
   - Нажмите `Save Preferences`

### Метод 2: Ручная установка

1. **Определение директории аддонов**
   ```
   Windows: %APPDATA%\Blender Foundation\Blender\4.0\scripts\addons\
   macOS: ~/Library/Application Support/Blender/4.0/scripts/addons/
   Linux: ~/.config/blender/4.0/scripts/addons/
   ```

2. **Распаковка**
   - Распакуйте `ClonerPro.zip`
   - Скопируйте папку `ClonerPro` в директорию аддонов

3. **Перезапуск и активация**
   - Перезапустите Blender
   - Активируйте аддон в Preferences

### Метод 3: Установка для разработчиков

```bash
# Клонирование репозитория
git clone https://github.com/your-repo/ClonerPro.git

# Создание символической ссылки (Linux/macOS)
ln -s /path/to/ClonerPro ~/.config/blender/4.0/scripts/addons/ClonerPro

# Для Windows (PowerShell как администратор)
New-Item -ItemType SymbolicLink -Path "$env:APPDATA\Blender Foundation\Blender\4.0\scripts\addons\ClonerPro" -Target "C:\path\to\ClonerPro"
```

## Первая настройка

### Проверка установки

1. **Проверка панели**
   - Откройте 3D Viewport
   - Нажмите `N` для открытия боковой панели
   - Найдите вкладку `ClonerPro`

2. **Проверка работы**
   - Создайте объект (например, куб)
   - В панели ClonerPro нажмите `Grid Cloner`
   - Должен появиться клонированный объект

### Базовые настройки

#### Настройки производительности
```python
# В панели ClonerPro найдите "Settings"
Max Instances: 10000        # Максимум инстансов на клонер
Auto Update: True          # Автообновление при изменениях
Debug Mode: False          # Отладочный режим (только для разработки)
Anti Recursion: True       # Защита от рекурсии (всегда включено)
```

#### Настройки UI
```python
# Фильтры отображения
Show Unified Cloners: True    # Показывать unified клонеры (Grid, Linear, Circle)
Show Mesh Cloners: True       # Показывать mesh клонеры (Object, Spline)
Show Advanced: False          # Показывать расширенные опции
```

### Настройка коллекций

ClonerPro автоматически создаёт структуру коллекций:

```
Scene Collection
├── CLONERS_OriginalObjectName/  # Unified клонеры
│   ├── GridCloner3D_Advanced_001
│   ├── LinearCloner3D_Advanced_001
│   └── ...
├── ClonerTo_ObjectName_TYPE/    # Mesh клонеры
│   ├── ObjectName_OBJECT_Cloner
│   └── ...
└── ClonerTo/                    # Основная коллекция для копий
    ├── OriginalObject_Copy
    └── ...
```

## Базовое использование

### Создание первого клонера

#### Grid клонер (Unified система через единый API)
1. **Подготовка**
   ```python
   # Создайте исходный объект
   bpy.ops.mesh.primitive_cube_add()
   source_cube = bpy.context.active_object
   ```

2. **Создание клонера**
   - Выберите объект
   - В панели ClonerPro нажмите `Grid Cloner`
   - Выберите режим: `Object` (новый объект) или `Stacked` (модификатор)
   - Система автоматически выберет unified подсистему для Grid клонера

3. **Настройка параметров**
   ```python
   # В Properties > Modifier Properties найдите Grid Cloner
   Count X: 5           # Количество по X
   Count Y: 3           # Количество по Y  
   Count Z: 1           # Количество по Z
   Spacing: 2.0         # Расстояние между копиями
   Realize Instances: False  # Анти-рекурсия (по умолчанию включена)
   ```

#### Object клонер (Mesh система через единый API)
1. **Подготовка**
   ```python
   # Создайте объект для клонирования
   bpy.ops.mesh.primitive_cube_add(location=(0, 0, 0))
   source = bpy.context.active_object
   
   # Создайте поверхность для клонирования
   bpy.ops.mesh.primitive_plane_add(size=10, location=(5, 0, 0))
   target = bpy.context.active_object
   ```

2. **Создание клонера**
   - Выберите целевой объект (поверхность)
   - В панели ClonerPro нажмите `Object Cloner`
   - Система автоматически выберет mesh подсистему для Object клонера
   - Клонер создается в режиме `Stacked` на поверхности

3. **Настройка параметров**
   ```python
   # В Properties > Modifier Properties найдите Object Cloner
   Distribution Mode: 0  # 0=Vertices, 1=Edges, 2=Faces
   Instance Scale: 1.0   # Масштаб инстансов
   Align to Normal: True # Выравнивание по нормалям
   Realize Instances: False  # Анти-рекурсия (управляется автоматически)
   ```

## Режимы создания

### Object Mode
- **Описание**: Создаёт новый объект клонера в коллекции CLONERS_
- **Применение**: Когда нужен отдельный объект для клонирования
- **Преимущества**: Не изменяет исходный объект, легко управлять

```python
# Пример создания в Object Mode через единый API
from core.templates.cloner_creation import create_cloner_unified

cloner_obj = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT", 
    source=bpy.context.active_object
)
# Система автоматически выберет unified подсистему для GRID
```

### Stacked Mode  
- **Описание**: Добавляет модификатор клонера на исходный объект
- **Применение**: Когда нужно клонировать сам объект
- **Преимущества**: Компактность, прямое редактирование

```python
# Пример создания в Stacked Mode через единый API
modifier = create_cloner_unified(
    cloner_type="LINEAR",
    mode="STACKED",
    source=bpy.context.active_object
)
# Система автоматически выберет unified подсистему для LINEAR
```

### Collection Mode
- **Описание**: Клонирует целые коллекции объектов
- **Применение**: Для сложных групп объектов
- **Преимущества**: Клонирование множественных объектов одновременно

```python
# Пример создания в Collection Mode через единый API
cloner_obj = create_cloner_unified(
    cloner_type="CIRCLE",
    mode="COLLECTION",
    source="MyCollection"  # Имя существующей коллекции
)
# Система автоматически выберет unified подсистему для CIRCLE
```

## Работа с цепочками клонеров

### Автоматическое создание цепочек
ClonerPro автоматически обнаруживает и создаёт цепочки клонеров:

```python
# Пример цепочки: Cube -> Grid -> Object -> Linear
# 1. Исходный куб
bpy.ops.mesh.primitive_cube_add()
original = bpy.context.active_object

# 2. Grid клонер от куба
grid_cloner = create_cloner_unified("GRID", "OBJECT", original)

# 3. Object клонер от Grid клонера (автоматически обнаружится цепочка)
object_modifier = create_cloner_unified("OBJECT", "STACKED", grid_cloner)

# 4. Linear клонер от Object клонера (автоматически обнаружится цепочка)
linear_cloner = create_cloner_unified("LINEAR", "OBJECT", grid_cloner)
```

### Браузер цепочек
1. **Открытие браузера**
   - В панели ClonerPro найдите `Cloner Browser`
   - Нажмите `Scan Cloners`

2. **Навигация по цепочкам**
   - Цепочки группируются по источнику
   - Клик на клонер выбирает его в сцене
   - UUID отображаются для отладки

## Диагностика и отладка

### Проверка состояния аддона

```python
# В Python Console (Shift+F4)
import bpy

# Проверка что аддон загружен
print("ClonerPro" in bpy.context.preferences.addons)

# Проверка панели
areas = [area for area in bpy.context.screen.areas if area.type == 'VIEW_3D']
if areas:
    space = areas[0].spaces.active
    print("ClonerPro" in [panel.bl_category for panel in space.panels])
```

### Диагностика клонеров

```python
# Проверка объекта на наличие клонера
from core.core import get_cloner_modifier, get_cloner_info

obj = bpy.context.active_object
modifier = get_cloner_modifier(obj)

if modifier:
    info = get_cloner_info(modifier)
    print(f"Тип клонера: {info['type']}")
    print(f"Режим: {info['mode']}")
    print(f"UUID: {info.get('cloner_uuid', 'НЕТ')}")
else:
    print("Объект не является клонером")
```

### Сканирование всех клонеров

```python
# Полное сканирование сцены
from core.uuid.manager import BlenderClonerUUIDManager

result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
print(f"Найдено клонеров с UUID: {len(result['cloners_by_uuid'])}")
print(f"Найдено цепочек: {len(result['chains_by_uuid'])}")
print(f"Legacy клонеры: {len(result['legacy_cloners'])}")
```

## Решение проблем

### Частые проблемы

#### Аддон не отображается в панели
1. **Проверьте активацию**: Preferences > Add-ons > ClonerPro ✓
2. **Перезапустите Blender**
3. **Проверьте версию Blender** (минимум 4.0)

#### Клонер не создаётся
1. **Проверьте выбранный объект**: Должен быть mesh объект
2. **Проверьте консоль ошибок**: Window > Toggle System Console
3. **Попробуйте другой режим создания**

#### Цепочки не обнаруживаются
1. **Включите UUID систему**: Должна быть включена по умолчанию
2. **Пересканируйте клонеры**: Cloner Browser > Scan Cloners
3. **Проверьте метаданные клонеров**

#### Производительность низкая
1. **Уменьшите количество инстансов**: Max Instances < 1000
2. **Отключите Auto Update**: Для сложных сцен
3. **Используйте LOD objects**: Упрощённые объекты для клонирования

### Логи отладки

```python
# Включение отладочного режима
import bpy
scene = bpy.context.scene
scene.clonerpro_debug_mode = True

# Просмотр логов в System Console
# Windows > Toggle System Console
```

### Резервное копирование

#### Перед крупными операциями
```python
# Сохранение файла с версией
import bpy
import time

timestamp = int(time.time())
backup_name = f"{bpy.data.filepath[:-6]}_backup_{timestamp}.blend"
bpy.ops.wm.save_as_mainfile(filepath=backup_name, copy=True)
```

#### Экспорт настроек клонеров
```python
# TODO: Будет реализовано в следующих версиях
# Экспорт всех клонеров и их параметров в JSON
# Импорт настроек из JSON файла
```

## Оптимизация производительности

### Настройки для больших сцен
```python
# В панели Settings
Max Instances: 5000          # Уменьшите для лучшей производительности
Auto Update: False           # Отключите для ручного обновления
Cache Node Groups: True      # Включите кэширование (когда доступно)
```

### Viewport оптимизация
```python
# В 3D Viewport
Viewport Shading: Solid      # Вместо Material Preview/Rendered
Display As: Bounds           # Для клонированных объектов
Simplify: Enable             # В Render Properties
```

Эта настройка обеспечит оптимальную работу ClonerPro в вашей среде Blender.