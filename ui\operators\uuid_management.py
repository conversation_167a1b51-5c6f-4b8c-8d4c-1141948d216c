"""
Операторы для управления UUID системой ClonerPro
Обеспечивают миграцию, валидацию и восстановление UUID клонеров
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, BoolProperty, EnumProperty

# Импорт UUID системы
try:
    from ...core.uuid.manager import BlenderClonerUUIDManager
    from ...core.uuid.chain_management import BlenderUUIDChainManager
    UUID_SYSTEM_AVAILABLE = True
except ImportError:
    UUID_SYSTEM_AVAILABLE = False
    print("⚠️ UUID Management Operators: Not available")

from ...core.core import get_cloner_modifier, get_cloner_info


class CLONERPRO_OT_Migrate_Legacy_Blender_Cloners(Operator):
    """Миграция всех legacy клонеров в Blender"""
    bl_idname = "clonerpro.migrate_legacy_blender_cloners"
    bl_label = "Migrate Legacy Cloners"
    bl_description = "Migrate all legacy cloners to UUID system"
    bl_options = {'REGISTER', 'UNDO'}
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        legacy_cloners = scan_result["legacy_cloners"]
        
        if not legacy_cloners:
            self.report({'INFO'}, "No legacy cloners found")
            return {'FINISHED'}
        
        migrated_count = 0
        errors = []
        
        for obj, modifier in legacy_cloners:
            try:
                cloner_uuid = BlenderClonerUUIDManager.migrate_legacy_cloner(obj, modifier)
                if cloner_uuid:
                    migrated_count += 1
                    print(f"✓ Migrated {obj.name} → UUID: {cloner_uuid}")
            except Exception as e:
                error_msg = f"Migration failed for {obj.name}: {str(e)}"
                print(f"❌ {error_msg}")
                errors.append(error_msg)
        
        # Отчет о результатах
        if errors:
            self.report({'WARNING'}, f"Migrated {migrated_count}/{len(legacy_cloners)} cloners. {len(errors)} errors occurred.")
            # Выводим первые 3 ошибки в консоль
            for error in errors[:3]:
                print(f"  Error: {error}")
            if len(errors) > 3:
                print(f"  ... and {len(errors) - 3} more errors")
        else:
            self.report({'INFO'}, f"Successfully migrated {migrated_count}/{len(legacy_cloners)} cloners")
        
        # Обновление UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                for region in area.regions:
                    if region.type == 'UI':
                        region.tag_redraw()
        
        return {'FINISHED'}


class CLONERPRO_OT_Migrate_Single_Cloner(Operator):
    """Миграция отдельного legacy клонера"""
    bl_idname = "clonerpro.migrate_single_cloner"
    bl_label = "Migrate Single Cloner"
    bl_description = "Migrate specific legacy cloner to UUID system"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        if self.object_name not in bpy.data.objects:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        obj = bpy.data.objects[self.object_name]
        modifier = None
        
        for mod in obj.modifiers:
            if mod.name == self.modifier_name:
                modifier = mod
                break
        
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        try:
            cloner_uuid = BlenderClonerUUIDManager.migrate_legacy_cloner(obj, modifier)
            if cloner_uuid:
                self.report({'INFO'}, f"Successfully migrated {obj.name} to UUID: {cloner_uuid[:8]}...")
                print(f"✓ Single migration complete: {obj.name} → {cloner_uuid}")
            else:
                self.report({'ERROR'}, f"Migration failed for {obj.name}")
        except Exception as e:
            self.report({'ERROR'}, f"Migration error: {str(e)}")
            print(f"❌ Migration error for {obj.name}: {e}")
        
        return {'FINISHED'}


class CLONERPRO_OT_Repair_Blender_UUID_Chains(Operator):
    """Восстановление битых UUID цепочек"""
    bl_idname = "clonerpro.repair_blender_uuid_chains"
    bl_label = "Repair UUID Chains"
    bl_description = "Repair broken UUID chain links"
    bl_options = {'REGISTER', 'UNDO'}
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        orphaned_cloners = scan_result["orphaned_cloners"]
        
        if not orphaned_cloners:
            self.report({'INFO'}, "No broken chain links found")
            return {'FINISHED'}
        
        repaired_count = 0
        
        for obj, modifier in orphaned_cloners:
            try:
                success = self._repair_cloner_links(obj, modifier)
                if success:
                    repaired_count += 1
                    print(f"✓ Repaired links for {obj.name}")
            except Exception as e:
                print(f"❌ Failed to repair {obj.name}: {e}")
        
        self.report({'INFO'}, f"Repaired {repaired_count}/{len(orphaned_cloners)} broken chain links")
        return {'FINISHED'}
    
    def _repair_cloner_links(self, obj, modifier):
        """Восстановить связи конкретного клонера"""
        cloner_info = get_cloner_info(modifier)
        
        # Проверяем previous_cloner_uuid
        previous_uuid = cloner_info.get("previous_cloner_uuid", "")
        if previous_uuid:
            prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
            if not prev_obj:
                # Удаляем битую ссылку
                modifier["previous_cloner_uuid"] = ""
                print(f"  Removed broken previous link: {previous_uuid}")
        
        # Проверяем next_cloner_uuids
        next_uuids = cloner_info.get("next_cloner_uuids", "").split(",")
        next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
        
        valid_next_uuids = []
        for next_uuid in next_uuids:
            next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
            if next_obj:
                valid_next_uuids.append(next_uuid)
            else:
                print(f"  Removed broken next link: {next_uuid}")
        
        modifier["next_cloner_uuids"] = ",".join(valid_next_uuids)
        
        return True


class CLONERPRO_OT_Repair_Cloner_Links(Operator):
    """Восстановление связей отдельного клонера"""
    bl_idname = "clonerpro.repair_cloner_links"
    bl_label = "Repair Cloner Links"
    bl_description = "Repair broken links for specific cloner"
    bl_options = {'REGISTER', 'UNDO'}
    
    cloner_uuid: StringProperty(name="Cloner UUID")
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(self.cloner_uuid)
        if not obj:
            self.report({'ERROR'}, f"Cloner with UUID {self.cloner_uuid} not found")
            return {'CANCELLED'}
        
        try:
            # Используем метод из предыдущего оператора
            repair_op = CLONERPRO_OT_Repair_Blender_UUID_Chains()
            success = repair_op._repair_cloner_links(obj, modifier)
            
            if success:
                self.report({'INFO'}, f"Successfully repaired links for {obj.name}")
            else:
                self.report({'WARNING'}, f"Could not fully repair links for {obj.name}")
        except Exception as e:
            self.report({'ERROR'}, f"Repair failed: {str(e)}")
        
        return {'FINISHED'}


class CLONERPRO_OT_Scan_And_Validate_UUIDs(Operator):
    """Сканирование и валидация UUID системы"""
    bl_idname = "clonerpro.scan_and_validate_uuids"
    bl_label = "Validate UUIDs"
    bl_description = "Scan and validate UUID integrity"
    bl_options = {'REGISTER'}
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        # Выполняем полное сканирование
        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        
        # Анализируем результаты
        total_cloners = len(scan_result["cloners_by_uuid"])
        chains_count = len(scan_result["chains_by_uuid"])
        legacy_count = len(scan_result["legacy_cloners"])
        orphaned_count = len(scan_result["orphaned_cloners"])
        session_uid_count = len(scan_result["session_uid_cloners"])
        
        # Проверяем дубликаты UUID
        duplicate_check = self._check_duplicate_uuids(scan_result)
        
        # Формируем отчет
        report_lines = [
            f"UUID Validation Complete:",
            f"  Total UUID cloners: {total_cloners}",
            f"  Chains: {chains_count}",
            f"  Legacy cloners: {legacy_count}",
            f"  Orphaned cloners: {orphaned_count}",
            f"  Session UID tracked: {session_uid_count}"
        ]
        
        if duplicate_check["duplicates"]:
            report_lines.append(f"  ⚠️ Duplicate UUIDs found: {len(duplicate_check['duplicates'])}")
        
        # Выводим отчет
        for line in report_lines:
            print(line)
        
        # Определяем статус
        if orphaned_count > 0 or duplicate_check["duplicates"]:
            self.report({'WARNING'}, f"Validation complete: {orphaned_count} orphaned, {len(duplicate_check['duplicates'])} duplicates")
        else:
            self.report({'INFO'}, f"Validation complete: {total_cloners} cloners, {chains_count} chains - All OK")
        
        return {'FINISHED'}
    
    def _check_duplicate_uuids(self, scan_result):
        """Проверить дубликаты UUID"""
        uuid_counts = {}
        duplicates = []
        
        for cloner_uuid, (obj, modifier) in scan_result["cloners_by_uuid"].items():
            if cloner_uuid in uuid_counts:
                uuid_counts[cloner_uuid].append((obj, modifier))
            else:
                uuid_counts[cloner_uuid] = [(obj, modifier)]
        
        for uuid, cloners in uuid_counts.items():
            if len(cloners) > 1:
                duplicates.append({
                    "uuid": uuid,
                    "cloners": cloners
                })
                print(f"⚠️ Duplicate UUID found: {uuid}")
                for obj, mod in cloners:
                    print(f"    Object: {obj.name}")
        
        return {"duplicates": duplicates}


class CLONERPRO_OT_Clean_Orphaned_UUIDs(Operator):
    """Очистка orphaned UUID записей"""
    bl_idname = "clonerpro.clean_orphaned_uuids"
    bl_label = "Clean Orphaned UUIDs"
    bl_description = "Clean up orphaned UUID references"
    bl_options = {'REGISTER', 'UNDO'}
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        cleaned_count = 0
        
        # Сканируем все объекты на предмет orphaned UUID метаданных
        for obj in bpy.data.objects:
            for modifier in obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    cleaned = self._clean_modifier_orphaned_data(modifier)
                    if cleaned:
                        cleaned_count += 1
        
        self.report({'INFO'}, f"Cleaned orphaned data from {cleaned_count} cloners")
        return {'FINISHED'}
    
    def _clean_modifier_orphaned_data(self, modifier):
        """Очистить orphaned данные из модификатора"""
        cleaned = False
        
        # Проверяем и очищаем битые UUID ссылки
        previous_uuid = modifier.get("previous_cloner_uuid", "")
        if previous_uuid:
            prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
            if not prev_obj:
                modifier["previous_cloner_uuid"] = ""
                cleaned = True
        
        # Проверяем next_cloner_uuids
        next_uuids = modifier.get("next_cloner_uuids", "").split(",")
        next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
        
        valid_next_uuids = []
        for next_uuid in next_uuids:
            next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
            if next_obj:
                valid_next_uuids.append(next_uuid)
            else:
                cleaned = True
        
        if len(valid_next_uuids) != len(next_uuids):
            modifier["next_cloner_uuids"] = ",".join(valid_next_uuids)
        
        return cleaned


class CLONERPRO_OT_Delete_Cloner_UUID(Operator):
    """Удаление клонера по UUID с восстановлением цепи"""
    bl_idname = "clonerpro.delete_cloner_uuid"
    bl_label = "Delete Cloner (UUID)"
    bl_description = "Delete cloner using UUID-based chain recovery"
    bl_options = {'REGISTER', 'UNDO'}
    
    cloner_uuid: StringProperty(name="Cloner UUID")
    confirm: BoolProperty(name="Confirm", default=False)
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def invoke(self, context, event):
        if not self.confirm:
            return context.window_manager.invoke_confirm(self, event)
        return self.execute(context)
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(self.cloner_uuid)
        if not obj:
            self.report({'ERROR'}, f"Cloner with UUID {self.cloner_uuid} not found")
            return {'CANCELLED'}
        
        object_name = obj.name
        
        try:
            success = BlenderUUIDChainManager.delete_cloner_with_uuid_recovery_blender(
                context, self.cloner_uuid
            )
            
            if success:
                self.report({'INFO'}, f"Successfully deleted cloner: {object_name}")
                print(f"✓ UUID-based deletion complete: {object_name}")
            else:
                self.report({'ERROR'}, f"Failed to delete cloner: {object_name}")
        except Exception as e:
            self.report({'ERROR'}, f"Deletion failed: {str(e)}")
            print(f"❌ UUID deletion error: {e}")
        
        return {'FINISHED'}


class CLONERPRO_OT_Select_Chain(Operator):
    """Выбрать всю цепочку клонеров"""
    bl_idname = "clonerpro.select_chain"
    bl_label = "Select Chain"
    bl_description = "Select all cloners in the chain"
    bl_options = {'REGISTER', 'UNDO'}
    
    chain_uuid: StringProperty(name="Chain UUID")
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(self.chain_uuid)
        
        if not chain_cloners:
            self.report({'ERROR'}, f"Chain with UUID {self.chain_uuid} not found")
            return {'CANCELLED'}
        
        # Очищаем текущий выбор
        bpy.ops.object.select_all(action='DESELECT')
        
        # Выбираем все объекты в цепи
        for obj, modifier in chain_cloners:
            obj.select_set(True)
        
        # Делаем первый объект активным
        if chain_cloners:
            context.view_layer.objects.active = chain_cloners[0][0]
        
        self.report({'INFO'}, f"Selected {len(chain_cloners)} cloners in chain")
        return {'FINISHED'}


class CLONERPRO_OT_Delete_Chain(Operator):
    """Удалить всю цепочку клонеров"""
    bl_idname = "clonerpro.delete_chain"
    bl_label = "Delete Chain"
    bl_description = "Delete entire cloner chain"
    bl_options = {'REGISTER', 'UNDO'}
    
    chain_uuid: StringProperty(name="Chain UUID")
    confirm: BoolProperty(name="Confirm", default=False)
    
    @classmethod
    def poll(cls, context):
        return UUID_SYSTEM_AVAILABLE
    
    def invoke(self, context, event):
        if not self.confirm:
            return context.window_manager.invoke_confirm(self, event)
        return self.execute(context)
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(self.chain_uuid)
        
        if not chain_cloners:
            self.report({'ERROR'}, f"Chain with UUID {self.chain_uuid} not found")
            return {'CANCELLED'}
        
        deleted_count = 0
        # Удаляем в обратном порядке (от конца к началу)
        for obj, modifier in reversed(chain_cloners):
            try:
                cloner_uuid = modifier.get("cloner_uuid", "")
                if cloner_uuid:
                    success = BlenderUUIDChainManager.delete_cloner_with_uuid_recovery_blender(
                        context, cloner_uuid
                    )
                    if success:
                        deleted_count += 1
            except Exception as e:
                print(f"❌ Failed to delete {obj.name}: {e}")
        
        self.report({'INFO'}, f"Deleted {deleted_count}/{len(chain_cloners)} cloners from chain")
        return {'FINISHED'}


# Регистрация операторов
classes = [
    CLONERPRO_OT_Migrate_Legacy_Blender_Cloners,
    CLONERPRO_OT_Migrate_Single_Cloner,
    CLONERPRO_OT_Repair_Blender_UUID_Chains,
    CLONERPRO_OT_Repair_Cloner_Links,
    CLONERPRO_OT_Scan_And_Validate_UUIDs,
    CLONERPRO_OT_Clean_Orphaned_UUIDs,
    CLONERPRO_OT_Delete_Cloner_UUID,
    CLONERPRO_OT_Select_Chain,
    CLONERPRO_OT_Delete_Chain,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)