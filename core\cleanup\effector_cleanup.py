"""
Effector Cleanup Manager для ClonerPro
Автоматическая очистка эффекторов при удалении модификаторов
"""

import bpy


def setup_effector_deletion_monitoring():
    """
    Настраивает мониторинг удаления эффекторов
    С защитными механизмами против крашей
    """
    try:
        # Создаем handler для отслеживания изменений
        def effector_cleanup_handler(scene, depsgraph):
            """Handler для автоматической очистки удаленных эффекторов"""
            try:
                # Импортируем защитные функции из core
                from ...core.system.dependency_safety import is_safe_to_perform_effector_operations
                
                # Проверяем безопасность операций
                if not is_safe_to_perform_effector_operations():
                    return
                
                # Получаем список всех объектов с клонерами
                for obj in scene.objects:
                    if not obj.modifiers:
                        continue
                    
                    # Проверяем каждый клонер на объекте
                    for cloner_mod in obj.modifiers:
                        if (cloner_mod.type == 'NODES' and cloner_mod.node_group and
                            cloner_mod.node_group.get("supports_effector_chains", False)):
                            
                            # Проверяем связанные эффекторы
                            linked_effectors = list(cloner_mod.node_group.get("linked_effectors", []))
                            effectors_to_remove = []
                            
                            for effector_name in linked_effectors:
                                # Проверяем, существует ли еще модификатор эффектора
                                effector_mod = obj.modifiers.get(effector_name)
                                if not effector_mod:
                                    # Эффектор был удален - нужно очистить
                                    effectors_to_remove.append(effector_name)
                            
                            # Безопасно удаляем отсутствующие эффекторы из цепочки
                            for effector_name in effectors_to_remove:
                                safe_cleanup_deleted_effector_from_cloner(cloner_mod, effector_name)
                                # Cleanup operation
            
            except Exception as e:
                # Безопасно игнорируем ошибки в handler
                # Cleanup operation
                
                # При критической ошибке выполняем экстренную очистку
                try:
                    from ...core.system.dependency_safety import emergency_system_reset
                    emergency_system_reset()
                except:
                    pass
        
        # Убираем старые handlers
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, '__name__') and 
                handler.__name__ == 'effector_cleanup_handler'):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
        
        # Добавляем новый handler
        bpy.app.handlers.depsgraph_update_post.append(effector_cleanup_handler)
        
        print("✅ Настроен мониторинг удаления эффекторов")
        
    except Exception as e:
        print(f"[ERROR] Ошибка настройки мониторинга: {e}")


def cleanup_deleted_effector_from_cloner(cloner_mod, effector_name):
    """
    Очищает удаленный эффектор из клонера
    Простая реализация без сложной архитектуры
    """
    try:
        if not cloner_mod.node_group:
            return
        
        # 1. Удаляем узел эффектора из цепочки
        effector_node = None
        for node in cloner_mod.node_group.nodes:
            if (node.type == 'GROUP' and 
                node.name.startswith("Effector_") and
                effector_name in node.name):
                effector_node = node
                break
        
        if effector_node:
            # Получаем входящие и исходящие связи
            input_socket = None
            output_connections = []
            
            # Ищем входящую связь
            for link in cloner_mod.node_group.links:
                if link.to_node == effector_node and link.to_socket.name == 'Geometry':
                    input_socket = link.from_socket
                    break
            
            # Ищем исходящие связи
            for link in cloner_mod.node_group.links:
                if link.from_node == effector_node and link.from_socket.name == 'Geometry':
                    output_connections.append(link.to_socket)
            
            # Удаляем все связи эффектора
            links_to_remove = []
            for link in cloner_mod.node_group.links:
                if link.from_node == effector_node or link.to_node == effector_node:
                    links_to_remove.append(link)
            
            for link in links_to_remove:
                cloner_mod.node_group.links.remove(link)
            
            # Восстанавливаем прямую связь (обходим эффектор)
            if input_socket and output_connections:
                for output_socket in output_connections:
                    cloner_mod.node_group.links.new(input_socket, output_socket)
            
            # Удаляем узел эффектора
            cloner_mod.node_group.nodes.remove(effector_node)
            # Removed debug log
        
        # 2. Удаляем из списка связанных эффекторов
        linked_effectors = list(cloner_mod.node_group.get("linked_effectors", []))
        if effector_name in linked_effectors:
            linked_effectors.remove(effector_name)
            cloner_mod.node_group["linked_effectors"] = linked_effectors
        
        # 3. Удаляем из метаданных цепочки
        chain_order = list(cloner_mod.node_group.get("effector_chain_order", []))
        if effector_name in chain_order:
            chain_order.remove(effector_name)
            cloner_mod.node_group["effector_chain_order"] = chain_order
        
        nodes_map = dict(cloner_mod.node_group.get("effector_nodes_map", {}))
        if effector_name in nodes_map:
            del nodes_map[effector_name]
            cloner_mod.node_group["effector_nodes_map"] = nodes_map
        
        # Removed debug log
        
    except Exception as e:
        print(f"[ERROR] Ошибка очистки эффектора {effector_name}: {e}")


def safe_cleanup_deleted_effector_from_cloner(cloner_mod, effector_name):
    """
    Безопасная очистка удаленного эффектора из клонера
    С дополнительными проверками безопасности
    """
    try:
        # Импортируем функции безопасности из core
        from ...core.system.dependency_safety import safe_execute_effector_operation
        
        def _internal_cleanup():
            return cleanup_deleted_effector_from_cloner(cloner_mod, effector_name)
        
        # Выполняем операцию безопасно
        success, result = safe_execute_effector_operation(_internal_cleanup)
        
        if success:
            # Removed debug log
            pass
        else:
            print(f"[ERROR] Safe cleanup failed for {effector_name}: {result}")
            # Выполняем дополнительную очистку при неудаче
            force_rebuild_effector_chain(cloner_mod)
            
    except Exception as e:
        print(f"[ERROR] Safe cleanup error for {effector_name}: {e}")


def force_rebuild_effector_chain(cloner_mod):
    """
    Принудительная перестройка цепочки эффекторов
    """
    try:
        if not cloner_mod.node_group:
            return
            
        # Safety operation
        
        # Собираем информацию о существующих эффекторах
        existing_effectors = []
        effector_nodes = []
        
        for node in cloner_mod.node_group.nodes:
            if (node.type == 'GROUP' and 
                node.name.startswith("Effector_")):
                effector_nodes.append(node)
                
                # Пытаемся извлечь имя эффектора
                parts = node.name.split("_")
                if len(parts) >= 3:
                    effector_name = "_".join(parts[2:])
                    existing_effectors.append(effector_name)
        
        # Обновляем метаданные на основе реальных узлов
        cloner_mod.node_group["linked_effectors"] = existing_effectors
        
        # Очищаем и перестраиваем метаданные цепочки
        cloner_mod.node_group["effector_chain_order"] = existing_effectors
        
        nodes_map = {}
        for i, effector_name in enumerate(existing_effectors):
            if i < len(effector_nodes):
                nodes_map[effector_name] = effector_nodes[i].name
        
        cloner_mod.node_group["effector_nodes_map"] = nodes_map
        cloner_mod.node_group["last_effector_index"] = len(existing_effectors)
        
        # Safety operation
        
    except Exception as e:
        print(f"[ERROR] Ошибка перестройки цепочки: {e}")


def cleanup_unused_effectors():
    """
    Принудительная очистка всех осиротевших эффекторов
    Может использоваться для ручной очистки
    """
    try:
        print("=== Принудительная очистка осиротевших эффекторов ===")
        cleaned_count = 0
        
        for obj in bpy.context.scene.objects:
            if not obj.modifiers:
                continue
            
            # Проверяем каждый клонер на объекте
            for cloner_mod in obj.modifiers:
                if (cloner_mod.type == 'NODES' and cloner_mod.node_group and
                    cloner_mod.node_group.get("supports_effector_chains", False)):
                    
                    # Проверяем связанные эффекторы
                    linked_effectors = list(cloner_mod.node_group.get("linked_effectors", []))
                    effectors_to_remove = []
                    
                    for effector_name in linked_effectors:
                        # Проверяем, существует ли еще модификатор эффектора
                        effector_mod = obj.modifiers.get(effector_name)
                        if not effector_mod:
                            effectors_to_remove.append(effector_name)
                    
                    # Удаляем отсутствующие эффекторы
                    for effector_name in effectors_to_remove:
                        cleanup_deleted_effector_from_cloner(cloner_mod, effector_name)
                        cleaned_count += 1
                        print(f"Очищен эффектор {effector_name} из {cloner_mod.name}")
        
        print(f"✅ Очистка завершена. Удалено {cleaned_count} осиротевших эффекторов")
        return cleaned_count
        
    except Exception as e:
        print(f"[ERROR] Ошибка принудительной очистки: {e}")
        return 0


def complete_effector_memory_cleanup(effector_name):
    """
    Обертка для полной очистки памяти эффектора через core систему
    """
    try:
        from ...core.system.dependency_safety import complete_effector_memory_cleanup as core_cleanup
        core_cleanup(effector_name)
    except Exception as e:
        print(f"[ERROR] Ошибка полной очистки памяти для {effector_name}: {e}")


def remove_effector_deletion_monitoring():
    """Удаляет мониторинг удаления эффекторов"""
    try:
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, '__name__') and 
                handler.__name__ == 'effector_cleanup_handler'):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
        
        print("✅ Мониторинг удаления эффекторов отключен")
        
    except Exception as e:
        print(f"[ERROR] Ошибка отключения мониторинга: {e}")


