"""
Операторы для Browser Panel
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty


class CLONERPRO_OT_refresh_browser(Operator):
    """Обновить браузер компонентов"""
    bl_idname = "clonerpro.refresh_browser"
    bl_label = "Refresh Browser"
    bl_description = "Refresh component browser"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        # TODO: Обновить список компонентов в сцене
        self.report({'INFO'}, "Browser refreshed")
        return {'FINISHED'}


class CLONERPRO_OT_toggle_expand(Operator):
    """Toggle panel/element expanded state"""
    bl_idname = "clonerpro.toggle_expand"
    bl_label = "Toggle Expand"
    bl_description = "Toggle panel or element expanded state"
    bl_options = {'REGISTER'}
    
    obj_name: StringProperty(
        name="Object Name",
        description="Object name for state storage",
        default=""
    )
    
    modifier_name: StringProperty(
        name="Modifier Name", 
        description="Modifier name for state storage",
        default=""
    )
    
    state_property: StringProperty(
        name="State Property",
        description="Scene property name to store state",
        default=""
    )

    def execute(self, context):
        # Toggle the state property
        if self.state_property and hasattr(context.scene, self.state_property):
            current_state = getattr(context.scene, self.state_property)
            setattr(context.scene, self.state_property, not current_state)
        
        # Force UI refresh
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}


class CLONERPRO_OT_expand_all_groups(Operator):
    """Развернуть все группы"""
    bl_idname = "clonerpro.expand_all_groups"
    bl_label = "Expand All Groups"
    bl_description = "Expand all component groups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        """Развернуть все группы в браузере"""
        settings = context.scene.clonerpro_settings
        
        # Установить все группы как развернутые
        for group_state in settings.browser_group_states:
            group_state.is_expanded = True
        
        self.report({'INFO'}, "All groups expanded")
        return {'FINISHED'}


class CLONERPRO_OT_collapse_all_groups(Operator):
    """Свернуть все группы"""
    bl_idname = "clonerpro.collapse_all_groups"
    bl_label = "Collapse All Groups"
    bl_description = "Collapse all component groups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        """Свернуть все группы в браузере"""
        settings = context.scene.clonerpro_settings
        
        # Установить все группы как свернутые
        for group_state in settings.browser_group_states:
            group_state.is_expanded = False
        
        self.report({'INFO'}, "All groups collapsed")
        return {'FINISHED'}


class CLONERPRO_OT_toggle_group_expanded(Operator):
    """Переключить состояние группы"""
    bl_idname = "clonerpro.toggle_group_expanded"
    bl_label = "Toggle Group Expanded"
    bl_description = "Toggle group expand/collapse state"
    bl_options = {'REGISTER'}
    
    group_name: StringProperty(
        name="Group Name",
        description="Name of group to toggle",
        default=""
    )

    def execute(self, context):
        """Переключить состояние expand/collapse для группы"""
        settings = context.scene.clonerpro_settings
        
        # Найти состояние группы
        group_state = None
        for state in settings.browser_group_states:
            if state.group_name == self.group_name:
                group_state = state
                break
        
        # Создать новое состояние если не найдено
        if not group_state:
            group_state = settings.browser_group_states.add()
            group_state.group_name = self.group_name
            group_state.is_expanded = True  # По умолчанию развернуто
        
        # Переключить состояние
        group_state.is_expanded = not group_state.is_expanded
        
        return {'FINISHED'}


class CLONERPRO_OT_show_component_info(Operator):
    """Показать информацию о компоненте"""
    bl_idname = "clonerpro.show_component_info"
    bl_label = "Component Info"
    bl_description = "Show component information"
    bl_options = {'REGISTER'}
    
    component_type: StringProperty(
        name="Component Type",
        description="Type of component",
        default=""
    )
    
    description: StringProperty(
        name="Description",
        description="Component description",
        default=""
    )

    def execute(self, context):
        self.report({'INFO'}, f"{self.component_type}: {self.description}")
        return {'FINISHED'}


def register():
    """Регистрация операторов браузера"""
    bpy.utils.register_class(CLONERPRO_OT_refresh_browser)
    bpy.utils.register_class(CLONERPRO_OT_toggle_expand)
    bpy.utils.register_class(CLONERPRO_OT_expand_all_groups)
    bpy.utils.register_class(CLONERPRO_OT_collapse_all_groups)
    bpy.utils.register_class(CLONERPRO_OT_toggle_group_expanded)
    bpy.utils.register_class(CLONERPRO_OT_show_component_info)


def unregister():
    """Отмена регистрации операторов браузера"""
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_show_component_info)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_toggle_group_expanded)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_collapse_all_groups)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_expand_all_groups)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_toggle_expand)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_refresh_browser)
    except:
        pass