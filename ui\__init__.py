"""
ClonerPro UI Module
Простой UI без автогенерации и сложных абстракций
"""

from . import panels
from . import operators
from .utils.ui_helpers import register_expanded_states_property, unregister_expanded_states_property


def register():
    """Register all UI components"""
    # Регистрируем свойства для состояний раскрытия
    register_expanded_states_property("cloner_expanded_states")
    
    operators.register()
    panels.register()


def unregister():
    """Unregister all UI components"""
    panels.unregister()
    operators.unregister()
    
    # Удаляем свойства для состояний раскрытия
    unregister_expanded_states_property("cloner_expanded_states")