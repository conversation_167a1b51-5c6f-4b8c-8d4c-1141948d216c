"""
Base Component Class для ClonerPro
Базовый класс для всех компонентов (клонеров, эффекторов, полей)
"""

import bpy
from abc import ABC, abstractmethod


class BaseComponent(ABC):
    """
    Базовый абстрактный класс для всех компонентов ClonerPro
    
    Каждый компонент должен реализовать:
    - Регистрацию своих свойств (bpy.props)
    - Логику создания geometry nodes
    - Отрисовку UI
    
    Принципы:
    - Один класс = один компонент 
    - Вся логика в одном месте
    - Консистентные интерфейсы
    """
    
    # Обязательные атрибуты для каждого компонента
    bl_idname = "base_component"
    bl_label = "Base Component"
    component_type = "BASE"  # BASE, CLONER, EFFECTOR, FIELD
    
    def __init__(self):
        """Инициализация базового компонента"""
        pass
    
    @abstractmethod
    def register_properties(self, props_owner):
        """
        Регистрация всех bpy.props этого компонента
        
        Args:
            props_owner: Объект для регистрации свойств (обычно bpy.types.Scene)
        """
        pass
    
    @abstractmethod 
    def create_node_group(self, mode="OBJECT"):
        """
        Создание node group с логикой компонента
        
        Args:
            mode: Режим создания ("OBJECT", "STACKED", "COLLECTION")
            
        Returns:
            tuple: (node_group, socket_mapping) или None при ошибке
        """
        pass
    
    @abstractmethod
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI компонента в панели
        
        Args:
            layout: UI layout для отрисовки
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть) для доступа к сокетам
        """
        pass
    
    def get_default_parameters(self):
        """
        Возвращает словарь параметров по умолчанию
        
        Returns:
            dict: {"parameter_name": default_value}
        """
        return {}
    
    def validate_parameters(self, parameters):
        """
        Валидация параметров компонента
        
        Args:
            parameters: Словарь параметров для проверки
            
        Returns:
            tuple: (is_valid: bool, error_message: str)
        """
        return True, ""
    
    def get_socket_definitions(self):
        """
        Возвращает определения сокетов для создания интерфейса
        
        Returns:
            dict: Структурированное описание сокетов
        """
        return {
            "inputs": [],
            "outputs": []
        }
    
    def get_parameter_groups(self):
        """
        Возвращает группировку параметров для UI
        
        Returns:
            dict: {"group_name": ["param1", "param2"]}
        """
        return {}
    
    def create_base_interface(self, node_group):
        """
        Создание базового интерфейса (общие сокеты)
        
        Args:
            node_group: Node group для создания интерфейса
            
        Returns:
            dict: Маппинг созданных сокетов
        """
        interface = node_group.interface
        socket_mapping = {}
        
        # Базовые сокеты, общие для большинства компонентов
        common_sockets = [
            ("Geometry", "NodeSocketGeometry", "INPUT"),
            ("Geometry", "NodeSocketGeometry", "OUTPUT"),
        ]
        
        for socket_name, socket_type, in_out in common_sockets:
            if in_out == "INPUT":
                socket = interface.new_socket(socket_name, in_out='INPUT', socket_type=socket_type)
            else:
                socket = interface.new_socket(socket_name, in_out='OUTPUT', socket_type=socket_type)
            socket_mapping[socket_name.lower().replace(" ", "_")] = socket.identifier
            
        return socket_mapping
    
    def setup_default_values(self, modifier, socket_mapping):
        """
        Установка значений по умолчанию для сокетов модификатора
        
        Args:
            modifier: Модификатор geometry nodes
            socket_mapping: Маппинг сокетов
        """
        if not modifier or not socket_mapping:
            return
            
        defaults = self.get_default_parameters()
        for param_name, default_value in defaults.items():
            socket_id = socket_mapping.get(param_name.lower().replace(" ", "_"))
            if socket_id:
                try:
                    modifier[socket_id] = default_value
                except Exception as e:
                    print(f"[WARNING] Could not set default for {param_name}: {e}")
    
    def get_ui_layout_info(self):
        """
        Информация о раскладке UI
        
        Returns:
            dict: Информация о группах, порядке, видимости параметров
        """
        return {
            "show_advanced": False,
            "collapsible_groups": [],
            "parameter_order": []
        }
    
    def __str__(self):
        return f"{self.__class__.__name__}(id={self.bl_idname}, type={self.component_type})"
    
    def __repr__(self):
        return self.__str__()