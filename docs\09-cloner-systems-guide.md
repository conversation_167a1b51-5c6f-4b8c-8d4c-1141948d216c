# Руководство по системам клонеров ClonerPro

## Введение в единую архитектуру с автоматической маршрутизацией

ClonerPro построен на принципе **единого API** с автоматическим выбором оптимальной системы исполнения. Пользователь всегда использует одну функцию `create_cloner_unified()`, а система автоматически направляет запрос к нужной подсистеме.

## BaseCloner - единая классовая архитектура

### Философия и принципы

**Все клонеры наследуются от BaseCloner** для максимальной унификации и предсказуемости поведения.

#### Основные принципы:
- **Единый API** - `create_cloner_unified()` для всех типов клонеров
- **BaseCloner архитектура** - Все клонеры наследуются от одного базового класса  
- **Автоматическая маршрутизация** - Система сама выбирает unified/mesh подсистему
- **Встроенная функциональность** - Трансформации, рандомизация, анти-рекурсия
- **Трёхрежимная поддержка** - Object, Stacked, Collection режимы для всех типов

## Unified клонеры (математически основанные)

```python
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE"}
```

### GridCloner - Сетчатое клонирование

**Класс**: `GridCloner(BaseCloner)`  
**Идентификатор**: `bl_idname = "GRID"`  
**Система**: Автоматически выбирается unified при `create_cloner_unified("GRID", ...)`

**Назначение**: Создание 2D/3D сеток объектов с математическим распределением

**Специфичные параметры** (добавляются к базовым BaseCloner):
- Count X/Y/Z - Количество копий по осям
- Spacing - Расстояние между копиями

**Базовые параметры** (автоматически от BaseCloner):
- Instance Scale/Rotation - Трансформации инстансов
- Random Position/Rotation/Scale - Рандомизация
- Global Position/Rotation - Глобальные трансформации
- Realize Instances - Анти-рекурсия

**Применение**:
- Архитектурные массивы (окна, колонны)
- Городские кварталы  
- Мебельные композиции
- Технические структуры

**Пример создания через единый API**:
```python
from core.templates.cloner_creation import create_cloner_unified

# Система автоматически выберет unified и найдет GridCloner
grid_cloner = create_cloner_unified("GRID", "OBJECT", source_obj)
    links.new(mesh_grid.outputs['Mesh'], mesh_to_points.inputs['Mesh'])
    links.new(mesh_to_points.outputs['Points'], instance_on_points.inputs['Points'])
    
    return node_group
```

#### Linear Cloner - Линейное клонирование
**Назначение**: Создание линейных массивов

**Параметры**:
- Count - Количество копий
- Distance - Расстояние между копиями
- Direction - Направление массива
- End Offset - Смещение последней копии

**Применение**:
- Заборы и ограждения
- Колоннады
- Железнодорожные пути
- Конвейерные ленты

#### Circle Cloner - Круговое клонирование
**Назначение**: Размещение объектов по окружности

**Параметры**:
- Count - Количество копий
- Radius - Радиус окружности
- Start/End Angle - Углы начала и конца
- Align to Curve - Выравнивание по касательной

**Применение**:
- Круглые столы с стульями
- Храмовые колоннады
- Карусели и аттракционы
- Круговые орнаменты

#### Spiral Cloner - Спиральное клонирование
**Назначение**: Создание спиральных структур

**Параметры**:
- Count - Количество копий
- Turns - Количество оборотов
- Height - Высота спирали
- Radius - Радиус спирали

**Применение**:
- Винтовые лестницы
- ДНК структуры
- Пружины и спирали
- Декоративные элементы

### Архитектура BaseCloner системы

#### Единая архитектура для всех клонеров
```
User Request → create_cloner_unified(type, mode, source)
                      ↓
              Автоматическое определение системы (unified/mesh)
                      ↓
              _get_cloner_instance(cloner_type)  # Поиск BaseCloner наследника
                      ↓
              cloner_instance.create_node_group(mode)
                      ↓
              BaseCloner создает полную структуру:
              ├── Базовые сокеты (всем клонерам)
              ├── Специфичные сокеты (get_specific_sockets())
              ├── Специфичная логика (_create_cloner_logic())
              ├── Трансформации (instance + random + global)
              └── Анти-рекурсия (встроенная Switch логика)
```

#### BaseCloner архитектура
```python
class BaseCloner(BaseComponent):
    def create_node_group(self, mode="OBJECT"):
        """Единый метод для всех клонеров"""
        
        # 1. Создание интерфейса (базовые + специфичные сокеты)
        specific_sockets = self.get_specific_sockets()
        socket_mapping = self.create_cloner_interface(node_group, specific_sockets)
        
        # 2. Создание базовых нодов
        base_nodes = self.create_base_nodes(node_group, mode)
        
        # 3. Специфичная логика клонера (переопределяется)
        final_geometry = self._create_cloner_logic(base_nodes, mode)
        
        # 4. Встроенная анти-рекурсия (автоматически)
        anti_recursion_result = self.apply_anti_recursion(base_nodes, final_geometry)
        
        return node_group, socket_mapping
```

#### Три режима создания unified клонеров

**Object Mode - Создание объекта клонера**
```python
def _create_object_mode_cloner(wrapper_group, source_obj, cloner_type, use_anti_recursion):
    """Object режим для unified клонеров"""
    
    # 1. Создание копии источника в ClonerTo
    cloner_source_obj = create_object_copy_for_cloning(context, source_obj)
    
    # 2. Создание коллекции клонера
    cloner_collection = create_cloner_collection(context, source_obj, cloner_type)
    
    # 3. Создание объекта клонера
    cloner_obj = bpy.data.objects.new(cloner_name, cloner_mesh)
    cloner_collection.objects.link(cloner_obj)
    
    # 4. Создание модификатора с wrapper
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}Cloner3D", type='NODES')
    modifier.node_group = wrapper_group
    
    # 5. Настройка метаданных и UUID
    set_cloner_metadata(modifier, cloner_type, "OBJECT", original_object=source_obj.name)
    
    return cloner_obj
```

**Stacked Mode - Модификатор на объекте**
```python
def _create_stacked_mode_cloner(wrapper_group, target_obj, cloner_type, use_anti_recursion):
    """Stacked режим для unified клонеров"""
    
    # Создание модификатора напрямую на целевом объекте
    modifier = target_obj.modifiers.new(name=f"{cloner_type}Cloner", type='NODES')
    modifier.node_group = wrapper_group
    
    # Метаданные для stacked режима
    set_cloner_metadata(modifier, cloner_type, "STACKED", original_object=target_obj.name)
    
    return modifier
```

**Collection Mode - Клонирование коллекций**
```python
def _create_collection_mode_cloner(wrapper_group, collection_name, cloner_type, use_anti_recursion, name_suffix):
    """Collection режим для unified клонеров"""
    
    # 1. Обработка исходной коллекции
    target_collection = bpy.data.collections[collection_name]
    cloner_source_collection = create_collection_copy_for_cloning(context, target_collection)
    
    # 2. Создание объекта клонера
    cloner_obj = bpy.data.objects.new(cloner_name, cloner_mesh)
    
    # 3. Настройка Collection input в wrapper
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}ClonerCollection", type='NODES')
    modifier.node_group = wrapper_group
    
    # Подключение коллекции к Collection сокету
    for socket in wrapper_group.interface.items_tree:
        if socket.name == 'Collection':
            modifier[socket.identifier] = cloner_source_collection
    
    return cloner_obj
```

### Преимущества unified системы

#### 1. Консистентность API
```python
# Все unified клонеры создаются одинаково
grid_cloner = create_cloner_unified("GRID", "OBJECT", source)
linear_cloner = create_cloner_unified("LINEAR", "OBJECT", source)
circle_cloner = create_cloner_unified("CIRCLE", "OBJECT", source)
```

#### 2. Встроенная безопасность
- **Anti-recursion защита** применяется автоматически
- **Dependency safety** на уровне architecture
- **Chain management** интегрирован в создание

#### 3. Гибкость режимов
- **Object** - Независимые объекты клонеров
- **Stacked** - Модификаторы на исходных объектах
- **Collection** - Клонирование групп объектов

#### 4. Расширяемость
```python
# Добавление нового unified клонера требует только:
# 1. Добавить в UNIFIED_CLONERS
# 2. Создать logic функции
# 3. Зарегистрировать в CLONER_LOGIC_REGISTRY
# 4. Добавить в _get_cloner_logic_function()
```

## Mesh клонеры (геометрически основанные)

### Философия и принципы

**Mesh клонеры** специализируются на **клонировании по геометрии** (поверхности, кривые) и также наследуются от BaseCloner для единообразия.

```python
MESH_CLONERS = {"OBJECT", "SPLINE"}
```

#### Основные принципы:
- **BaseCloner наследование** - Та же архитектура что и unified клонеры
- **Mesh-dependent логика** - Специфичная логика для работы с геометрией
- **Автоматическая маршрутизация** - Система автоматически выбирает mesh подсистему
- **Единый API** - Те же `create_cloner_unified()` и BaseCloner методы
- **Performance first** - Оптимизация для сложных mesh операций
- **Специализация** - Каждый тип оптимизирован для своих задач

### Типы mesh клонеров

```python
MESH_CLONERS = {"OBJECT", "CURVES", "VOLUME", "SPLINE"}
```

### ObjectCloner - Клонирование по поверхности

**Класс**: `ObjectCloner(BaseCloner)`  
**Идентификатор**: `bl_idname = "OBJECT"`  
**Система**: Автоматически выбирается mesh при `create_cloner_unified("OBJECT", ...)`

**Назначение**: Распределение объектов по поверхности меша

**Специфичные параметры** (добавляются к базовым BaseCloner):
- Distribution Mode - Режим распределения (Vertices/Edges/Faces)
- Align to Normal - Выравнивание по нормалям поверхности

**Базовые параметры** (автоматически от BaseCloner):
- Instance Scale/Rotation - Трансформации инстансов
- Random Position/Rotation/Scale - Рандомизация
- Global Position/Rotation - Глобальные трансформации
- Realize Instances - Анти-рекурсия

**Режимы распределения**:
- **Vertices** (0) - По вертексам меша
- **Edges** (1) - По рёбрам меша  
- **Faces** (2) - По центрам фейсов

**Применение**:
- Деревья на ландшафте
- Детали на поверхности объектов
- Процедурные украшения
- Архитектурные элементы

**Пример создания через единый API**:
```python
from core.templates.cloner_creation import create_cloner_unified

# Система автоматически выберет mesh и найдет ObjectCloner
object_cloner = create_cloner_unified("OBJECT", "STACKED", surface_obj)
        "instance_scale": "Instance_Scale_Socket_ID"
    }
    
    return node_group, socket_mapping
```

## Преимущества единой архитектуры

### 1. Простота использования
```python
# Один API для ВСЕХ клонеров - система автоматически выберет подходящую реализацию
grid = create_cloner_unified("GRID", "OBJECT", source)      # unified
object = create_cloner_unified("OBJECT", "STACKED", surface) # mesh  
linear = create_cloner_unified("LINEAR", "COLLECTION", "MyCollection") # unified
spline = create_cloner_unified("SPLINE", "STACKED", curve)   # mesh
```

### 2. Предсказуемость интерфейса
Все клонеры имеют одинаковые базовые сокеты:
- **Геометрия**: Geometry, Instance Source, Collection
- **Трансформации**: Instance Scale, Instance Rotation  
- **Рандомизация**: Random Position, Random Rotation, Random Scale, Random Seed
- **Глобальные**: Global Position, Global Rotation
- **Защита**: Realize Instances (анти-рекурсия)

### 3. Автоматическая маршрутизация
```python
def create_cloner_unified(cloner_type, mode, source, **kwargs):
    """Система автоматически определяет и вызывает нужную подсистему"""
    
    # Автоматическое определение системы
    if cloner_type in UNIFIED_CLONERS:
        # Маршрутизация к unified подсистеме
        return _create_unified_cloner_via_unified_system(...)
    elif cloner_type in MESH_CLONERS:
        # Маршрутизация к mesh подсистеме  
        return _create_mesh_cloner_via_mesh_system(...)
```

### 4. Единообразное расширение
Добавление нового клонера требует только:
1. **Создать BaseCloner наследник** с `bl_idname`
2. **Добавить тип в константы** (`UNIFIED_CLONERS` или `MESH_CLONERS`)
3. **Переопределить специфичную логику** (`_create_cloner_logic()`)
4. **Автоматическое обнаружение** - система найдет новый клонер сама

### 5. Встроенная надежность
- **Анти-рекурсия**: Автоматически для всех клонеров через BaseCloner
- **UUID система**: Автоматическое управление цепочками клонеров
- **Системы безопасности**: Dependency safety, event handling
- **Валидация**: Проверка параметров и состояний

### 6. Производительность
- **Ленивая загрузка**: Клонеры загружаются только при использовании
- **Оптимизированные подсистемы**: unified для математических, mesh для геометрических задач
- **Кэширование**: Системное кэширование node групп (планируется)

## Сравнение подходов

### Старый подход (до рефакторинга)
```python
# Разные API для разных систем
from core.templates.unified_creation import create_cloner_unified  # unified
from core.templates.mesh_creation import create_mesh_cloner        # mesh

# Разные функции, разные параметры, разная логика
grid = create_cloner_unified("GRID", "OBJECT", source)
object = create_mesh_cloner("OBJECT", "STACKED", target_obj=surface)
```

### Новый подход (после рефакторинга)
```python
# Единый API для всех клонеров
from core.templates.cloner_creation import create_cloner_unified

# Одна функция, одинаковые параметры, автоматическая маршрутизация
grid = create_cloner_unified("GRID", "OBJECT", source)
object = create_cloner_unified("OBJECT", "STACKED", surface)
```

**Результат**: Упрощение на 50% кода при сохранении всей функциональности и добавлении новых возможностей.
```

#### Spline Cloner - Клонирование по кривым
**Назначение**: Размещение объектов вдоль кривых и путей

**Параметры**:
- Count - Количество инстансов
- Spacing Mode - Режим размещения (равномерно/по длине)
- Align to Curve - Выравнивание по касательной
- Offset - Смещение от кривой
- Start/End Factor - Начало и конец на кривой

**Применение**:
- Дороги с фонарями
- Железные дороги
- Трубопроводы с опорами
- Декоративные элементы вдоль путей

#### Volume Cloner - Клонирование по объёму (планируется)
**Назначение**: Распределение в трёхмерном пространстве объекта

**Параметры**:
- Density - Плотность распределения
- Volume Mode - Режим заполнения (весь объём/только поверхность)
- Noise Scale - Масштаб шума для нерегулярности

#### Curves Cloner - Клонирование по Hair/Curves (планируется)
**Назначение**: Работа с новой системой Curves в Blender

### Архитектура mesh системы

#### Прямое создание без wrapper
```
User Request → create_mesh_cloner() → _get_mesh_cloner_logic_function()
                                   ↓
                           Direct Node Group Creation
                                   ↓
                           Modifier Setup with Socket Mapping
                                   ↓
                           Parameter Configuration
```

#### Создание mesh клонера
```python
def create_mesh_cloner(cloner_type, mode, target_obj=None, collection_name=None, **kwargs):
    """Главная функция создания mesh клонеров"""
    
    # 1. Получение функции создания логики
    logic_function = _get_mesh_cloner_logic_function(cloner_type)
    
    # 2. Создание в зависимости от режима
    if mode == "STACKED":
        return _create_stacked_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs)
    elif mode == "COLLECTION":
        return _create_collection_mesh_cloner(cloner_type, logic_function, target_obj, collection_name, **kwargs)
    elif mode == "OBJECT":
        return _create_object_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs)
```

#### Stacked режим для mesh клонеров
```python
def _create_stacked_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs):
    """Создание mesh клонера в STACKED режиме"""
    
    # 1. Создание коллекции ClonerTo
    cloner_collection = ensure_cloner_to_collection(f"ClonerTo_{target_obj.name}_{cloner_type}")
    
    # 2. Создание копии целевого объекта
    cloner_obj = target_obj.copy()
    cloner_obj.data = target_obj.data.copy()
    cloner_collection.objects.link(cloner_obj)
    
    # 3. Предварительная обработка mesh (для Object клонера)
    if cloner_type == "OBJECT":
        calculate_edge_normals(cloner_obj)  # Важно для правильной работы
    
    # 4. Создание node group и модификатора
    node_group, socket_mapping = logic_function()
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}_Cloner", type='NODES')
    modifier.node_group = node_group
    
    # 5. Настройка параметров
    _setup_mesh_cloner_parameters(modifier, cloner_type, socket_mapping, "STACKED", **kwargs)
    
    return modifier
```

### Специализированные особенности mesh системы

#### Object Cloner - Обработка edge нормалей
```python
def calculate_edge_normals(obj):
    """Предварительное вычисление нормалей рёбер для Object клонера"""
    if obj.type != 'MESH':
        return
    
    # Переключение в Edit Mode для расчёта
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Рассчёт нормалей
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.normals_make_consistent(inside=False)
    
    bpy.ops.object.mode_set(mode='OBJECT')
```

#### Socket mapping система
```python
def _setup_mesh_cloner_parameters(modifier, cloner_type, socket_mapping, cloner_mode="STACKED", **kwargs):
    """Настройка параметров mesh клонера через socket mapping"""
    
    # Дефолтные значения для Object клонера
    if cloner_type == "OBJECT":
        defaults = {
            "Distribution Mode": 0,  # Vertices
            "Instance Scale": (1.0, 1.0, 1.0),
            "Align to Normal": True,
            "Source Mode": 1 if cloner_mode == "COLLECTION" else 0
        }
        
        # Применение дефолтов
        for param_name, default_value in defaults.items():
            _set_socket_value(modifier, param_name, default_value)
    
    # Применение пользовательских параметров
    for param_name, value in kwargs.items():
        _set_socket_value(modifier, param_name, value)
```

### Преимущества mesh системы

#### 1. Производительность
- **Прямое создание** без промежуточных wrapper слоёв
- **Оптимизированные geometry nodes** для mesh операций
- **Специализация** под конкретные типы геометрии

#### 2. Mesh-зависимая логика
- **Vertex/Edge/Face анализ** для точного позиционирования
- **Normal alignment** для реалистичного размещения
- **Adaptive distribution** в зависимости от геометрии

#### 3. Прямой контроль
- **Socket mapping** для точной настройки параметров
- **Immediate feedback** без дополнительных слоёв абстракции
- **Custom parameters** для каждого типа клонера

## Сравнение систем

### Когда использовать Unified систему

#### Идеальные сценарии:
- **Регулярные паттерны** (сетки, массивы, круги)
- **Архитектурное клонирование** (окна, колонны, структуры)
- **Безопасное клонирование** (когда важна защита от рекурсии)
- **Коллекционное клонирование** (группы объектов)
- **Простые сценарии** для начинающих пользователей

#### Преимущества:
```python
# Простота создания
cloner = create_cloner_unified("GRID", "OBJECT", source)

# Все режимы поддерживаются
cloner_obj = create_cloner_unified("LINEAR", "OBJECT", source)      # Новый объект
modifier = create_cloner_unified("LINEAR", "STACKED", source)       # Модификатор
collection_cloner = create_cloner_unified("CIRCLE", "COLLECTION", "MyCollection")
```

#### Ограничения:
- Менее оптимально для сложных mesh операций
- Больше слоёв абстракции
- Фиксированный набор паттернов распределения

### Когда использовать Mesh систему

#### Идеальные сценарии:
- **Клонирование по геометрии** (деревья на ландшафте)
- **Кривые и пути** (дороги, трубопроводы)
- **Поверхностное распределение** (детали на объектах)
- **Сложная геометрия** (объёмы, нерегулярные поверхности)
- **Производительность критична**

#### Преимущества:
```python
# Прямое создание для производительности
modifier = create_mesh_cloner("OBJECT", "STACKED", surface_mesh)

# Специализированные параметры
modifier = create_mesh_cloner(
    "OBJECT", 
    "STACKED", 
    target_obj,
    distribution_mode=2,    # Faces
    align_to_normal=True,
    face_center_mode=False
)
```

#### Ограничения:
- Больше сложности в настройке
- Требует понимания mesh структур
- Ограниченная поддержка режимов (в основном STACKED/COLLECTION)

## Внутренние различия реализации

### Unified: Template + Wrapper архитектура

```python
# Unified поток создания
create_cloner_unified("GRID", "OBJECT", source)
    ↓
_get_cloner_logic_function("GRID", "OBJECT")  # Получение create_grid_cloner_logic_group
    ↓
create_wrapper_with_template("GRID", logic_function, "", "OBJECT", True)
    ↓
apply_anti_recursion_architecture(wrapper_group, "")
    ↓
_create_object_mode_cloner(protected_wrapper, source, "GRID", True)
```

**Результат**: Объект с модификатором, содержащим wrapper node group с embedded logic и anti-recursion защитой.

### Mesh: Direct creation архитектура

```python
# Mesh поток создания
create_mesh_cloner("OBJECT", "STACKED", target)
    ↓
_get_mesh_cloner_logic_function("OBJECT")  # Получение create_object_cloner_logic_group
    ↓
create_object_cloner_logic_group()  # Прямое создание logic + socket_mapping
    ↓
_create_stacked_mesh_cloner("OBJECT", logic_function, target)
```

**Результат**: Копия объекта с модификатором, содержащим прямую logic без wrapper слоёв.

### Метаданные и UUID интеграция

#### Unified метаданные
```python
# Unified клонеры получают полный набор метаданных
{
    "cloner_type": "GRID",
    "cloner_mode": "OBJECT", 
    "cloner_system": "unified",
    "is_unified_cloner": True,
    "original_object": "Cube",
    "cloner_collection": "CLONERS_Cube",
    # UUID данные
    "cloner_uuid": "...",
    "chain_uuid": "...",
    # ... полный набор
}
```

#### Mesh метаданные
```python
# Mesh клонеры получают специализированные метаданные
{
    "cloner_type": "OBJECT",
    "cloner_mode": "STACKED",
    "cloner_system": "mesh", 
    "is_mesh_cloner": True,
    "target_object": "Landscape",  # Вместо original_object
    "cloner_collection": "ClonerTo_Landscape_OBJECT",
    # UUID данные аналогично
    "cloner_uuid": "...",
    "chain_uuid": "...",
}
```

## Производительность и оптимизация

### Unified система оптимизации

#### Anti-recursion overhead
```python
# Unified клонеры имеют дополнительный слой проверки
Wrapper Group
├── Anti-Recursion Logic (проверки зависимостей)
└── Embedded Logic Group (основная логика)
```

**Impact**: ~5-10% overhead, но полная защита от рекурсии.

#### Template caching
```python
# Unified система может кэшировать templates
# (временно отключено в текущей версии)
TEMPLATE_CACHE = {
    "GRID_OBJECT": cached_wrapper_group,
    "LINEAR_STACKED": cached_wrapper_group
}
```

### Mesh система оптимизации

#### Direct geometry nodes
```python
# Mesh клонеры работают напрямую с geometry nodes
Target Mesh → Mesh Analysis → Points Generation → Instance on Points → Result
```

**Impact**: Максимальная производительность, минимальный overhead.

#### Specialized mesh operations
```python
# Object клонер: оптимизированный анализ меша
def setup_mesh_analysis_nodes(node_group):
    # Прямая работа с vertex/edge/face данными
    # Без промежуточных конверсий
    # Оптимизированные normal calculations
```

### Рекомендации по производительности

#### Для больших сцен (>1000 инстансов)
```python
# Используйте mesh систему для поверхностного клонирования
object_cloner = create_mesh_cloner("OBJECT", "STACKED", large_landscape)

# Используйте unified систему для регулярных паттернов
grid_cloner = create_cloner_unified("GRID", "OBJECT", simple_object)
```

#### Для сложных цепочек клонеров
```python
# Unified система лучше подходит для цепочек
# благодаря встроенной UUID системе и chain management
original → unified_grid → unified_linear → unified_circle
```

#### Для простых задач
```python
# Mesh система быстрее для простого распределения
trees_on_landscape = create_mesh_cloner("OBJECT", "STACKED", landscape)
```

## Лучшие практики

### Выбор системы

#### Схема принятия решений:
```
Нужно ли клонирование по геометрии? 
    ├─ Да → Mesh система
    └─ Нет → Нужны ли регулярные паттерны?
        ├─ Да → Unified система
        └─ Нет → Определить по сложности:
            ├─ Простое → Mesh (производительность)
            └─ Сложное → Unified (безопасность)
```

### Смешанное использование

#### Правильные комбинации:
```python
# 1. Ландшафт с деревьями (Mesh) + архитектура (Unified)
landscape_trees = create_mesh_cloner("OBJECT", "STACKED", terrain)
building_windows = create_cloner_unified("GRID", "OBJECT", window)

# 2. Дорога (Mesh) + регулярные структуры (Unified)
road_lamps = create_mesh_cloner("SPLINE", "STACKED", road_curve)
lamp_details = create_cloner_unified("CIRCLE", "COLLECTION", "LampParts")
```

#### Интеграция в цепочки:
```python
# Смешанные цепочки поддерживаются через UUID систему
original_cube = create_cube()
grid_cloner = create_cloner_unified("GRID", "OBJECT", original_cube)      # Unified
object_cloner = create_mesh_cloner("OBJECT", "STACKED", grid_cloner)      # Mesh
linear_cloner = create_cloner_unified("LINEAR", "OBJECT", grid_cloner)    # Unified снова
```

### Отладка и диагностика

#### Система-специфичная диагностика:
```python
def diagnose_cloner_system(obj):
    """Диагностика с учётом системы клонера"""
    from core.core import get_cloner_modifier, get_cloner_info
    
    modifier = get_cloner_modifier(obj)
    if not modifier:
        return "Не клонер"
    
    info = get_cloner_info(modifier)
    system = info['system']
    
    if system == 'unified':
        # Проверка wrapper архитектуры
        if modifier.node_group:
            wrapper_nodes = len(modifier.node_group.nodes)
            has_embedded = any("EmbeddedLogic" in node.name for node in modifier.node_group.nodes)
            print(f"Unified клонер: {wrapper_nodes} нодов, embedded: {has_embedded}")
    
    elif system == 'mesh':
        # Проверка прямой архитектуры
        if modifier.node_group:
            direct_nodes = len(modifier.node_group.nodes)
            print(f"Mesh клонер: {direct_nodes} нодов прямой логики")
    
    return f"{info['type']} ({system})"
```

Понимание этих различий позволяет эффективно использовать каждую систему по назначению и создавать оптимальные решения для конкретных задач клонирования.