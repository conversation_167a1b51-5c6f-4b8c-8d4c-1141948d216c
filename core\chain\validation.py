"""
Chain Validation - Валидация и ремонт цепей клонеров
"""

import bpy
from ...core.core import get_cloner_modifier, get_cloner_info, set_cloner_metadata
from .utils import chain_debug_print


def validate_and_repair_chains(context):
    """
    Проверить и восстановить все цепи в сцене
    Функция для автоматической проверки целостности
    """
    # Тихий режим - минимум логов
    repair_count = 0
    
    # Находим все клонеры в сцене  
    all_cloners = []
    for obj in context.scene.objects:
        modifier = get_cloner_modifier(obj)
        if modifier:
            cloner_info = get_cloner_info(modifier)
            if cloner_info.get("is_chained", False):
                all_cloners.append(obj)
    
    if not all_cloners:
        return True
    
    # Дополнительно: умное восстановление сломанных источников
    try:
        from .smart_recovery import SmartChainRecovery
        for obj in context.scene.objects:
            modifier = get_cloner_modifier(obj)
            if modifier:
                if SmartChainRecovery.smart_repair_broken_source(obj, modifier):
                    repair_count += 1
    except:
        pass
    
    # Группируем по chain_source
    chains = {}
    for cloner_obj in all_cloners:
        modifier = get_cloner_modifier(cloner_obj)
        cloner_info = get_cloner_info(modifier)
        
        chain_source = cloner_info.get("chain_source_object", "")
        if not chain_source:
            chain_source = cloner_info.get("chain_source_collection", "")
        
        if chain_source not in chains:
            chains[chain_source] = []
        chains[chain_source].append(cloner_obj)
    
    # Проверяем каждую цепь
    repair_count = 0
    for chain_source, chain_cloners in chains.items():
        chain_debug_print(2, f"🔗 [CHAIN] Validating chain from {chain_source} ({len(chain_cloners)} cloners)")
        
        # Сортируем по chain_index
        chain_cloners.sort(key=lambda obj: get_cloner_info(get_cloner_modifier(obj)).get("chain_index", 0))
        
        # Проверяем связи между соседними клонерами
        for i, cloner_obj in enumerate(chain_cloners):
            expected_previous = chain_cloners[i-1] if i > 0 else None
            
            modifier = get_cloner_modifier(cloner_obj)
            cloner_info = get_cloner_info(modifier)
            actual_previous_name = cloner_info.get("previous_cloner", "")
            
            expected_previous_name = expected_previous.name if expected_previous else ""
            
            if actual_previous_name != expected_previous_name:
                print(f"🔧 [CHAIN] Repairing {cloner_obj.name}: {actual_previous_name} -> {expected_previous_name}")
                
                # Обновляем метаданные
                set_cloner_metadata(
                    modifier,
                    cloner_info.get("type", "UNKNOWN"),
                    cloner_info.get("mode", "OBJECT"),
                    is_chained=True,
                    previous_cloner=expected_previous_name,
                    chain_index=i
                )
                
                # Переподключаем если нужно
                if expected_previous:
                    from .reconnection import reconnect_cloner_to_source
                    reconnect_cloner_to_source(context, cloner_obj, expected_previous)
                
                repair_count += 1
    
    print(f"✅ [CHAIN] Chain validation complete: {repair_count} repairs made")
    return repair_count == 0  # True если ремонт не понадобился


def validate_uuid_chains(context):
    """Валидация UUID цепей"""
    try:
        from ..uuid.manager import BlenderClonerUUIDManager
        
        print("🔍 [CHAIN] Validating UUID chains...")
        
        # Сканируем все UUID клонеры
        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        
        uuid_cloners = scan_result.get("cloners_by_uuid", {})
        chains_by_uuid = scan_result.get("chains_by_uuid", {})
        orphaned_cloners = scan_result.get("orphaned_cloners", [])
        
        print(f"🔍 [CHAIN] Found {len(uuid_cloners)} UUID cloners in {len(chains_by_uuid)} chains")
        print(f"🔍 [CHAIN] Found {len(orphaned_cloners)} orphaned cloners")
        
        repair_count = 0
        
        # Ремонтируем orphaned клонеры
        for cloner_obj, cloner_modifier in orphaned_cloners:
            print(f"🔧 [CHAIN] Repairing orphaned cloner: {cloner_obj.name}")
            try:
                repair_count += repair_orphaned_uuid_cloner(cloner_obj, cloner_modifier)
            except Exception as e:
                print(f"⚠️ [CHAIN] Error repairing {cloner_obj.name}: {e}")
        
        # Валидируем цепи
        for chain_uuid, chain_cloners in chains_by_uuid.items():
            print(f"🔍 [CHAIN] Validating chain {chain_uuid} ({len(chain_cloners)} cloners)")
            try:
                repair_count += validate_single_uuid_chain(chain_uuid, chain_cloners)
            except Exception as e:
                print(f"⚠️ [CHAIN] Error validating chain {chain_uuid}: {e}")
        
        print(f"✅ [CHAIN] UUID chain validation complete: {repair_count} repairs made")
        return repair_count == 0
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available for validation")
        return True
    except Exception as e:
        print(f"❌ [CHAIN] Error in UUID chain validation: {e}")
        return False


def repair_orphaned_uuid_cloner(cloner_obj, cloner_modifier):
    """Ремонт orphaned UUID клонера"""
    repairs_made = 0
    
    # Проверяем UUID связи
    previous_uuid = cloner_modifier.get("previous_cloner_uuid", "")
    next_uuids = cloner_modifier.get("next_cloner_uuids", "").split(",")
    next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
    
    from ..uuid.manager import BlenderClonerUUIDManager
    
    # Проверяем previous связь
    if previous_uuid:
        prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
        if not prev_obj:
            print(f"🔧 [CHAIN] Removing broken previous UUID: {previous_uuid}")
            cloner_modifier["previous_cloner_uuid"] = ""
            repairs_made += 1
    
    # Проверяем next связи
    valid_next_uuids = []
    for next_uuid in next_uuids:
        next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
        if next_obj:
            valid_next_uuids.append(next_uuid)
        else:
            print(f"🔧 [CHAIN] Removing broken next UUID: {next_uuid}")
            repairs_made += 1
    
    if len(valid_next_uuids) != len(next_uuids):
        cloner_modifier["next_cloner_uuids"] = ",".join(valid_next_uuids)
    
    return repairs_made


def validate_single_uuid_chain(chain_uuid, chain_cloners):
    """Валидация отдельной UUID цепи"""
    repairs_made = 0
    
    if len(chain_cloners) < 2:
        return 0  # Цепь из одного клонера не требует валидации
    
    # Сортируем по sequence
    sorted_cloners = sorted(chain_cloners, key=lambda x: x[1].get("chain_sequence", 0))
    
    # Проверяем связи между соседними клонерами
    for i, (cloner_obj, cloner_modifier) in enumerate(sorted_cloners):
        expected_previous_uuid = ""
        expected_next_uuids = []
        
        # Определяем ожидаемые связи
        if i > 0:
            prev_obj, prev_modifier = sorted_cloners[i-1]
            expected_previous_uuid = prev_modifier.get("cloner_uuid", "")
        
        if i < len(sorted_cloners) - 1:
            next_obj, next_modifier = sorted_cloners[i+1]
            expected_next_uuids = [next_modifier.get("cloner_uuid", "")]
        
        # Проверяем actual связи
        actual_previous_uuid = cloner_modifier.get("previous_cloner_uuid", "")
        actual_next_uuids = cloner_modifier.get("next_cloner_uuids", "").split(",")
        actual_next_uuids = [uuid.strip() for uuid in actual_next_uuids if uuid.strip()]
        
        # Ремонтируем previous связь
        if actual_previous_uuid != expected_previous_uuid:
            print(f"🔧 [CHAIN] Fixing previous UUID for {cloner_obj.name}: {actual_previous_uuid} -> {expected_previous_uuid}")
            cloner_modifier["previous_cloner_uuid"] = expected_previous_uuid
            repairs_made += 1
        
        # Ремонтируем next связи
        if set(actual_next_uuids) != set(expected_next_uuids):
            print(f"🔧 [CHAIN] Fixing next UUIDs for {cloner_obj.name}: {actual_next_uuids} -> {expected_next_uuids}")
            cloner_modifier["next_cloner_uuids"] = ",".join(expected_next_uuids)
            repairs_made += 1
        
        # Проверяем sequence
        expected_sequence = i
        actual_sequence = cloner_modifier.get("chain_sequence", -1)
        if actual_sequence != expected_sequence:
            print(f"🔧 [CHAIN] Fixing sequence for {cloner_obj.name}: {actual_sequence} -> {expected_sequence}")
            cloner_modifier["chain_sequence"] = expected_sequence
            repairs_made += 1
    
    return repairs_made


def diagnose_chain_issues(context):
    """Диагностика проблем с цепями"""
    print("🔍 [CHAIN] Diagnosing chain issues...")
    
    issues = {
        "broken_connections": [],
        "missing_sources": [],
        "orphaned_cloners": [],
        "uuid_mismatches": [],
        "sequence_errors": []
    }
    
    # Сканируем все клонеры
    for obj in context.scene.objects:
        modifier = get_cloner_modifier(obj)
        if not modifier:
            continue
        
        cloner_info = get_cloner_info(modifier)
        if not cloner_info:
            continue
        
        # Проверяем источники
        chain_source_obj = cloner_info.get("chain_source_object", "")
        if chain_source_obj and chain_source_obj not in bpy.data.objects:
            issues["missing_sources"].append({
                "cloner": obj.name,
                "missing_source": chain_source_obj,
                "type": "object"
            })
        
        chain_source_coll = cloner_info.get("chain_source_collection", "")
        if chain_source_coll and chain_source_coll not in bpy.data.collections:
            issues["missing_sources"].append({
                "cloner": obj.name,
                "missing_source": chain_source_coll,
                "type": "collection"
            })
        
        # Проверяем UUID связи если есть
        if cloner_info.get("has_uuid", False):
            previous_uuid = modifier.get("previous_cloner_uuid", "")
            if previous_uuid:
                try:
                    from ..uuid.manager import BlenderClonerUUIDManager
                    prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
                    if not prev_obj:
                        issues["broken_connections"].append({
                            "cloner": obj.name,
                            "broken_uuid": previous_uuid,
                            "type": "previous"
                        })
                except ImportError:
                    pass
    
    # Печатаем отчет
    total_issues = sum(len(issue_list) for issue_list in issues.values())
    print(f"🔍 [CHAIN] Diagnosis complete: {total_issues} issues found")
    
    for issue_type, issue_list in issues.items():
        if issue_list:
            print(f"  {issue_type}: {len(issue_list)} issues")
            for issue in issue_list[:3]:  # Показываем только первые 3
                print(f"    - {issue}")
            if len(issue_list) > 3:
                print(f"    ... and {len(issue_list) - 3} more")
    
    return issues


def repair_all_chain_issues(context):
    """Ремонт всех обнаруженных проблем с цепями"""
    print("🔧 [CHAIN] Starting comprehensive chain repair...")
    
    total_repairs = 0
    
    try:
        # 1. Валидация legacy цепей
        legacy_repairs = validate_and_repair_chains(context)
        if not legacy_repairs:
            total_repairs += 1
        
        # 2. Валидация UUID цепей
        uuid_repairs = validate_uuid_chains(context)
        if not uuid_repairs:
            total_repairs += 1
        
        # 3. Диагностика и специфичный ремонт
        issues = diagnose_chain_issues(context)
        
        # Ремонтируем missing sources через fallback
        for issue in issues["missing_sources"]:
            try:
                print(f"🔧 [CHAIN] Attempting to repair missing source for {issue['cloner']}")
                total_repairs += repair_missing_source(context, issue)
            except Exception as e:
                print(f"⚠️ [CHAIN] Error repairing missing source: {e}")
        
        print(f"✅ [CHAIN] Comprehensive repair complete: {total_repairs} repairs made")
        return total_repairs == 0
        
    except Exception as e:
        print(f"❌ [CHAIN] Error in comprehensive chain repair: {e}")
        return False


def repair_missing_source(context, issue):
    """Ремонт missing источника"""
    cloner_name = issue["cloner"]
    missing_source = issue["missing_source"]
    source_type = issue["type"]
    
    if cloner_name not in bpy.data.objects:
        return 0
    
    cloner_obj = bpy.data.objects[cloner_name]
    modifier = get_cloner_modifier(cloner_obj)
    if not modifier:
        return 0
    
    print(f"🔧 [CHAIN] Repairing missing {source_type} source '{missing_source}' for {cloner_name}")
    
    # Пытаемся найти альтернативный источник
    alternative_source = None
    
    if source_type == "object":
        # Ищем похожие объекты
        for obj in bpy.data.objects:
            if missing_source.lower() in obj.name.lower():
                alternative_source = obj
                break
    elif source_type == "collection":
        # Ищем похожие коллекции
        for coll in bpy.data.collections:
            if missing_source.lower() in coll.name.lower():
                alternative_source = coll
                break
    
    if alternative_source:
        print(f"🔧 [CHAIN] Found alternative source: {alternative_source.name}")
        from .reconnection import reconnect_to_source
        success = reconnect_to_source(cloner_obj, modifier, alternative_source)
        return 1 if success else 0
    else:
        print(f"⚠️ [CHAIN] No alternative source found for {missing_source}")
        return 0