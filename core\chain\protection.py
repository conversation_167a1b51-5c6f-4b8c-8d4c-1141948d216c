"""
Invisible Chain Protection - Невидимая защита цепей клонеров
Работает в фоне без интерфейса, только предотвращает случайную поломку цепей
"""

import bpy
from bpy.app.handlers import persistent
from ..core import get_cloner_modifier, get_cloner_info

class InvisibleChainProtection:
    """Невидимая система защиты цепей"""
    
    @staticmethod
    def is_source_for_cloners(obj):
        """Проверить, используется ли объект как источник для клонеров"""
        if not obj:
            return False, []
        
        dependent_cloners = []
        
        # Быстрый поиск зависимых клонеров
        for scene_obj in bpy.context.scene.objects:
            cloner_modifier = get_cloner_modifier(scene_obj)
            if cloner_modifier:
                cloner_info = get_cloner_info(cloner_modifier)
                
                # Проверяем различные типы источников
                chain_source_obj = cloner_info.get("chain_source_object", "")
                original_obj = cloner_modifier.get("original_object", "")
                
                if (chain_source_obj == obj.name or original_obj == obj.name):
                    dependent_cloners.append(scene_obj.name)
        
        return len(dependent_cloners) > 0, dependent_cloners
    
    @staticmethod
    def auto_validate_on_load():
        """Автоматическая валидация цепей при загрузке файла"""
        try:
            from .validation import validate_and_repair_chains
            context = bpy.context
            
            # Тихая валидация без вывода (только критичные ошибки)
            is_valid = validate_and_repair_chains(context)
            
            if not is_valid:
                print("🔧 [PROTECTION] Auto-repaired chain issues on file load")
                
        except Exception as e:
            # Не показываем ошибки пользователю, только в консоль разработчика
            pass


# Хандлер загрузки файла для автоматической валидации
@persistent
def auto_validate_chains_on_load(dummy):
    """Автоматическая валидация при загрузке файла"""
    # Небольшая задержка для полной загрузки
    bpy.app.timers.register(
        lambda: InvisibleChainProtection.auto_validate_on_load(), 
        first_interval=1.0
    )


def register_invisible_protection():
    """Регистрация невидимой защиты"""
    # Регистрируем только автоматическую валидацию при загрузке
    if auto_validate_chains_on_load not in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.append(auto_validate_chains_on_load)


def unregister_invisible_protection():
    """Отмена регистрации невидимой защиты"""
    if auto_validate_chains_on_load in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.remove(auto_validate_chains_on_load)