"""
Noise Effector Parameters для ClonerPro
Определения параметров для автогенерации UI
"""

# Определения групп параметров для Noise Effector
NOISE_EFFECTOR_PARAMETERS = {
    "Effector Settings": [
        {
            "name": "Enable",
            "socket_name": "Enable", 
            "type": "BOOL",
            "default": True,
            "description": "Enable/disable the effector"
        },
        {
            "name": "Strength", 
            "socket_name": "Strength",
            "type": "FLOAT", 
            "default": 1.0,
            "min": 0.0,
            "max": 2.0,
            "description": "Overall strength of the effect"
        }
    ],
    
    "Transform Parameters": [
        {
            "name": "Position",
            "socket_name": "Position",
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Noise-based position offset range"
        },
        {
            "name": "Rotation",
            "socket_name": "Rotation", 
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Noise-based rotation range"
        },
        {
            "name": "Scale",
            "socket_name": "Scale",
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Noise-based scale variation range"
        }
    ],
    
    "Noise Settings": [
        {
            "name": "Uniform Scale",
            "socket_name": "Uniform Scale",
            "type": "BOOL",
            "default": True,
            "description": "Use the same noise value for all scale axes"
        },
        {
            "name": "Symmetric Translation",
            "socket_name": "Symmetric Translation",
            "type": "BOOL",
            "default": False,
            "description": "Apply symmetric noise to position"
        },
        {
            "name": "Symmetric Rotation",
            "socket_name": "Symmetric Rotation",
            "type": "BOOL",
            "default": False,
            "description": "Apply symmetric noise to rotation"
        }
    ],
    
    "Noise Controls": [
        {
            "name": "Noise Scale",
            "socket_name": "Noise Scale",
            "type": "FLOAT",
            "default": 0.5,
            "min": 0.1,
            "max": 10.0,
            "description": "Overall scale of the noise pattern"
        },
        {
            "name": "Noise Detail",
            "socket_name": "Noise Detail",
            "type": "FLOAT",
            "default": 2.0,
            "min": 0.0,
            "max": 15.0,
            "description": "Amount of noise detail (number of octaves)"
        },
        {
            "name": "Noise Roughness",
            "socket_name": "Noise Roughness",
            "type": "FLOAT",
            "default": 0.5,
            "min": 0.0,
            "max": 1.0,
            "description": "Roughness of noise pattern"
        },
        {
            "name": "Noise Lacunarity",
            "socket_name": "Noise Lacunarity",
            "type": "FLOAT",
            "default": 2.0,
            "min": 0.0,
            "max": 10.0,
            "description": "Gap between noise frequencies"
        },
        {
            "name": "Noise Distortion",
            "socket_name": "Noise Distortion",
            "type": "FLOAT",
            "default": 0.0,
            "min": -10.0,
            "max": 10.0,
            "description": "Amount of noise distortion"
        }
    ],
    
    "Noise Position": [
        {
            "name": "Noise Position",
            "socket_name": "Noise Position",
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Offset position of noise pattern"
        },
        {
            "name": "Noise XYZ Scale",
            "socket_name": "Noise XYZ Scale",
            "type": "VECTOR",
            "default": (1.0, 1.0, 1.0),
            "description": "Per-axis scale of noise pattern"
        }
    ],
    
    "Animation": [
        {
            "name": "Speed",
            "socket_name": "Speed",
            "type": "FLOAT",
            "default": 0.0,
            "min": 0.0,
            "max": 10.0,
            "description": "Animation speed of noise pattern"
        },
        {
            "name": "Seed",
            "socket_name": "Seed",
            "type": "INT",
            "default": 0,
            "min": 0,
            "max": 10000,
            "description": "Seed for noise generation"
        }
    ]
}


def get_noise_effector_parameters():
    """Получить параметры Noise Effector"""
    return NOISE_EFFECTOR_PARAMETERS


