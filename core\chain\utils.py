"""
Chain Utilities - Вспомогательные утилиты для работы с цепочками
"""

import bpy


def chain_debug_print(level, message):
    """Print chain debug messages"""
    # Упрощенное логирование: всегда печатаем важные сообщения (level 1+)
    if level <= 1:
        print(message)


def force_viewport_update():
    """Принудительное обновление viewport"""
    for i in range(3):
        bpy.context.view_layer.update()
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            depsgraph = bpy.context.evaluated_depsgraph_get()
            depsgraph.update()