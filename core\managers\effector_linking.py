"""
Effector Linking Manager для ClonerPro
Прямая система связывания эффекторов с клонерами БЕЗ фабрик и абстракций
"""

import bpy


def link_effector_to_cloner_simple(obj, cloner_mod, effector_mod):
    """
    Улучшенная система связывания эффектора с клонером для ClonerPro
    Правильно обрабатывает архитектуру с анти-рекурсией
    """
    
    try:
        # ИСПРАВЛЕНИЕ: Дополнительные проверки безопасности
        if not obj or not cloner_mod or not effector_mod:
            print(f"[ERROR] Один из аргументов None: obj={obj}, cloner_mod={cloner_mod}, effector_mod={effector_mod}")
            return False
        
        # Проверяем, что объект все еще существует в сцене
        if obj.name not in bpy.context.scene.objects:
            print(f"[ERROR] Объект {obj.name} больше не существует в сцене")
            return False
        
        # Проверяем, что модификаторы все еще существуют на объекте
        if cloner_mod.name not in [m.name for m in obj.modifiers]:
            print(f"[ERROR] Модификатор клонера {cloner_mod.name} больше не существует на объекте")
            return False
            
        if effector_mod.name not in [m.name for m in obj.modifiers]:
            print(f"[ERROR] Модификатор эффектора {effector_mod.name} больше не существует на объекте")
            return False

        # 1. Проверяем типы модификаторов
        if cloner_mod.type != 'NODES' or effector_mod.type != 'NODES':
            print(f"[ERROR] Один из модификаторов не является node group")
            return False

        if not cloner_mod.node_group or not effector_mod.node_group:
            print(f"[ERROR] Отсутствует node group в одном из модификаторов")
            return False

        # 2. КРИТИЧЕСКИ ВАЖНО: Используем улучшенную проверку связывания для решения проблемы Undo
        from ...core.system.dependency_safety import is_effector_already_linked_properly
        
        if is_effector_already_linked_properly(cloner_mod.node_group, effector_mod.name):
            print(f"[WARNING] Эффектор {effector_mod.name} уже правильно связан с клонером {cloner_mod.name}")
            return True

        # 3. КРИТИЧЕСКИ ВАЖНО: Определяем архитектуру клонера
        cloner_group = cloner_mod.node_group
        
        # Проверяем, является ли это wrapper группой (содержит ObjectInfo + logic subgroup)
        logic_group_node = None
        object_info_node = None
        
        print(f"[DEBUG] Анализируем архитектуру клонера: {cloner_group.name}")
        for node in cloner_group.nodes:
            print(f"[DEBUG] Узел: {node.name}, тип: {node.type}")
            if node.type == 'GROUP' and hasattr(node, 'node_tree') and node.node_tree:
                print(f"[DEBUG] Subgroup: {node.node_tree.name}")
                # Ищем logic группу (содержит "Logic" в имени)
                if "Logic" in node.node_tree.name:
                    logic_group_node = node
                    print(f"[DEBUG] Найдена logic группа: {node.node_tree.name}")
            elif node.type == 'OBJECT_INFO':
                object_info_node = node
                print(f"[DEBUG] Найден ObjectInfo узел: {node.name}")
        
        print(f"[DEBUG] Logic group: {logic_group_node is not None}, ObjectInfo: {object_info_node is not None}")
        
        # Если найдена logic группа, значит это wrapper архитектура (Object/Stacked/Collection)
        if logic_group_node:
            print(f"[DEBUG] Обнаружена wrapper архитектура: logic группа {logic_group_node.node_tree.name}")
            
            # ЕДИНАЯ АРХИТЕКТУРА: Система цепочек эффекторов для всех режимов
            success = add_effector_to_chain_simple(cloner_group, effector_mod)
            if not success:
                print(f"[ERROR] Не удалось добавить эффектор в цепочку")
                return False
            
        else:
            # Старая архитектура - интегрируем напрямую в основную группу
            print(f"[DEBUG] Используется простая архитектура")
            
            # Ищем Group Input и Group Output в клонере
            group_input = None
            group_output = None
            
            for node in cloner_group.nodes:
                if node.type == 'GROUP_INPUT':
                    group_input = node
                elif node.type == 'GROUP_OUTPUT':
                    group_output = node
            
            if not group_input or not group_output:
                print(f"[ERROR] Не найдены Group Input/Output узлы в клонере")
                return False

            # Создаем Group узел для эффектора в клонере
            effector_node = cloner_group.nodes.new('GeometryNodeGroup')
            effector_node.node_tree = effector_mod.node_group
            effector_node.name = f"Effector_{effector_mod.name}"
            effector_node.location = (200, 0)

            # Ищем точку интеграции
            for link in cloner_group.links:
                if link.to_node == group_output and link.to_socket.name == "Geometry":
                    target_output_socket = link.from_socket
                    target_input_socket = link.to_socket
                    cloner_group.links.remove(link)
                    print(f"[DEBUG] Простая интеграция перед Group Output")
                    break
            
            if target_output_socket and target_input_socket:
                cloner_group.links.new(target_output_socket, effector_node.inputs["Geometry"])
                cloner_group.links.new(effector_node.outputs["Geometry"], target_input_socket)
                print(f"✅ Эффектор интегрирован в простую архитектуру")
            else:
                print(f"[ERROR] Не удалось найти точку интеграции")
                return False

        # 6. КРИТИЧЕСКИ ВАЖНО: Синхронизируем параметры и настраиваем live обновление
        if logic_group_node:
            # Новая wrapper архитектура - эффектор добавлен в цепочку через add_effector_to_chain_simple
            # Находим созданный узел эффектора в wrapper группе
            actual_effector_node = None
            for node in cloner_group.nodes:
                if (node.type == 'GROUP' and 
                    hasattr(node, 'name') and 
                    node.name.startswith("Effector_") and
                    effector_mod.name in node.name):
                    actual_effector_node = node
                    print(f"[DEBUG] Найден узел эффектора в wrapper: {node.name}")
                    break
            
            if actual_effector_node:
                sync_effector_parameters_simple(effector_mod, actual_effector_node)
                setup_live_sync_simple(effector_mod, actual_effector_node, cloner_mod)
                print(f"[DEBUG] Синхронизация параметров для wrapper архитектуры завершена")
            else:
                print(f"[ERROR] Не найден узел эффектора в wrapper группе для синхронизации")
        else:
            # Старая архитектура - прямая синхронизация
            # effector_node определен выше в этой же ветке кода
            if 'effector_node' in locals():
                sync_effector_parameters_simple(effector_mod, effector_node)
                setup_live_sync_simple(effector_mod, effector_node, cloner_mod)
            else:
                print(f"[ERROR] effector_node не определен для старой архитектуры")
        
        # 7. Включаем эффектор
        enable_effector_simple(effector_mod)
        
        # 8. Сохраняем метаданные
        if not cloner_group.get("linked_effectors"):
            cloner_group["linked_effectors"] = []
        
        linked_effectors = list(cloner_group.get("linked_effectors", []))
        if effector_mod.name not in linked_effectors:
            linked_effectors.append(effector_mod.name)
            cloner_group["linked_effectors"] = linked_effectors

        # 9. Обновляем dependency graph
        update_dependency_graph_simple(cloner_mod)

        print(f"✅ Эффектор {effector_mod.name} успешно связан с клонером {cloner_mod.name}")
        return True

    except Exception as e:
        print(f"❌ Ошибка связывания эффектора: {e}")
        return False


def is_effector_already_linked_simple(cloner_mod, effector_name):
    """
    Простая проверка, связан ли эффектор с клонером
    """
    if not cloner_mod.node_group:
        return False
    
    linked_effectors = cloner_mod.node_group.get("linked_effectors", [])
    return effector_name in linked_effectors


def enable_effector_simple(effector_mod):
    """
    Включить эффектор после связывания с клонером
    """
    try:
        if not effector_mod.node_group:
            return False
        
        # Включаем модификатор в viewport
        effector_mod.show_viewport = True
        
        # Устанавливаем Enable = True через сокет
        for socket in effector_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Enable":
                try:
                    effector_mod[socket.identifier] = True
                    print(f"✅ Включен эффектор {effector_mod.name}")
                    return True
                except Exception as e:
                    print(f"Warning: Could not enable effector: {e}")
        
        return False
        
    except Exception as e:
        print(f"Error enabling effector: {e}")
        return False


def auto_link_effector_to_cloners(context, effector_mod):
    """
    Автоматически связать эффектор со всеми клонерами
    ИСПРАВЛЕНО: Поддержка Object режима - поиск клонеров во всей сцене
    """
    obj = context.active_object
    if not obj:
        return False, "No active object"
    
    linked_count = 0
    
    # СНАЧАЛА: Ищем клонеры на том же объекте (Stacked/Collection режимы)
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group and mod != effector_mod:
            # Проверяем, является ли это клонером
            if is_cloner_modifier_simple(mod):
                success = link_effector_to_cloner_simple(obj, mod, effector_mod)
                if success:
                    linked_count += 1
    
    # ЕСЛИ НЕ НАЙДЕНО: Ищем клонеры связанные с этим объектом (Object режим)
    if linked_count == 0:
        linked_count += _find_and_link_object_mode_cloners(context, obj, effector_mod)
    
    if linked_count > 0:
        return True, f"Linked to {linked_count} cloner(s)"
    else:
        return False, "No cloners found on this object"


def _find_and_link_object_mode_cloners(context, source_obj, effector_mod):
    """
    Найти и связать клонеры Object режима, которые используют source_obj как источник
    """
    linked_count = 0
    
    # Ищем во всех объектах сцены клонеры, которые ссылаются на source_obj
    for scene_obj in context.scene.objects:
        if scene_obj == source_obj:
            continue
            
        for mod in scene_obj.modifiers:
            if mod.type == 'NODES' and mod.node_group and is_cloner_modifier_simple(mod):
                # Проверяем, использует ли этот клонер source_obj как источник
                if _cloner_uses_source_object(mod, source_obj):
                    # Создаем копию эффектора на объекте клонера
                    cloner_effector = _create_effector_copy_on_cloner_object(scene_obj, effector_mod)
                    if cloner_effector:
                        # Связываем эффектор с клонером
                        success = link_effector_to_cloner_simple(scene_obj, mod, cloner_effector)
                        if success:
                            linked_count += 1
                            print(f"[DEBUG] Object Mode: Linked effector to cloner {mod.name} on {scene_obj.name}")
    
    return linked_count


def _cloner_uses_source_object(cloner_mod, source_obj):
    """Проверить, использует ли клонер указанный объект как источник"""
    # Проверяем метаданные клонера
    original_object = cloner_mod.get("original_object")
    if original_object == source_obj.name:
        return True
    
    # Дополнительно проверяем через ObjectInfo узлы в node group
    if cloner_mod.node_group:
        for node in cloner_mod.node_group.nodes:
            if node.type == 'OBJECT_INFO':
                try:
                    # Проверяем через сокет Object
                    for socket in cloner_mod.node_group.interface.items_tree:
                        if socket.name == 'Object' and socket.in_out == 'INPUT':
                            object_ref = cloner_mod.get(socket.identifier)
                            if object_ref == source_obj:
                                return True
                except:
                    pass
    
    return False


def _create_effector_copy_on_cloner_object(cloner_obj, source_effector_mod):
    """Создать копию эффектора на объекте клонера"""
    try:
        # Создаем новый модификатор
        new_mod_name = f"{source_effector_mod.name}_Auto"
        
        # Проверяем, не существует ли уже такой модификатор
        counter = 1
        final_name = new_mod_name
        while final_name in [m.name for m in cloner_obj.modifiers]:
            counter += 1
            final_name = f"{new_mod_name}_{counter}"
        
        # Создаем модификатор
        new_modifier = cloner_obj.modifiers.new(name=final_name, type='NODES')
        new_modifier.node_group = source_effector_mod.node_group
        
        # Копируем все параметры
        if source_effector_mod.node_group:
            for socket in source_effector_mod.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    try:
                        source_value = source_effector_mod.get(socket.identifier)
                        if source_value is not None:
                            new_modifier[socket.identifier] = source_value
                    except:
                        pass
        
        print(f"[DEBUG] Created effector copy {final_name} on cloner object {cloner_obj.name}")
        return new_modifier
        
    except Exception as e:
        print(f"[ERROR] Failed to create effector copy: {e}")
        return None


def is_cloner_modifier_simple(modifier):
    """
    Простая проверка, является ли модификатор клонером
    """
    if not modifier.node_group:
        return False
    
    cloner_patterns = [
        "GridCloner3D_Advanced",
        "GridClonerStacked", 
        "GridClonerCollection",
        "LinearCloner3D_Advanced",
        "LinearClonerStacked",
        "LinearClonerCollection"
    ]
    
    node_group_name = modifier.node_group.name
    
    for pattern in cloner_patterns:
        if pattern in node_group_name:
            return True
    
    # Проверяем метаданные
    if modifier.get("cloner_type") or modifier.get("is_stacked_cloner"):
        return True
    
    return False


def sync_effector_parameters_simple(effector_mod, effector_node):
    """
    Улучшенная синхронизация параметров из модификатора в узел эффектора
    Теперь работает bidirectional - из модификатора в узел И из узла в модификатор
    """
    try:
        if not effector_mod.node_group or not effector_node.node_tree:
            print(f"[ERROR] Отсутствует node_group в модификаторе или узле")
            return
        
        synced_count = 0
        
        print(f"[DEBUG] Синхронизация между модификатором {effector_mod.name} и узлом {effector_node.name}")
        
        # Bidirectional синхронизация параметров
        for socket in effector_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name != 'Geometry':
                try:
                    # Находим соответствующий вход в узле
                    if socket.name in effector_node.inputs:
                        # Получаем значения из обеих сторон
                        modifier_value = effector_mod.get(socket.identifier)
                        node_value = effector_node.inputs[socket.name].default_value
                        
                        print(f"[DEBUG] {socket.name}: модификатор={modifier_value}, узел={node_value}")
                        
                        # Если значение в модификаторе установлено, используем его
                        if modifier_value is not None:
                            if modifier_value != node_value:
                                effector_node.inputs[socket.name].default_value = modifier_value
                                print(f"[DEBUG] Modif->Node: {socket.name} = {modifier_value}")
                                synced_count += 1
                        else:
                            # Если значение в модификаторе не установлено, используем значение из узла
                            if node_value is not None:
                                try:
                                    effector_mod[socket.identifier] = node_value
                                    print(f"[DEBUG] Node->Modif: {socket.name} = {node_value}")
                                    synced_count += 1
                                except Exception as e:
                                    print(f"[DEBUG] Не удалось установить {socket.name} в модификатор: {e}")
                                    # Используем default значение из интерфейса
                                    default_value = getattr(socket, 'default_value', None)
                                    if default_value is not None:
                                        effector_node.inputs[socket.name].default_value = default_value
                                        print(f"[DEBUG] Used default for {socket.name}: {default_value}")
                                        synced_count += 1
                    else:
                        print(f"[WARNING] Socket {socket.name} не найден в узле эффектора")
                except Exception as e:
                    print(f"[WARNING] Could not sync parameter {socket.name}: {e}")
        
        # Обновляем dependency graph для применения изменений
        if hasattr(bpy.context, 'view_layer'):
            bpy.context.view_layer.update()
        
        print(f"[DEBUG] Синхронизировано {synced_count} параметров эффектора")
                    
    except Exception as e:
        print(f"[ERROR] Error syncing effector parameters: {e}")
        import traceback
        traceback.print_exc()


def update_dependency_graph_simple(cloner_mod):
    """
    Обновляет dependency graph после изменений
    Точная копия логики из advanced_cloners
    """
    try:
        # Сбрасываем кэш для обновления всех зависимостей
        if cloner_mod.node_group:
            cloner_mod.node_group.update_tag()
        
        # Обновляем контекст
        import bpy
        bpy.context.view_layer.update()
        
        # Также запускаем синхронизацию эффекторов если есть связанные
        sync_all_linked_effectors(cloner_mod)
        
        print("✅ Dependency graph updated")
        
    except Exception as e:
        print(f"Warning: Could not update dependency graph: {e}")


def sync_all_linked_effectors(cloner_mod):
    """
    Синхронизирует все связанные эффекторы с их узлами
    """
    try:
        if not cloner_mod or not cloner_mod.node_group:
            return
        
        linked_effectors = cloner_mod.node_group.get("linked_effectors", [])
        if not linked_effectors:
            return
        
        # Получаем объект клонера
        cloner_obj = None
        for obj in bpy.context.scene.objects:
            for mod in obj.modifiers:
                if mod == cloner_mod:
                    cloner_obj = obj
                    break
            if cloner_obj:
                break
        
        if not cloner_obj:
            return
        
        # Синхронизируем каждый связанный эффектор
        for effector_name in linked_effectors:
            # Ищем модификатор эффектора на том же объекте
            effector_mod = None
            for mod in cloner_obj.modifiers:
                if mod.name == effector_name and mod.type == 'NODES':
                    effector_mod = mod
                    break
            
            if effector_mod:
                # Ищем узел эффектора в клонере
                effector_node = None
                for node in cloner_mod.node_group.nodes:
                    if (node.type == 'GROUP' and 
                        node.name.startswith("Effector_") and
                        effector_name in node.name):
                        effector_node = node
                        break
                
                if effector_node:
                    # Выполняем синхронизацию
                    sync_effector_parameters_simple(effector_mod, effector_node)
                    
    except Exception as e:
        print(f"[DEBUG] Ошибка синхронизации эффекторов: {e}")


def setup_live_sync_simple(effector_mod, effector_node, cloner_mod):
    """
    Настройка live синхронизации параметров эффектора
    Использует depsgraph_update_post handler для автоматической синхронизации
    """
    try:
        def sync_handler(scene, depsgraph):
            """КРИТИЧЕСКИ ИСПРАВЛЕННЫЙ Handler для автоматической синхронизации параметров"""
            try:
                # КРИТИЧНО: Безопасная проверка объектов через ReferenceError protection
                try:
                    # Проверяем валидность модификатора
                    if not effector_mod:
                        return
                    
                    # КРИТИЧНО: Проверяем, что модификатор не был удален
                    effector_name = effector_mod.name  # Может вызвать ReferenceError
                    
                    # КРИТИЧНО: Проверяем, что node_group не была удалена
                    if not effector_mod.node_group:
                        return
                    
                    node_group_name = effector_mod.node_group.name  # Может вызвать ReferenceError
                    
                    # Проверяем cloner_mod
                    if not cloner_mod:
                        return
                    
                    cloner_name = cloner_mod.name  # Может вызвать ReferenceError
                    
                    # Проверяем effector_node
                    if not effector_node or not effector_node.node_tree:
                        return
                    
                    effector_node_name = effector_node.name  # Может вызвать ReferenceError
                    
                except ReferenceError:
                    # КРИТИЧНО: Объект был удален - самоудаляем handler
                    print(f"[SAFETY] Handler обнаружил удаленный объект, самоудаление")
                    try:
                        if sync_handler in bpy.app.handlers.depsgraph_update_post:
                            bpy.app.handlers.depsgraph_update_post.remove(sync_handler)
                        if sync_handler in bpy.app.handlers.frame_change_post:
                            bpy.app.handlers.frame_change_post.remove(sync_handler)
                    except:
                        pass
                    return
                
                # КРИТИЧНО: Дополнительная проверка через safe operations
                from ...core.system.safe_operations import safe_access_node_group
                safe_node_group = safe_access_node_group(effector_mod)
                if not safe_node_group:
                    print(f"[SAFETY] Handler: NodeGroup недоступна для {effector_name}")
                    return
                
                # Синхронизация модификатор -> узел в реальном времени
                try:
                    for socket in safe_node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name != 'Geometry':
                            try:
                                if socket.name in effector_node.inputs:
                                    modifier_value = effector_mod.get(socket.identifier)
                                    if modifier_value is not None:
                                        current_node_value = effector_node.inputs[socket.name].default_value
                                        # Проверяем, изменилось ли значение в модификаторе
                                        if modifier_value != current_node_value:
                                            effector_node.inputs[socket.name].default_value = modifier_value
                            except ReferenceError:
                                # Node был удален
                                return
                            except Exception as sync_error:
                                pass  # Игнорируем ошибки синхронизации
                except ReferenceError:
                    # NodeGroup была удалена
                    print(f"[SAFETY] Handler: NodeGroup была удалена")
                    return
                    
            except ReferenceError as ref_error:
                # КРИТИЧНО: Любая ReferenceError означает что объект удален
                print(f"[SAFETY] Handler: ReferenceError - самоудаление handler")
                try:
                    if sync_handler in bpy.app.handlers.depsgraph_update_post:
                        bpy.app.handlers.depsgraph_update_post.remove(sync_handler)
                    if sync_handler in bpy.app.handlers.frame_change_post:
                        bpy.app.handlers.frame_change_post.remove(sync_handler)
                except:
                    pass
                return
            except Exception as handler_error:
                # Безопасно игнорируем остальные ошибки
                pass
        
        # Сохраняем информацию о связке для handler
        sync_handler.effector_name = effector_mod.name
        sync_handler.cloner_name = cloner_mod.name if cloner_mod else "unknown"
        
        # Убираем старые handlers для этого эффектора (если есть)
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, 'effector_name') and 
                hasattr(handler, 'cloner_name') and
                handler.effector_name == effector_mod.name):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
            print(f"[DEBUG] Удален старый handler для {handler.effector_name}")
        
        # Добавляем новый handler
        bpy.app.handlers.depsgraph_update_post.append(sync_handler)
        
        # ДОПОЛНИТЕЛЬНО: Добавляем handler на frame_change для еще более надежной синхронизации
        if sync_handler not in bpy.app.handlers.frame_change_post:
            bpy.app.handlers.frame_change_post.append(sync_handler)
        
        print(f"✅ Настроена live синхронизация для эффектора {effector_mod.name}")
        print(f"[DEBUG] Добавлены handlers: depsgraph_update_post и frame_change_post")
        
    except Exception as e:
        print(f"[WARNING] Не удалось настроить live синхронизацию: {e}")
        import traceback
        traceback.print_exc()


def add_effector_to_chain_simple(wrapper_group, effector_mod):
    """
    Добавляет эффектор в цепочку эффекторов wrapper группы
    Портированная логика из advanced_cloners/core/utils/effector_management/effector_chains.py
    """
    try:
        if not wrapper_group.get("supports_effector_chains", False):
            print(f"[ERROR] Wrapper группа не поддерживает цепочки эффекторов")
            return False
        
        # Получаем метаданные цепочки
        chain_order = list(wrapper_group.get("effector_chain_order", []))
        nodes_map = dict(wrapper_group.get("effector_nodes_map", {}))
        last_index = wrapper_group.get("last_effector_index", 0)
        
        # ИСПРАВЛЕНИЕ: Определяем корректный индекс эффектора
        # Если цепочка была сброшена, используем последний известный индекс
        if len(chain_order) == 0 and last_index == 0:
            # Проверяем, есть ли уже эффекторы в группе (если цепочка была сброшена)
            existing_effector_count = 0
            max_index = 0
            for node in wrapper_group.nodes:
                if node.type == 'GROUP' and "Effector_" in node.name:
                    existing_effector_count += 1
                    # Извлекаем индекс из имени узла
                    try:
                        parts = node.name.split("_")
                        if len(parts) >= 2 and parts[1].isdigit():
                            index = int(parts[1])
                            max_index = max(max_index, index)
                    except:
                        pass
            
            # Используем следующий доступный индекс
            effector_index = max(last_index, max_index) + 1
            print(f"[DEBUG] Обнаружено {existing_effector_count} эффекторов, используем индекс {effector_index}")
        else:
            effector_index = last_index + 1
        
        effector_node_name = f"Effector_{effector_index:03d}_{effector_mod.name}"
        print(f"[DEBUG] Создаем узел эффектора: {effector_node_name}")
        
        # Создаем узел эффектора
        effector_node = wrapper_group.nodes.new('GeometryNodeGroup')
        effector_node.node_tree = effector_mod.node_group
        effector_node.name = effector_node_name
        effector_node.location = (300 + effector_index * 200, 0)
        
        # Определяем точку вставки
        if len(chain_order) == 0:
            # Первый эффектор - подключаем к Global Transform или Logic группе
            global_transform = None
            cloner_logic_node = None
            
            for node in wrapper_group.nodes:
                print(f"[DEBUG] Проверяем узел: {node.name}, тип: {node.type}")
                if node.type == 'TRANSFORM_GEOMETRY':
                    global_transform = node
                    print(f"[DEBUG] Найден Global Transform: {node.name}")
                elif node.type == 'GROUP' and hasattr(node, 'node_tree') and node.node_tree:
                    print(f"[DEBUG] Группа: {node.name}, node_tree: {node.node_tree.name}")
                    if "Logic" in node.node_tree.name:
                        cloner_logic_node = node
                        print(f"[DEBUG] Найден Logic узел: {node.name}, node_tree: {node.node_tree.name}")
            
            print(f"[DEBUG] Global Transform найден: {global_transform is not None}")
            print(f"[DEBUG] Logic узел найден: {cloner_logic_node is not None}")
            
            if global_transform:
                # Object/Collection режимы - подключаем к Global Transform
                previous_output = global_transform.outputs['Geometry']
                print(f"[DEBUG] Первый эффектор: подключение к Global Transform")
            elif cloner_logic_node:
                # Stacked режим - подключаем к Logic группе
                previous_output = cloner_logic_node.outputs['Geometry']
                print(f"[DEBUG] Первый эффектор: подключение к Logic группе (Stacked режим)")
            else:
                print(f"[ERROR] Не найдена точка подключения для первого эффектора")
                print(f"[ERROR] Global Transform: {global_transform}")
                print(f"[ERROR] Logic узел: {cloner_logic_node}")
                return False
        else:
            # Последующие эффекторы - к последнему в цепочке
            last_effector_name = chain_order[-1]
            last_node_name = nodes_map.get(last_effector_name)
            
            # КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем, существует ли узел
            if not last_node_name or last_node_name not in wrapper_group.nodes:
                print(f"[WARNING] Последний узел {last_node_name} не найден, сбрасываем цепочку")
                # Сбрасываем метаданные цепочки и подключаемся как первый эффектор
                wrapper_group["effector_chain_order"] = []
                wrapper_group["effector_nodes_map"] = {}
                wrapper_group["last_effector_index"] = 0
                
                # Повторяем логику для первого эффектора
                global_transform = None
                cloner_logic_node = None
                
                for node in wrapper_group.nodes:
                    if node.type == 'TRANSFORM_GEOMETRY':
                        global_transform = node
                        break
                    elif node.type == 'GROUP' and hasattr(node, 'node_tree') and node.node_tree:
                        if "Logic" in node.node_tree.name:
                            cloner_logic_node = node
                            break
                
                if global_transform:
                    previous_output = global_transform.outputs['Geometry']
                    print(f"[DEBUG] Сброшена цепочка, подключение к Global Transform")
                elif cloner_logic_node:
                    previous_output = cloner_logic_node.outputs['Geometry']
                    print(f"[DEBUG] Сброшена цепочка, подключение к Logic группе")
                else:
                    print(f"[ERROR] Не найдена точка подключения после сброса цепочки")
                    return False
                
                # Обновляем переменные для корректного добавления
                chain_order = []
                nodes_map = {}
                last_index = 0
            else:
                last_node = wrapper_group.nodes[last_node_name]
                previous_output = last_node.outputs['Geometry']
                print(f"[DEBUG] Добавляем к цепочке после {last_effector_name}")
        
        # Определяем точку продолжения (Final Realize Instances)
        final_realize = None
        for node in wrapper_group.nodes:
            if node.type == 'REALIZE_INSTANCES' and "Final Realize" in node.name:
                final_realize = node
                break
        
        if not final_realize:
            print(f"[ERROR] Final Realize Instances не найден")
            return False
        
        next_input = final_realize.inputs['Geometry']
        
        # Удаляем старую связь
        target_link = None
        for link in wrapper_group.links:
            if link.from_socket == previous_output and link.to_socket == next_input:
                target_link = link
                break
        
        if target_link:
            wrapper_group.links.remove(target_link)
        
        # Подключаем эффектор
        wrapper_group.links.new(previous_output, effector_node.inputs['Geometry'])
        wrapper_group.links.new(effector_node.outputs['Geometry'], next_input)
        
        # Обновляем метаданные
        chain_order.append(effector_mod.name)
        nodes_map[effector_mod.name] = effector_node_name
        
        wrapper_group["effector_chain_order"] = chain_order
        wrapper_group["effector_nodes_map"] = nodes_map
        wrapper_group["last_effector_index"] = effector_index
        
        # Обновляем список связанных эффекторов
        linked_effectors = list(wrapper_group.get("linked_effectors", []))
        if effector_mod.name not in linked_effectors:
            linked_effectors.append(effector_mod.name)
            wrapper_group["linked_effectors"] = linked_effectors
        
        # Синхронизируем параметры
        sync_effector_parameters_simple(effector_mod, effector_node)
        setup_live_sync_simple(effector_mod, effector_node, None)
        
        print(f"✅ Эффектор {effector_mod.name} добавлен в цепочку как {effector_node_name}")
        return True
        
    except Exception as e:
        print(f"[ERROR] Ошибка добавления эффектора в цепочку: {e}")
        import traceback
        traceback.print_exc()
        return False


def get_linked_cloners_for_effector(context, effector_mod):
    """
    Получить список клонеров, связанных с эффектором
    """
    linked_cloners = []
    
    for obj in context.scene.objects:
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                linked_effectors = mod.node_group.get("linked_effectors", [])
                if effector_mod.name in linked_effectors:
                    linked_cloners.append((obj, mod))
    
    return linked_cloners


