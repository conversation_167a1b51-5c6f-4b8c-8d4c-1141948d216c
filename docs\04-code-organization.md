# Организация кода ClonerPro

## Общий обзор структуры

ClonerPro организован по модульному принципу с чётким разделением ответственности между компонентами. Структура спроектирована для максимальной читаемости, расширяемости и сопровождения.

## Корневая структура

```
ClonerPro/
├── __init__.py                 # Главный файл аддона и регистрация
├── components/                 # Реализации клонеров, эффекторов, филдов
├── config/                     # Конфигурации и параметры компонентов
├── core/                       # Центральная логика и системы
├── ui/                         # Пользовательский интерфейс
└── docs/                       # Документация проекта
```

### Принципы организации

1. **Разделение по функциональности** - Каждый модуль отвечает за конкретную область
2. **Слоистая архитектура** - Чёткая иерархия зависимостей: UI → Core → Components
3. **Модульность** - Независимые компоненты с минимальными связями
4. **Расширяемость** - Простое добавление новых типов клонеров и эффекторов

## Детальная структура модулей

### 📂 core/ - Центральная логика

#### Основные файлы
```
core/
├── core.py                     # Константы, утилиты, базовые функции
├── __init__.py                 # Экспорт публичного API
```

**core.py** - Сердце системы:
```python
# Ключевые константы
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE", "SPIRAL"}
MESH_CLONERS = {"OBJECT", "CURVES", "VOLUME", "SPLINE"}
CREATION_MODES = {'OBJECT': 'object', 'STACKED': 'stacked', 'COLLECTION': 'collection'}

# Критически важные функции
def get_cloner_system(cloner_type: str) -> str
def get_cloner_modifier(obj)
def set_cloner_metadata(modifier, cloner_type, mode, **kwargs)
def get_cloner_info(modifier) -> dict
```

#### 📂 core/templates/ - Единая система создания
```
templates/
├── cloner_creation.py          # **ЕДИНЫЙ API для всех клонеров**
├── anti_recursion.py           # Защита от рекурсии (DEPRECATED)
├── unified_creation.py         # Legacy unified (для совместимости)
├── mesh_creation.py           # Legacy mesh (для совместимости)
└── cloner_templates.py        # Legacy wrapper (для совместимости)
```

**cloner_creation.py** - **ЕДИНАЯ ТОЧКА ВХОДА**:
```python
# Главная функция для ВСЕХ клонеров
def create_cloner_unified(cloner_type, mode, source, use_anti_recursion=True, mesh_stacked=False):
    """Единый API создания для ВСЕХ типов клонеров с автоматической маршрутизацией"""
    
    # Автоматическое определение системы
    if cloner_type in ["OBJECT", "SPLINE"]:
        return _create_mesh_cloner_via_mesh_system(...)
    elif cloner_type in ["GRID", "LINEAR", "CIRCLE"]:
        return _create_unified_cloner_via_unified_system(...)

# Внутренние функции маршрутизации
def _create_mesh_cloner_via_mesh_system(cloner_instance, mode, source, ...)
def _create_unified_cloner_via_unified_system(cloner_instance, mode, source, ...)
def _get_cloner_instance(cloner_type)  # Получение BaseCloner наследника
```

**Прямые API (опциональные для обратной совместимости)**:
```python
# Прямой доступ к mesh системе
def create_mesh_cloner(cloner_type, mode, target_obj=None, **kwargs)

# Прямой доступ к unified системе  
def create_unified_cloner(cloner_type, mode, source, **kwargs)
```

#### 📂 core/uuid/ - UUID система
```
uuid/
├── manager.py                  # Основной UUID менеджер
└── chain_management.py         # Управление цепочками
```

**manager.py** - UUID менеджер:
```python
class BlenderClonerUUIDManager:
    @staticmethod
    def generate_cloner_uuid() -> str
    
    @staticmethod 
    def set_cloner_uuid_metadata(modifier, cloner_type: str, mode: str, **kwargs)
    
    @staticmethod
    def find_cloner_by_uuid(cloner_uuid: str)
    
    @staticmethod
    def scan_all_cloners_with_uuid() -> Dict
```

#### 📂 core/chain/ - Chain management
```
chain/
├── detection.py               # Автоматическое обнаружение цепочек
├── restoration.py             # Восстановление связей
├── metadata.py                # Управление метаданными цепочек
├── reconnection.py            # Переподключение связей
├── smart_recovery.py          # Умное восстановление
├── source_discovery.py        # Обнаружение источников
├── utils.py                   # Утилиты для цепочек
├── validation.py              # Валидация цепочек
├── deletion.py                # Безопасное удаление
└── protection.py              # Защита цепочек
```

#### 📂 core/managers/ - Менеджеры ресурсов
```
managers/
├── cloner_modes.py            # Маршрутизация между системами
├── object_creation.py         # Создание объектов и коллекций
├── effector_creation.py       # Создание эффекторов
└── effector_linking.py        # Связывание эффекторов с клонерами
```

#### 📂 core/system/ - Системы безопасности
```
system/
├── dependency_safety.py       # Безопасность зависимостей
├── event_handling.py          # Обработка событий Blender
├── error_handling.py          # Обработка ошибок
├── safe_operations.py         # Безопасные операции
├── settings.py                # Системные настройки
├── validation.py              # Валидация данных
└── cache_manager.py           # Управление кэшем
```

#### 📂 core/services/ - Сервисы
```
services/
└── cloner_scanner.py          # Сканирование клонеров в сцене
```

#### 📂 core/cleanup/ - Очистка ресурсов
```
cleanup/
└── effector_cleanup.py       # Очистка эффекторов
```

### 📂 components/ - Реализации компонентов

#### 📂 components/ - Базовые классы
```
components/
├── __init__.py                # Экспорт базовых классов
├── base.py                    # BaseComponent
└── base_cloner.py             # **BaseCloner - базовый класс всех клонеров**
```

**base_cloner.py** - **Центральный класс архитектуры**:
```python
class BaseCloner(BaseComponent):
    """Базовый класс для всех клонеров ClonerPro"""
    
    def create_node_group(self, mode="OBJECT"):
        """Единый метод создания node group с анти-рекурсией для всех клонеров"""
        
    def _create_cloner_logic(self, base_nodes, mode):
        """ДОЛЖНО БЫТЬ ПЕРЕОПРЕДЕЛЕНО в дочерних классах"""
        raise NotImplementedError
        
    def get_specific_sockets(self):
        """Специфичные сокеты клонера (переопределяется)"""
        return []
        
    def apply_anti_recursion(self, base_nodes, final_geometry, use_anti_recursion=True):
        """Встроенная анти-рекурсия через Switch ноды"""
```

#### 📂 components/cloners/ - Клонеры (BaseCloner наследники)
```
cloners/
├── __init__.py                # Экспорт всех клонеров
├── grid.py                    # GridCloner(BaseCloner) - unified
├── linear.py                  # LinearCloner(BaseCloner) - unified  
├── circle.py                  # CircleCloner(BaseCloner) - unified
├── object.py                  # ObjectCloner(BaseCloner) - mesh
└── spline.py                  # SplineCloner(BaseCloner) - mesh
```

**Новая структура файла клонера (на примере grid.py)**:
```python
class GridCloner(BaseCloner):
    """Grid клонер с 2D/3D сеточным распределением"""
    
    bl_idname = "GRID"
    
    def get_specific_sockets(self):
        """Специфичные сокеты Grid клонера"""
        return [
            ("Count X", "NodeSocketInt", "INPUT", 3),
            ("Count Y", "NodeSocketInt", "INPUT", 3),
            ("Count Z", "NodeSocketInt", "INPUT", 1),
            ("Spacing", "NodeSocketFloat", "INPUT", 2.0),
        ]
    
    def _create_cloner_logic(self, base_nodes, mode):
        """Специфичная логика Grid клонера"""
        # Создание сетки точек
        grid_points = self._create_grid_points(base_nodes)
        # Центрирование
        centered_points = self._apply_grid_centering(base_nodes, grid_points)
        # Инстансирование и трансформации
        return self.apply_instance_transforms(base_nodes, instance_output)

# Экспорт экземпляра для использования в системе
grid_cloner = GridCloner()
```

#### 📂 components/effectors/ - Эффекторы
```
effectors/
├── __init__.py                # Экспорт эффекторов
├── random.py                  # Random эффектор (реализован)
└── noise.py                   # Noise эффектор (реализован, не импортирован)
```

**Структура файла эффектора (на примере random.py)**:
```python
def create_random_effector_logic_group(name_suffix=""):
    """Создание geometry nodes группы для Random эффектора"""
    # Создание нодов для случайных трансформаций
    # Настройка параметров (seed, strength, etc.)
    # Возврат node group с socket mapping

# Вспомогательные функции
def setup_random_transformation_nodes(node_group)
def setup_random_interface(node_group)
```

#### 📂 components/fields/ - Поля воздействия
```
fields/
├── __init__.py                # Экспорт филдов  
└── sphere.py                  # Sphere филд (заготовка)
```

### 📂 config/ - Конфигурации

#### 📂 config/cloners/ - Конфигурации клонеров
```
cloners/
├── __init__.py                # Экспорт конфигураций
├── grid_config.py             # Параметры Grid клонера
├── linear_config.py           # Параметры Linear клонера
├── circle_config.py           # Параметры Circle клонера
├── object_config.py           # Параметры Object клонера
└── spline_config.py           # Параметры Spline клонера
```

**Структура конфигурационного файла**:
```python
# grid_config.py
GRID_CLONER_PARAMETERS = {
    "Count X": {"type": "INT", "default": 3, "min": 1, "max": 100},
    "Count Y": {"type": "INT", "default": 3, "min": 1, "max": 100},
    "Count Z": {"type": "INT", "default": 1, "min": 1, "max": 100},
    "Spacing": {"type": "FLOAT", "default": 2.0, "min": 0.1, "max": 100.0},
    "Offset": {"type": "VECTOR", "default": (0, 0, 0)}
}

def get_grid_cloner_parameters():
    """Возвращает параметры Grid клонера"""
    return GRID_CLONER_PARAMETERS
```

#### 📂 config/effectors/ - Конфигурации эффекторов
```
effectors/
├── __init__.py                # Экспорт конфигураций
├── random_config.py           # Параметры Random эффектора
└── noise_config.py            # Параметры Noise эффектора
```

### 📂 ui/ - Пользовательский интерфейс

#### 📂 ui/panels/ - Панели интерфейса
```
panels/
├── __init__.py                # Регистрация всех панелей
├── cloners.py                 # Панель клонеров (основная)
├── effectors.py               # Панель эффекторов
├── fields.py                  # Панель филдов
└── uuid_browser.py            # Браузер клонеров с UUID
```

**Структура панели (на примере cloners.py)**:
```python
class CLONERPRO_PT_ClonersPanel(bpy.types.Panel):
    """Основная панель клонеров"""
    bl_label = "Cloners"
    bl_idname = "CLONERPRO_PT_cloners" 
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    
    def draw(self, context):
        layout = self.layout
        
        # Фильтры отображения
        self.draw_system_filters(layout, context)
        
        # Unified клонеры
        if context.scene.clonerpro_show_unified:
            self.draw_unified_cloners(layout, context)
        
        # Mesh клонеры  
        if context.scene.clonerpro_show_mesh:
            self.draw_mesh_cloners(layout, context)
```

#### 📂 ui/operators/ - Операторы
```
operators/
├── __init__.py                # Регистрация операторов
├── creation.py                # Операторы создания клонеров
├── management.py              # Управление клонерами
├── browser.py                 # Браузер клонеров
├── cloner_ui.py               # UI операторы клонеров  
├── effector_ui.py             # UI операторы эффекторов
├── chain_management.py        # Управление цепочками
└── uuid_management.py         # Управление UUID
```

**Структура оператора создания**:
```python
class CLONERPRO_OT_CreateGridCloner(bpy.types.Operator):
    """Оператор создания Grid клонера"""
    bl_idname = "clonerpro.create_grid_cloner"
    bl_label = "Create Grid Cloner"
    bl_description = "Create a Grid cloner from selected object"
    bl_options = {'REGISTER', 'UNDO'}
    
    # Параметры оператора
    mode: bpy.props.EnumProperty(
        items=[
            ('OBJECT', 'Object', 'Create new cloner object'),
            ('STACKED', 'Stacked', 'Add modifier to current object'),
            ('COLLECTION', 'Collection', 'Clone collections')
        ],
        default='OBJECT'
    )
    
    def execute(self, context):
        # Логика создания клонера
        from core.templates.unified_creation import create_cloner_unified
        
        result = create_cloner_unified(
            cloner_type="GRID",
            mode=self.mode,
            source=context.active_object
        )
        
        if result:
            self.report({'INFO'}, f"Grid Cloner created in {self.mode} mode")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, "Failed to create Grid Cloner")
            return {'CANCELLED'}
```

#### 📂 ui/generators/ - Генераторы UI
```
generators/
├── __init__.py                # Экспорт генераторов
├── cloner_ui.py               # Автогенерация UI для клонеров
└── effector_ui.py             # Автогенерация UI для эффекторов
```

#### 📂 ui/utils/ - Утилиты UI
```
utils/
├── __init__.py                # Экспорт утилит
├── ui_helpers.py              # Вспомогательные функции UI
└── ui_state.py                # Управление состоянием UI
```

## Паттерны кода и соглашения

### Именование файлов и модулей
- **snake_case** для файлов: `unified_creation.py`
- **PascalCase** для классов: `BlenderClonerUUIDManager`
- **snake_case** для функций: `create_cloner_unified()`
- **UPPER_CASE** для констант: `UNIFIED_CLONERS`

### Структура BaseCloner классов

#### Единая архитектура для всех клонеров
```python
class {ClonerName}Cloner(BaseCloner):
    """Описание клонера"""
    
    bl_idname = "{CLONER_TYPE}"  # "GRID", "LINEAR", "OBJECT", etc.
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты клонера (добавляются к базовым)
        
        Returns:
            list: Список сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Специфичный параметр", "NodeSocketInt", "INPUT", 3),
            # ... другие параметры
        ]
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Создание специфичной логики клонера
        
        Args:
            base_nodes: Словарь с базовыми нодами от BaseCloner
            mode: Режим создания ("OBJECT", "STACKED", "COLLECTION")
            
        Returns:
            NodeSocket: Финальный выход геометрии перед анти-рекурсией
        """
        # 1. Получение правильного входа геометрии
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # 2. Создание специфичной логики клонера
        instances = self._create_cloner_specific_logic(base_nodes, geometry_input)
        
        # 3. Применение базовых трансформаций
        transformed = self.apply_instance_transforms(base_nodes, instances)
        
        # 4. Применение случайных трансформаций  
        randomized = self.apply_random_transforms(base_nodes, transformed, index_input)
        
        # 5. Применение глобальных трансформаций
        final_geometry = self.apply_global_transforms(base_nodes, randomized)
        
        return final_geometry
    
    def _create_cloner_specific_logic(self, base_nodes, geometry_input):
        """Специфичная логика клонера (переопределяется)"""
        raise NotImplementedError
```

#### Преимущества новой архитектуры
- **Единообразие**: Все клонеры используют одинаковый BaseCloner API
- **Встроенная анти-рекурсия**: Автоматически применяется для всех клонеров  
- **Общие функции**: Трансформации, рандомизация работают одинаково
- **Гибкость**: Легко переопределить любую часть логики
- **Расширяемость**: Простое добавление новых клонеров через наследование

### Паттерны обработки ошибок

```python
def safe_cloner_operation(func):
    """Декоратор для безопасных операций с клонерами"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"[ERROR] {func.__name__}: {e}")
            return None
    return wrapper

@safe_cloner_operation
def create_cloner_unified(cloner_type, mode, source, **kwargs):
    # Основная логика создания
    pass
```

### Паттерны логирования

```python
# Логирование с префиксами для категоризации
print(f"[UNIFIED] Creating {cloner_type} cloner in {mode} mode")
print(f"[MESH] Setting up {cloner_type} parameters")
print(f"[UUID] Generated cloner UUID: {uuid}")
print(f"[CHAIN] Linking cloner to chain: {chain_uuid}")
print(f"[ERROR] Failed to create cloner: {error}")
print(f"⚠️ [WARNING] Legacy cloner found, consider migration")
print(f"✅ [SUCCESS] Cloner created successfully")
```

## Зависимости между модулями

### Диаграмма зависимостей
```
ui/
├── depends on → core/
├── depends on → components/
└── depends on → config/

core/
├── depends on → components/ (через динамический импорт)
└── depends on → config/ (через динамический импорт)

components/
└── depends on → config/

config/
└── independent (no dependencies)
```

### Правила импорта

#### Разрешённые импорты
```python
# UI может импортировать всё
from core.templates.unified_creation import create_cloner_unified
from components.cloners.grid import create_grid_cloner_logic_group
from config.cloners.grid_config import GRID_CLONER_PARAMETERS

# Core может использовать динамические импорты
import importlib
grid_module = importlib.import_module("components.cloners.grid")

# Components могут импортировать config
from config.cloners.grid_config import get_grid_cloner_parameters
```

#### Запрещённые импорты
```python
# ❌ Циклические зависимости
# В core/ НЕ импортировать ui/
# В components/ НЕ импортировать ui/

# ❌ Прямые импорты в core от components
# Используйте динамические импорты вместо этого
```

## Расширение системы

### Добавление нового клонера (новая BaseCloner архитектура)

1. **Создание BaseCloner наследника**:
   ```python
   # components/cloners/spiral.py
   from ..base_cloner import BaseCloner
   
   class SpiralCloner(BaseCloner):
       """Spiral клонер для спиральных распределений"""
       
       bl_idname = "SPIRAL"
       
       def get_specific_sockets(self):
           return [
               ("Turns", "NodeSocketFloat", "INPUT", 3.0),
               ("Height", "NodeSocketFloat", "INPUT", 5.0),
               ("Radius", "NodeSocketFloat", "INPUT", 2.0)
           ]
       
       def _create_cloner_logic(self, base_nodes, mode):
           # Получение входной геометрии
           geometry_input = self.get_geometry_input(base_nodes, mode)
           
           # Создание спиральной логики
           spiral_instances = self._create_spiral_distribution(base_nodes, geometry_input)
           
           # Применение стандартных трансформаций
           transformed = self.apply_instance_transforms(base_nodes, spiral_instances)
           final = self.apply_global_transforms(base_nodes, transformed)
           
           return final
       
       def _create_spiral_distribution(self, base_nodes, geometry_input):
           # Специфичная логика спирали
           pass
   
   # Экспорт экземпляра
   spiral_cloner = SpiralCloner()
   ```

2. **Добавление в константы**:
   ```python
   # В core/core.py
   UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE", "SPIRAL"}  # Добавить SPIRAL
   ```

3. **Обновление автоматической маршрутизации**:
   ```python
   # В core/templates/cloner_creation.py
   def _get_cloner_instance(cloner_type):
       """Система автоматически найдет SpiralCloner по bl_idname"""
       # Никаких изменений не требуется - автоматическое обнаружение!
   ```

4. **Создание конфигурации** (опционально):
   ```python
   # config/cloners/spiral_config.py
   SPIRAL_CLONER_PARAMETERS = {
       "Turns": {"type": "FLOAT", "default": 3.0},
       "Height": {"type": "FLOAT", "default": 5.0}
   }
   ```

5. **UI автоматически подхватит новый клонер** через системные фильтры

Эта организация обеспечивает чистоту кода, простоту сопровождения и лёгкость расширения функциональности ClonerPro.