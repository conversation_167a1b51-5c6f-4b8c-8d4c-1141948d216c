"""
Cloners module для ClonerPro
Прямая реализация клонеров без абстракций
"""

from . import grid
from . import linear
from . import circle
from . import object
from . import spline


def register():
    """Регистрация клонеров"""
    grid.register()
    linear.register()
    circle.register()
    object.register()
    spline.register()


def unregister():
    """Отмена регистрации клонеров"""
    spline.unregister()
    object.unregister()
    circle.unregister()
    linear.unregister()
    grid.unregister()