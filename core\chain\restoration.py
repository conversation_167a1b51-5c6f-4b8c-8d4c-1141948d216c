"""
Chain Restoration - Восстановление цепей клонеров
"""

import bpy
from ...core.core import (
    get_cloner_modifier, get_cloner_info, set_cloner_metadata
)
from .utils import chain_debug_print
from .metadata import prepare_uuid_chain_info


def prepare_chain_restoration_info(context, cloner_obj):
    """
    Подготовить информацию для восстановления цепи ПЕРЕД удалением
    Возвращает словарь с информацией о цепи
    Использует только UUID систему
    """
    chain_debug_print(2, f"🔗 [CHAIN] Preparing chain restoration info for {cloner_obj.name}")
    
    cloner_modifier = get_cloner_modifier(cloner_obj)
    if not cloner_modifier:
        chain_debug_print(2, f"🔗 [CHAIN] No cloner modifier found on {cloner_obj.name}")
        return None
    
    cloner_info = get_cloner_info(cloner_modifier)
    chain_debug_print(2, f"🔗 [CHAIN] Cloner info: {cloner_info}")
    
    # Только UUID система
    return prepare_uuid_chain_info(cloner_obj, cloner_modifier, cloner_info)


def ensure_uuid_source_migration():
    """Убедиться, что существующие клонеры мигрированы к UUID источникам"""
    try:
        from .detection import migrate_existing_cloners_to_uuid_sources
        migrate_existing_cloners_to_uuid_sources()
    except Exception as e:
        print(f"⚠️ [CHAIN] UUID source migration failed: {e}")


def restore_chain_connections_after_deletion(context, chain_info):
    """
    Восстановить соединения цепи ПОСЛЕ удаления клонера
    Использует только UUID систему
    """
    # Убеждаемся, что существующие клонеры мигрированы к UUID источникам
    ensure_uuid_source_migration()
    
    if not chain_info or not chain_info.get("is_chained", False):
        chain_debug_print(2, "🔗 [CHAIN] No chain restoration needed")
        return True
    
    chain_debug_print(2, f"🔗 [CHAIN] Restoring chain after deletion of {chain_info['deleted_cloner_name']}")
    
    # Только UUID система
    return restore_uuid_chain(chain_info)


def restore_uuid_chain(chain_info):
    """Восстановление UUID цепи"""
    try:
        from ..uuid.chain_management import BlenderUUIDChainManager
        from ..uuid.manager import BlenderClonerUUIDManager
        
        deleted_uuid = chain_info.get("deleted_cloner_uuid", "")
        previous_uuid = chain_info.get("previous_cloner_uuid", "")
        next_uuids = chain_info.get("next_cloner_uuids", [])
        
        chain_debug_print(2, f"🔗 [CHAIN] UUID restoration:")
        print(f"    Deleted: {deleted_uuid}")
        print(f"    Previous: {previous_uuid}")
        print(f"    Next: {next_uuids}")
        
        if not next_uuids:
            print("✅ [CHAIN] No next cloners to reconnect - UUID restoration complete")
            return True
        
        # Определяем новый источник для следующих клонеров
        new_source = None
        
        if previous_uuid:
            # Есть предыдущий клонер - используем его как источник
            prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
            if prev_obj:
                new_source = prev_obj
                print(f"🔗 [CHAIN] Using previous cloner as source: {prev_obj.name}")
            else:
                print(f"⚠️ [CHAIN] Previous cloner with UUID {previous_uuid} not found")
        
        if not new_source:
            # Нет предыдущего клонера или он не найден - используем оригинальный источник
            from .source_discovery import get_original_source_from_info
            new_source = get_original_source_from_info(chain_info)
            if new_source:
                print(f"🔗 [CHAIN] Using original source: {new_source.name}")
        
        if not new_source:
            print("❌ [CHAIN] Could not determine new source for chain restoration")
            return False
        
        # Переподключаем всех следующих клонеров к новому источнику
        successful_reconnections = 0
        for next_uuid in next_uuids:
            next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
            if next_obj and next_modifier:
                print(f"🔗 [CHAIN] Reconnecting {next_obj.name} to {new_source.name}")
                
                from .reconnection import reconnect_to_source
                success = reconnect_to_source(next_obj, next_modifier, new_source)
                if success:
                    successful_reconnections += 1
                    
                    # Обновляем UUID метаданные связей
                    if previous_uuid:
                        # Обновляем previous_cloner_uuid
                        next_modifier["previous_cloner_uuid"] = previous_uuid
                        
                        # Добавляем этот клонер в next_cloner_uuids предыдущего
                        if prev_obj and prev_modifier:
                            current_next_uuids = prev_modifier.get("next_cloner_uuids", "").split(",")
                            current_next_uuids = [uuid.strip() for uuid in current_next_uuids if uuid.strip()]
                            
                            # Удаляем deleted_uuid и добавляем next_uuid если его нет
                            if deleted_uuid in current_next_uuids:
                                current_next_uuids.remove(deleted_uuid)
                            if next_uuid not in current_next_uuids:
                                current_next_uuids.append(next_uuid)
                            
                            prev_modifier["next_cloner_uuids"] = ",".join(current_next_uuids)
                    else:
                        # Это первый клонер в цепи, убираем previous_cloner_uuid
                        next_modifier["previous_cloner_uuid"] = ""
                else:
                    print(f"⚠️ [CHAIN] Failed to reconnect {next_obj.name}")
            else:
                print(f"⚠️ [CHAIN] Next cloner with UUID {next_uuid} not found")
        
        print(f"✅ [CHAIN] UUID restoration complete: {successful_reconnections}/{len(next_uuids)} reconnections")
        return successful_reconnections > 0
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available for restoration")
        return False
    except Exception as e:
        print(f"❌ [CHAIN] Error in UUID chain restoration: {e}")
        return False




def verify_chain_restoration(restored_cloners, expected_source):
    """Проверить корректность восстановления цепи"""
    if not restored_cloners or not expected_source:
        return False
    
    print(f"🔍 [CHAIN] Verifying chain restoration for {len(restored_cloners)} cloners")
    
    verified_count = 0
    for cloner_obj in restored_cloners:
        try:
            from .source_discovery import get_current_cloner_source
            cloner_modifier = get_cloner_modifier(cloner_obj)
            if not cloner_modifier:
                continue
            
            current_source = get_current_cloner_source(cloner_obj, cloner_modifier)
            if current_source and current_source.name == expected_source.name:
                verified_count += 1
                print(f"✓ [CHAIN] {cloner_obj.name} correctly connected to {current_source.name}")
            else:
                print(f"⚠️ [CHAIN] {cloner_obj.name} source mismatch - expected {expected_source.name}, got {current_source.name if current_source else 'None'}")
        except Exception as e:
            print(f"⚠️ [CHAIN] Error verifying {cloner_obj.name}: {e}")
    
    success_rate = verified_count / len(restored_cloners) if restored_cloners else 0
    print(f"🔍 [CHAIN] Verification complete: {verified_count}/{len(restored_cloners)} verified ({success_rate:.1%})")
    
    return success_rate >= 0.8  # 80% успешности считаем хорошим результатом


def rebuild_complete_chain_after_deletion(context, remaining_cloners, chain_info):
    """Полностью перестроить цепь после удаления клонера"""
    print(f"🔨 [CHAIN] Rebuilding complete chain for {len(remaining_cloners)} cloners")
    
    if not remaining_cloners:
        return True
    
    try:
        # Получаем оригинальный источник
        from .source_discovery import get_original_source_from_info
        original_source = get_original_source_from_info(chain_info)
        if not original_source:
            print("❌ [CHAIN] Cannot rebuild without original source")
            return False
        
        # Сортируем клонеры по их индексам
        sorted_cloners = []
        for cloner_obj in remaining_cloners:
            cloner_modifier = get_cloner_modifier(cloner_obj)
            if cloner_modifier:
                cloner_info = get_cloner_info(cloner_modifier)
                index = cloner_info.get("cloner_index", 0)
                sorted_cloners.append((index, cloner_obj, cloner_modifier))
        
        sorted_cloners.sort(key=lambda x: x[0])
        
        # Перестраиваем цепь с правильными связями
        previous_source = original_source
        rebuilt_count = 0
        
        for i, (index, cloner_obj, cloner_modifier) in enumerate(sorted_cloners):
            print(f"🔨 [CHAIN] Rebuilding cloner {i+1}/{len(sorted_cloners)}: {cloner_obj.name}")
            
            # Переподключаем к предыдущему источнику
            from .reconnection import reconnect_to_source
            success = reconnect_to_source(cloner_obj, cloner_modifier, previous_source)
            
            if success:
                # Обновляем метаданные
                new_metadata = {
                    "cloner_index": i,
                    "chain_source_object": chain_info.get("chain_source_object", ""),
                    "chain_source_collection": chain_info.get("chain_source_collection", "")
                }
                set_cloner_metadata(cloner_modifier, **new_metadata)
                
                # Этот клонер становится источником для следующего
                previous_source = cloner_obj
                rebuilt_count += 1
            else:
                print(f"⚠️ [CHAIN] Failed to rebuild {cloner_obj.name}")
        
        print(f"✅ [CHAIN] Chain rebuild complete: {rebuilt_count}/{len(sorted_cloners)} cloners")
        return rebuilt_count == len(sorted_cloners)
        
    except Exception as e:
        print(f"❌ [CHAIN] Error rebuilding chain: {e}")
        return False


def find_dependent_cloners_manually(context, chain_info):
    """Найти зависимые клонеры вручную через поиск по метаданным"""
    dependent_cloners = []
    
    try:
        chain_source_object = chain_info.get("chain_source_object", "")
        chain_source_collection = chain_info.get("chain_source_collection", "")
        deleted_cloner_name = chain_info.get("deleted_cloner_name", "")
        
        print(f"🔍 [CHAIN] Manual search for dependent cloners")
        print(f"    Source object: {chain_source_object}")
        print(f"    Source collection: {chain_source_collection}")
        print(f"    Deleted: {deleted_cloner_name}")
        
        # Проходим по всем объектам в сцене
        for obj in context.scene.objects:
            cloner_modifier = get_cloner_modifier(obj)
            if not cloner_modifier or obj.name == deleted_cloner_name:
                continue
            
            cloner_info = get_cloner_info(cloner_modifier)
            if not cloner_info:
                continue
            
            # Проверяем, принадлежит ли к той же цепи
            obj_source_object = cloner_info.get("chain_source_object", "")
            obj_source_collection = cloner_info.get("chain_source_collection", "")
            
            is_same_chain = False
            if chain_source_object and obj_source_object == chain_source_object:
                is_same_chain = True
            elif chain_source_collection and obj_source_collection == chain_source_collection:
                is_same_chain = True
            
            if is_same_chain:
                dependent_cloners.append(obj)
                print(f"✓ [CHAIN] Found dependent cloner: {obj.name}")
        
        print(f"🔍 [CHAIN] Manual search found {len(dependent_cloners)} dependent cloners")
        return dependent_cloners
        
    except Exception as e:
        print(f"❌ [CHAIN] Error in manual dependent cloner search: {e}")
        return []


def ensure_chain_source_uuid_propagation(start_uuid, source_obj, chain_info):
    """Обеспечить распространение UUID источника по всей цепи"""
    try:
        from ..uuid.manager import BlenderClonerUUIDManager
        from .source_discovery import get_or_create_source_uuid
        
        chain_uuid = chain_info.get("chain_uuid", "")
        if not chain_uuid:
            print("⚠️ [CHAIN] No chain UUID for source propagation")
            return
        
        # Получаем или создаем UUID для источника
        source_uuid = get_or_create_source_uuid(source_obj)
        if not source_uuid:
            print("⚠️ [CHAIN] Could not get/create source UUID")
            return
        
        print(f"🔗 [CHAIN] Propagating source UUID {source_uuid} through chain {chain_uuid}")
        
        # Находим всех клонеров в цепи
        chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
        
        # Обновляем chain_source_uuid у всех клонеров в цепи
        updated_count = 0
        for cloner_obj, cloner_modifier in chain_cloners:
            try:
                cloner_modifier["chain_source_uuid"] = source_uuid
                print(f"✓ [CHAIN] Updated source UUID for {cloner_obj.name}")
                updated_count += 1
            except Exception as e:
                print(f"⚠️ [CHAIN] Error updating source UUID for {cloner_obj.name}: {e}")
        
        print(f"✅ [CHAIN] Source UUID propagation complete: {updated_count}/{len(chain_cloners)} cloners updated")
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available for source propagation")
    except Exception as e:
        print(f"❌ [CHAIN] Error in source UUID propagation: {e}")


def get_deleted_cloner_source(chain_info):
    """Получить источник удаленного клонера из chain_info"""
    
    # Проверяем, есть ли direct ссылка на текущий источник
    current_source = chain_info.get("current_source")
    if current_source:
        print(f"🔍 [CHAIN] Found current source from chain_info: {current_source.name}")
        return current_source
    
    # Fallback к поиску оригинального источника
    from .source_discovery import get_original_source_from_info
    original_source = get_original_source_from_info(chain_info)
    if original_source:
        print(f"🔍 [CHAIN] Using original source as deleted cloner source: {original_source.name}")
        return original_source
    
    print(f"❌ [CHAIN] Could not determine deleted cloner source")
    return None