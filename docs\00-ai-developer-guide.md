# Руководство для ИИ-разработчиков по ClonerPro

> **🚨 ОБЯЗАТЕЛЬНОЕ ЧТЕНИЕ** для всех ИИ-ассистентов, работающих с ClonerPro

## КРИТИЧЕСКИ ВАЖНО: Что НИКОГДА нельзя делать

### ❌ ЗАПРЕЩЁННЫЕ ДЕЙСТВИЯ

1. **НЕ используйте старые API напрямую**
   ```python
   # ❌ НЕПРАВИЛЬНО - использование старых функций
   from core.templates.mesh_creation import create_mesh_cloner
   from core.templates.unified_creation import create_cloner_unified
   
   # ✅ ПРАВИЛЬНО - единый API
   from core.templates.cloner_creation import create_cloner_unified
   create_cloner_unified("OBJECT", "STACKED", source)  # Автоматически выберет mesh систему
   ```

2. **НЕ обходите классовую архитектуру**
   ```python
   # ❌ НЕПРАВИЛЬНО
   from components.cloners.grid import grid_cloner
   node_group, socket_mapping = grid_cloner.create_node_group()
   
   # ✅ ПРАВИЛЬНО
   from core.templates.cloner_creation import create_cloner_unified
   create_cloner_unified("GRID", "OBJECT", source)
   ```

3. **НЕ устанавливайте метаданные вручную**
   ```python
   # ❌ НЕПРАВИЛЬНО
   modifier["cloner_type"] = "GRID"
   modifier["cloner_uuid"] = str(uuid.uuid4())
   
   # ✅ ПРАВИЛЬНО
   from core.core import set_cloner_metadata
   set_cloner_metadata(modifier, "GRID", "OBJECT", original_object=source.name)
   ```

4. **НЕ обходите UUID систему**
   ```python
   # ❌ НЕПРАВИЛЬНО - UUID система критична для chain management
   modifier["use_uuid"] = False
   
   # ✅ ПРАВИЛЬНО - всегда используйте UUID систему
   # UUID включен по умолчанию в set_cloner_metadata()
   ```

## Единая классовая архитектура

### UNIFIED_CLONERS vs MESH_CLONERS

ClonerPro использует единую систему создания с автоматическим выбором подходящего метода:

```python
# Из core/core.py
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE"}
MESH_CLONERS = {"OBJECT", "SPLINE"}

def get_cloner_system(cloner_type: str) -> str:
    if cloner_type in UNIFIED_CLONERS:
        return "unified"
    elif cloner_type in MESH_CLONERS:
        return "mesh"
    else:
        return "unknown"
```

### Единый API и автоматическая маршрутизация

**Философия**: Одна функция для всех типов клонеров с автоматическим выбором системы

**Процесс создания**:
1. `create_cloner_unified()` - единая точка входа для всех клонеров
2. Автоматическое определение системы (unified или mesh)
3. Маршрутизация к соответствующей подсистеме
4. BaseCloner архитектура для всех клонеров

**Ключевые файлы**:
- `core/templates/cloner_creation.py` - объединённая система
- `components/base_cloner.py` - базовый класс для всех клонеров
- `components/cloners/grid.py` - Grid клонер (unified)
- `components/cloners/linear.py` - Linear клонер (unified) 
- `components/cloners/circle.py` - Circle клонер (unified)
- `components/cloners/object.py` - Object клонер (mesh)
- `components/cloners/spline.py` - Spline клонер (mesh)

## Правильные паттерны создания клонеров

### Любой клонер через единый API

```python
def create_any_cloner_properly():
    """✅ ПРАВИЛЬНЫЙ способ создания любого клонера"""
    from core.templates.cloner_creation import create_cloner_unified
    
    # Grid клонер (unified система)
    grid_cloner = create_cloner_unified(
        cloner_type="GRID",
        mode="OBJECT", 
        source=bpy.context.active_object
    )
    
    # Object клонер (mesh система) - API тот же!
    object_modifier = create_cloner_unified(
        cloner_type="OBJECT",
        mode="STACKED",
        source=bpy.context.active_object  # поверхность для клонирования
    )
    
    # Spline клонер с mesh stacking
    spline_modifier = create_cloner_unified(
        cloner_type="SPLINE",
        mode="STACKED",
        source=curve_object,
        mesh_stacked=True  # прямое создание на оригинале
    )
    
    # Collection режим - для любого типа клонера
    collection_cloner = create_cloner_unified(
        cloner_type="LINEAR",
        mode="COLLECTION",
        source="MyCollection"  # имя коллекции
    )
```

### Прямой доступ к mesh системе (опционально)

```python
def create_mesh_cloner_directly():
    """Прямой доступ к mesh системе для обратной совместимости"""
    from core.templates.cloner_creation import create_mesh_cloner
    
    modifier = create_mesh_cloner(
        cloner_type="OBJECT",
        mode="STACKED",
        target_obj=bpy.context.active_object
    )
```

## UUID система и Chain Management

### Основы UUID системы

ClonerPro использует UUID для:
- **Уникальной идентификации** каждого клонера
- **Связывания клонеров в цепочки** (chain management)  
- **Восстановления связей** после перезагрузки файла
- **Отслеживания источников** для цепочек

```python
# Пример UUID метаданных в модификаторе
{
    "cloner_uuid": "12345678-1234-5678-9abc-123456789def",
    "chain_uuid": "*************-8765-cba9-987654321fed", 
    "chain_source_uuid": "abcdef12-3456-7890-abcd-ef1234567890",
    "previous_cloner_uuid": "...",
    "next_cloner_uuids": "uuid1,uuid2,uuid3"
}
```

### Chain Detection и управление

```python
def work_with_chains_properly():
    """✅ ПРАВИЛЬНАЯ работа с цепочками клонеров"""
    from core.chain.detection import setup_cloner_chain_links_only
    from core.uuid.manager import BlenderClonerUUIDManager
    
    # Автоматическое связывание в цепочку
    setup_cloner_chain_links_only(context, new_cloner, source_cloner, None)
    
    # Поиск клонеров по UUID
    obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid)
    
    # Получение всех клонеров в цепочке
    chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
```

## Системы безопасности

### Anti-recursion система

```python
# Автоматически применяется в unified_creation.py
from core.templates.anti_recursion import apply_anti_recursion_architecture

# НЕ вызывайте напрямую, система применяется автоматически
wrapper_group = apply_anti_recursion_architecture(logic_group, name_suffix)
```

### Dependency Safety

```python
# Инициализируется автоматически в __init__.py
from core.system.dependency_safety import register_dependency_safety_handlers
register_dependency_safety_handlers()
```

## Чеклисты для ИИ-ассистентов

### ✅ Чеклист создания любого клонера

1. **Использование единого API**:
   ```python
   from core.templates.cloner_creation import create_cloner_unified
   ```

2. **Проверка типа клонера**:
   ```python
   # Unified клонеры
   assert cloner_type in ["GRID", "LINEAR", "CIRCLE"]
   # Mesh клонеры  
   assert cloner_type in ["OBJECT", "SPLINE"]
   ```

3. **Правильные параметры**:
   ```python
   create_cloner_unified(cloner_type, mode, source, use_anti_recursion=True, mesh_stacked=False)
   ```

4. **Режимы для unified клонеров**:
   - `mode="OBJECT"` - source должен быть объектом
   - `mode="STACKED"` - source должен быть объектом  
   - `mode="COLLECTION"` - source должен быть именем коллекции

5. **Режимы для mesh клонеров**:
   - `mode="STACKED"` - source обязателен (поверхность для клонирования)
   - `mode="COLLECTION"` - source + optional collection_name
   - `mesh_stacked=True` - для прямого создания на оригинале

### ✅ Чеклист добавления нового эффектора

1. **Создание файла компонента**:
   ```
   components/effectors/my_effector.py
   ```

2. **Создание конфигурации**:
   ```
   config/effectors/my_effector_config.py
   ```

3. **Регистрация в реестре**:
   ```python
   # В core/managers/effector_creation.py
   EFFECTOR_REGISTRY["MY_EFFECTOR"] = "create_my_effector_logic_group"
   ```

4. **Импорт в __init__.py**:
   ```python
   # В components/effectors/__init__.py
   from .my_effector import *
   ```

## Отладка и диагностика

### Проверка состояния клонера

```python
def debug_cloner_state(obj):
    """Диагностика состояния клонера"""
    from core.core import get_cloner_modifier, get_cloner_info
    
    modifier = get_cloner_modifier(obj)
    if not modifier:
        print(f"❌ {obj.name} не является клонером")
        return
    
    info = get_cloner_info(modifier)
    print(f"✅ Клонер найден:")
    print(f"  - Тип: {info['type']}")
    print(f"  - Режим: {info['mode']}")
    print(f"  - Система: {info['system']}")
    print(f"  - UUID: {info.get('cloner_uuid', 'НЕТ')}")
    print(f"  - Chain UUID: {info.get('chain_uuid', 'НЕТ')}")
```

### Сканирование всех клонеров

```python
def scan_all_cloners():
    """Полное сканирование клонеров в сцене"""
    from core.uuid.manager import BlenderClonerUUIDManager
    
    result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
    print(f"UUID клонеры: {len(result['cloners_by_uuid'])}")
    print(f"Цепочки: {len(result['chains_by_uuid'])}")
    print(f"Legacy клонеры: {len(result['legacy_cloners'])}")
```

## Распространённые ошибки и решения

### Ошибка: "Cloner type X not registered" 

```python
# ❌ Проблема: использование несуществующего типа клонера
create_cloner_unified("SPIRAL", "OBJECT", source)  # SPIRAL не реализован

# ✅ Решение: использовать реализованные типы
create_cloner_unified("CIRCLE", "OBJECT", source)  # CIRCLE реализован
```

### Ошибка: "No class-based cloner found"

```python
# ❌ Проблема: попытка создать клонер без классовой архитектуры
some_old_function("CUSTOM_CLONER")

# ✅ Решение: все клонеры используют BaseCloner архитектуру
create_cloner_unified("GRID", "OBJECT", source)  # Автоматически найдёт класс
```

### Ошибка: Chain detection не работает

```python
# ❌ Проблема: UUID система отключена
set_cloner_metadata(modifier, "GRID", "OBJECT", use_uuid=False)

# ✅ Решение: всегда использовать UUID
set_cloner_metadata(modifier, "GRID", "OBJECT")  # use_uuid=True по умолчанию
```

## Примеры интеграции

### Создание комплексной цепочки

```python
def create_complex_cloner_chain():
    """Создание цепочки: Cube -> Grid -> Object -> Linear"""
    import bpy
    from core.templates.cloner_creation import create_cloner_unified
    
    # 1. Исходный куб
    bpy.ops.mesh.primitive_cube_add()
    original_cube = bpy.context.active_object
    
    # 2. Grid клонер (unified система)
    grid_cloner = create_cloner_unified("GRID", "OBJECT", original_cube)
    
    # 3. Object клонер по Grid клонеру (mesh система)
    # Единый API автоматически выберет mesh систему для OBJECT
    object_modifier = create_cloner_unified("OBJECT", "STACKED", grid_cloner)
    
    # 4. Linear клонер от Object клонера (unified система)  
    linear_cloner = create_cloner_unified("LINEAR", "OBJECT", grid_cloner)
    
    print("✅ Цепочка создана: Cube -> Grid -> Object -> Linear")
```

### Настройка параметров клонера

```python
def setup_cloner_parameters(cloner_obj):
    """Настройка параметров через socket mapping"""
    from core.core import get_cloner_modifier
    
    modifier = get_cloner_modifier(cloner_obj)
    if not modifier or not modifier.node_group:
        return
    
    # Поиск и настройка сокетов
    for socket in modifier.node_group.interface.items_tree:
        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
            if socket.name == 'Count X':
                modifier[socket.identifier] = 5
            elif socket.name == 'Spacing':
                modifier[socket.identifier] = 2.0
```

## Лучшие практики для ИИ

1. **Используйте единый API** - `create_cloner_unified()` для всех типов клонеров
2. **Доверяйте автоматической маршрутизации** - система сама выберет unified/mesh
3. **Включайте UUID систему** для всех клонеров (включена по умолчанию)
4. **Следите за цепочками клонеров** при создании новых
5. **Используйте BaseCloner архитектуру** - все клонеры наследуются от неё
6. **Проверяйте результаты** через diagnostic функции
7. **Изучите анти-рекурсию** - она работает автоматически для всех клонеров

---

**Помните**: ClonerPro использует единую классовую архитектуру с автоматической маршрутизацией. Система упрощена, но остаётся мощной и гибкой.