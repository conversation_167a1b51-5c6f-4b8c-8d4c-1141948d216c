bl_info = {
    "name": "ClonerPro",
    "description": "Professional cloning tools for Blender - Simplified Architecture",
    "author": "ClonerPro Team",
    "version": (1, 0, 0),
    "blender": (4, 0, 0),
    "location": "View3D > Sidebar > ClonerPro",
    "category": "Mesh",
    "support": "COMMUNITY",
}

import bpy
from . import ui
from . import core
from . import components
from . import config


def register():
    """Register all addon components"""
    print("🚀 ClonerPro: Starting registration with COMPLETE advanced_cloners safety systems...")
    
    # 🛡️ КРИТИЧНО: Инициализируем ПОЛНУЮ систему защиты от крашей ПЕРВОЙ
    try:
        from .core.system.dependency_safety import register_dependency_safety_handlers
        
        print("🛡️ ClonerPro: Initializing COMPLETE safety systems from advanced_cloners...")
        
        # Инициализируем ПОЛНУЮ систему безопасности
        register_dependency_safety_handlers()
        print("✅ COMPLETE dependency safety system initialized")
        
        print("🛡️ ClonerPro: COMPLETE safety systems initialized successfully")
        
    except Exception as e:
        print(f"🚨 КРИТИЧЕСКАЯ ОШИБКА: Не удалось инициализировать ПОЛНУЮ систему безопасности: {e}")
        import traceback
        traceback.print_exc()
        # Продолжаем регистрацию без систем безопасности (не идеально, но лучше чем краш)
    
    # Register UI properties FIRST (required for panels)
    register_ui_properties()
    
    # Register core components
    core.register()
    
    # Register configurations
    config.register() if hasattr(config, 'register') else None
    
    # Register components
    components.register() if hasattr(components, 'register') else None
    
    # Register UI components
    ui.register()
    
    # 🔧 LEGACY: Настраиваем дополнительные системы мониторинга (для совместимости)
    try:
        from .core.cleanup.effector_cleanup import setup_effector_deletion_monitoring
        setup_effector_deletion_monitoring()
        print("✅ Legacy effector cleanup monitoring enabled")
    except Exception as e:
        print(f"⚠️ Warning: Legacy effector cleanup setup failed: {e}")
    
    print("🎉 ClonerPro: Addon registered successfully with enhanced safety!")


def register_ui_properties():
    """Register UI properties required for panels"""
    from bpy.props import BoolProperty
    
    # System filter properties for UI
    bpy.types.Scene.clonerpro_show_unified = BoolProperty(
        name="Show Unified Cloners",
        description="Show unified cloners (Grid, Linear, Circle, Spiral) in UI",
        default=True
    )
    
    bpy.types.Scene.clonerpro_show_mesh = BoolProperty(
        name="Show Mesh Cloners", 
        description="Show mesh cloners (Object, Curves, Volume, Spline) in UI",
        default=True
    )


def unregister_ui_properties():
    """Unregister UI properties"""
    try:
        del bpy.types.Scene.clonerpro_show_unified
        del bpy.types.Scene.clonerpro_show_mesh
    except:
        pass


def unregister():
    """Unregister all addon components"""
    print("🔄 ClonerPro: Starting safe unregistration...")
    
    # 🛡️ КРИТИЧНО: Безопасно отключаем ПОЛНУЮ систему безопасности ПЕРВОЙ
    try:
        from .core.system.dependency_safety import unregister_dependency_safety_handlers, emergency_reset_all_locks
        
        print("🛡️ ClonerPro: Disabling COMPLETE safety systems...")
        
        # Отключаем ПОЛНУЮ систему безопасности
        unregister_dependency_safety_handlers()
        emergency_reset_all_locks()
        print("✅ COMPLETE dependency safety system disabled")
        
        print("🛡️ ClonerPro: COMPLETE safety systems disabled successfully")
        
    except Exception as e:
        print(f"⚠️ Warning: Error disabling safety systems: {e}")
    
    # 🔧 LEGACY: Отключаем дополнительные системы мониторинга (для совместимости)
    try:
        from .core.cleanup.effector_cleanup import remove_effector_deletion_monitoring
        remove_effector_deletion_monitoring()
        print("✅ Legacy effector cleanup monitoring disabled")
    except Exception as e:
        print(f"⚠️ Warning: Legacy effector cleanup removal failed: {e}")
    
    # Unregister in reverse order
    ui.unregister()
    components.unregister() if hasattr(components, 'unregister') else None
    config.unregister() if hasattr(config, 'unregister') else None
    core.unregister()
    
    # Unregister UI properties LAST
    unregister_ui_properties()
    
    print("✅ ClonerPro: Addon unregistered safely!")


if __name__ == "__main__":
    register()