"""
Chain Reconnection - Переподключение клонеров к источникам
"""

import bpy
from ...core.core import get_cloner_modifier
from .utils import force_viewport_update


def reconnect_to_source(target_obj, target_modifier, source_obj):
    """Переподключить клонер к новому источнику с полным принудительным обновлением"""
    try:
        # Находим правильный input в node group
        if not target_modifier.node_group:
            return False
        
        # Определяем режим клонера для выбора правильного типа сокета
        cloner_mode = target_modifier.get("cloner_mode", "OBJECT")
        is_collection_mode = cloner_mode == "COLLECTION"
        
        success = False
        socket_identifier = None
        
        print(f"🔍 [CHAIN] Looking for {'collection' if is_collection_mode else 'object'} socket in {target_modifier.node_group.name}")
        
        # Используем тот же порядок поиска что и при создании клонера
        if is_collection_mode:
            success = _reconnect_collection_mode(target_modifier, source_obj)
        else:
            success, socket_identifier = _reconnect_object_mode(target_modifier, source_obj)
        
        if not success:
            # Вывести все доступные сокеты для отладки
            available_sockets = []
            for item in target_modifier.node_group.interface.items_tree:
                if item.item_type == 'SOCKET' and item.in_out == 'INPUT':
                    available_sockets.append(f"{item.name} ({item.identifier})")
            print(f"❌ [CHAIN] Failed to reconnect. Available input sockets: {available_sockets}")
            return False
        
        # АГРЕССИВНОЕ обновление viewport - используем старую рабочую логику
        _apply_enhanced_viewport_update(target_obj, target_modifier, source_obj, socket_identifier)
        
        return True
        
    except Exception as e:
        print(f"❌ [CHAIN] Error in reconnect_to_source: {e}")
        return False


def _reconnect_collection_mode(modifier, source_obj):
    """Переподключение для Collection режима"""
    # КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Определяем правильный источник коллекции
    actual_source_collection = None
    
    # Проверяем тип источника
    if hasattr(source_obj, 'objects'):
        # source_obj это коллекция - используем её напрямую
        actual_source_collection = source_obj
        print(f"🔌 [CHAIN] Source is collection directly: {actual_source_collection.name}")
        
    elif hasattr(source_obj, 'type'):
        # source_obj это объект-клонер - найти его коллекцию
        source_cloner_modifier = get_cloner_modifier(source_obj)
        if source_cloner_modifier:
            # Получаем коллекцию клонера-источника
            source_cloner_collection_name = source_cloner_modifier.get("cloner_collection", "")
            if source_cloner_collection_name and source_cloner_collection_name in bpy.data.collections:
                actual_source_collection = bpy.data.collections[source_cloner_collection_name]
                print(f"🔌 [CHAIN] Found source cloner collection: {actual_source_collection.name}")
            else:
                print(f"⚠️ [CHAIN] No cloner collection found for source {source_obj.name}")
        else:
            print(f"⚠️ [CHAIN] Source object {source_obj.name} is not a cloner")
    
    if not actual_source_collection:
        print(f"⚠️ [CHAIN] Could not determine source collection from {source_obj.name}")
        return False
    
    # Теперь подключаем ПРАВИЛЬНУЮ коллекцию
    for item in modifier.node_group.interface.items_tree:
        if (item.item_type == 'SOCKET' and item.in_out == 'INPUT' and item.name == 'Collection'):
            print(f"🔍 [CHAIN] Setting Collection socket to: {actual_source_collection.name}")
            try:
                modifier[item.identifier] = actual_source_collection
                print(f"✓ [CHAIN] Successfully reconnected Collection input to {actual_source_collection.name}")
                return True
            except Exception as e:
                print(f"⚠️ [CHAIN] Failed to set Collection socket: {e}")
                continue
    
    return False


def _reconnect_object_mode(modifier, source_obj):
    """Переподключение для Object режима"""
    # Для режима OBJECT используем точно такой же порядок как в unified_creation.py
    # Сначала ищем Object сокет (приоритет)
    for item in modifier.node_group.interface.items_tree:
        if (item.item_type == 'SOCKET' and item.in_out == 'INPUT' and item.name == 'Object'):
            print(f"🔍 [CHAIN] Attempting to set Object socket (ID: {item.identifier}) to {source_obj.name}")
            try:
                modifier[item.identifier] = source_obj
                print(f"✓ [CHAIN] Successfully reconnected Object input to {source_obj.name}")
                return True, item.identifier
            except Exception as e:
                print(f"⚠️ [CHAIN] Failed to set Object socket: {e}")
                continue
    
    # Fallback для других типов сокетов
    for item in modifier.node_group.interface.items_tree:
        if (item.item_type == 'SOCKET' and item.in_out == 'INPUT' and 
            item.name in ['Instance Source', 'Source Object']):
            print(f"🔍 [CHAIN] Attempting to set {item.name} socket (ID: {item.identifier}) to {source_obj.name} (fallback)")
            try:
                modifier[item.identifier] = source_obj
                print(f"✓ [CHAIN] Successfully reconnected {item.name} input to {source_obj.name} (fallback)")
                return True, item.identifier
            except Exception as e:
                print(f"⚠️ [CHAIN] Failed to set {item.name} socket: {e}")
                continue
    
    return False, None


def _apply_enhanced_viewport_update(target_obj, target_modifier, source_obj, socket_identifier):
    """Применить агрессивное обновление viewport"""
    try:
        # Определяем режим клонера
        cloner_mode = target_modifier.get("cloner_mode", "OBJECT")
        
        if cloner_mode == "OBJECT":
            _apply_object_mode_update(target_obj, target_modifier, source_obj, socket_identifier)
        elif cloner_mode == "COLLECTION":
            _apply_collection_mode_update(target_obj, target_modifier)
        else:
            _apply_stacked_mode_update(target_obj, target_modifier)
            
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in enhanced viewport update: {e}")


def _apply_object_mode_update(target_obj, target_modifier, source_obj, socket_identifier):
    """Обновление для Object режима"""
    try:
        print(f"🔄 [CHAIN] Starting enhanced Object viewport update for {target_obj.name}")
        
        # 1. Принудительно помечаем объекты как изменённые
        target_obj.update_tag()
        if hasattr(target_obj, 'data') and target_obj.data:
            target_obj.data.update_tag()
        
        # 2. Помечаем исходный объект как изменённый
        source_obj.update_tag()
        if hasattr(source_obj, 'data') and source_obj.data:
            source_obj.data.update_tag()
        
        # 3. АГРЕССИВНОЕ обновление dependency graph
        force_viewport_update()
        
        # 4. Переключение видимости модификатора
        original_show_viewport = target_modifier.show_viewport
        original_show_render = target_modifier.show_render
        
        target_modifier.show_viewport = False
        target_modifier.show_render = False
        bpy.context.view_layer.update()
        
        target_modifier.show_viewport = original_show_viewport
        target_modifier.show_render = original_show_render
        bpy.context.view_layer.update()
        
        # 5. Socket flip-flop для Object
        if socket_identifier:
            current_value = target_modifier.get(socket_identifier)
            target_modifier[socket_identifier] = None
            bpy.context.view_layer.update()
            target_modifier[socket_identifier] = current_value
            bpy.context.view_layer.update()
        
        # 6. Edit mode toggle для принуждения обновления
        if target_obj.type == 'MESH':
            # Устанавливаем активный объект перед переключением режима
            bpy.context.view_layer.objects.active = target_obj
            bpy.ops.object.mode_set(mode='EDIT')
            bpy.context.view_layer.update()
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.context.view_layer.update()
        
        print(f"✅ [CHAIN] Enhanced Object cloner viewport update completed")
        
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in Object cloner viewport update: {e}")


def _apply_collection_mode_update(target_obj, target_modifier):
    """Обновление для Collection режима"""
    # Для Collection режима используем ПРОСТУЮ логику из старой рабочей версии
    try:
        print(f"🔄 [CHAIN] Starting SIMPLE Collection viewport update for {target_obj.name}")
        
        # 1. Помечаем объект как изменённый
        target_obj.update_tag()
        if hasattr(target_obj, 'data') and target_obj.data:
            target_obj.data.update_tag()
        
        # 2. Помечаем данные модификатора как изменённые
        if hasattr(target_modifier, 'update_tag'):
            target_modifier.update_tag()
        
        # 3. АГРЕССИВНОЕ обновление dependency graph (несколько раз)
        force_viewport_update()
        
        # 4. Принудительно пересчитываем геометрию объекта
        bpy.context.view_layer.objects.active = target_obj
        target_obj.select_set(True)
        
        # Обновляем dependency graph еще раз после выбора
        bpy.context.view_layer.update()
        
        print(f"✅ [CHAIN] SIMPLE Collection viewport update completed")
        
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in Collection viewport update: {e}")


def _apply_stacked_mode_update(target_obj, target_modifier):
    """Обновление для Stacked режима"""
    try:
        print(f"🔄 [CHAIN] Starting enhanced Stacked viewport update for {target_obj.name}")
        
        # 1. Помечаем объект как изменённый
        target_obj.update_tag()
        if hasattr(target_modifier, 'update_tag'):
            target_modifier.update_tag()
        
        # 2. Обновляем dependency graph несколько раз
        force_viewport_update()
        
        print(f"✓ [CHAIN] Multiple dependency graph updates completed")
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in dependency graph update: {e}")
    
    # 3. Edit mode toggle принудительно
    try:
        if target_obj.type == 'MESH':
            bpy.context.view_layer.objects.active = target_obj
            target_obj.select_set(True)
            bpy.context.view_layer.update()
            
            bpy.ops.object.mode_set(mode='EDIT')
            bpy.context.view_layer.update()
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.context.view_layer.update()
            
        print(f"✓ [CHAIN] Edit mode toggle completed")
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in edit mode toggle: {e}")
    
    print(f"✅ [CHAIN] Enhanced Stacked viewport update completed")


def reconnect_cloner_to_source(context, cloner_obj, new_source_obj):
    """Переподключить конкретный клонер к новому источнику"""
    try:
        print(f"🔄 [CHAIN] Reconnecting {cloner_obj.name} to {new_source_obj.name}")
        
        # Получаем модификатор клонера
        cloner_modifier = get_cloner_modifier(cloner_obj)
        if not cloner_modifier:
            print(f"⚠️ [CHAIN] No cloner modifier found on {cloner_obj.name}")
            return False
        
        # Выполняем переподключение
        success = reconnect_to_source(cloner_obj, cloner_modifier, new_source_obj)
        
        if success:
            print(f"✅ [CHAIN] Successfully reconnected {cloner_obj.name} to {new_source_obj.name}")
        else:
            print(f"❌ [CHAIN] Failed to reconnect {cloner_obj.name} to {new_source_obj.name}")
        
        return success
        
    except Exception as e:
        print(f"❌ [CHAIN] Error reconnecting cloner: {e}")
        return False


def set_collection_input_with_update(modifier, collection):
    """Установить коллекцию в качестве входа С ПРИНУДИТЕЛЬНЫМ ОБНОВЛЕНИЕМ"""
    node_group = modifier.node_group
    
    # Ищем Collection socket
    for item in node_group.interface.items_tree:
        if (item.item_type == 'SOCKET' and 
            item.in_out == 'INPUT' and 
            item.name == "Collection"):
            
            modifier[item.identifier] = collection
            print(f"✓ [CHAIN] Updated Collection to {collection.name}")
            
            # ПРИНУДИТЕЛЬНОЕ обновление viewport
            apply_mode_change_update(modifier, modifier.id_data)
            
            return True
    
    print("⚠️ [CHAIN] No Collection socket found")
    return False


def apply_mode_change_update(modifier, cloner_obj):
    """Применить принудительное обновление после изменения режима"""
    try:
        print(f"🔄 [CHAIN] Applying mode change update for {cloner_obj.name}")
        
        # Находим объект-владелец модификатора
        if cloner_obj:
            print(f"✓ [CHAIN] Found cloner object: {cloner_obj.name}")
            
            # Помечаем объект как изменённый
            cloner_obj.update_tag()
            
            # Помечаем данные модификатора как изменённые
            if hasattr(modifier, 'update_tag'):
                modifier.update_tag()
            
            # Принудительно обновляем dependency graph
            bpy.context.view_layer.update()
            
            # Обновляем dependency graph несколько раз
            force_viewport_update()
            
            print(f"✓ [CHAIN] Multiple dependency graph updates completed")
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in dependency graph update: {e}")
    
    # Edit mode toggle принудительно
    try:
        if cloner_obj and cloner_obj.type == 'MESH':
            bpy.context.view_layer.objects.active = cloner_obj
            cloner_obj.select_set(True)
            bpy.context.view_layer.update()
            
            bpy.ops.object.mode_set(mode='EDIT')
            bpy.context.view_layer.update()
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.context.view_layer.update()
            
        print(f"✓ [CHAIN] Edit mode toggle completed")
    except Exception as e:
        print(f"⚠️ [CHAIN] Error in edit mode toggle: {e}")
    
    print(f"✅ [CHAIN] Mode change update completed")