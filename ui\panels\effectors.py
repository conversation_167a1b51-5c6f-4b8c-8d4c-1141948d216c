"""
Effectors Panel для ClonerPro
Точная копия интерфейса эффекторов из Advanced Cloners
"""

import bpy
from bpy.types import Panel


class EFFECTOR_PT_main_panel(Panel):
    """Панель эффекторов - точная копия advanced_cloners"""
    bl_label = "Effectors"
    bl_idname = "EFFECTOR_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 1
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout
        
        # 2x3 сетка кнопок создания эффекторов
        self.draw_effector_creation_grid(layout)
        
        # Список существующих эффекторов
        self.draw_effector_list(layout, context)

    def draw_effector_creation_grid(self, layout):
        """2x3 сетка кнопок создания эффекторов"""
        
        # Первый ряд: Plain, Noise
        row1 = layout.row(align=True)
        row1.scale_y = 1.2
        
        plain_op = row1.operator("clonerpro.create_effector", text="Plain", icon="EMPTY_ARROWS")
        plain_op.component_type = "PLAIN"
        
        noise_op = row1.operator("clonerpro.create_effector", text="Noise", icon="FORCE_TURBULENCE")
        noise_op.component_type = "NOISE"
        
        # Второй ряд: Random, Step
        row2 = layout.row(align=True)
        row2.scale_y = 1.2
        
        random_op = row2.operator("clonerpro.create_effector", text="Random", icon="FORCE_HARMONIC")
        random_op.component_type = "RANDOM"
        
        step_op = row2.operator("clonerpro.create_effector", text="Step", icon="IPO_CONSTANT")
        step_op.component_type = "STEP"
        
        # Третий ряд: Delay, Target
        row3 = layout.row(align=True)
        row3.scale_y = 1.2
        
        delay_op = row3.operator("clonerpro.create_effector", text="Delay", icon="TIME")
        delay_op.component_type = "DELAY"
        
        target_op = row3.operator("clonerpro.create_effector", text="Target", icon="FORCE_MAGNETIC")
        target_op.component_type = "TARGET"

    def draw_effector_list(self, layout, context):
        """Список существующих эффекторов"""
        effectors = self.get_scene_effectors(context)
        
        if not effectors:
            return
        
        # Заголовок списка
        layout.separator()
        list_header = layout.row()
        list_header.label(text=f"Effectors: {len(effectors)}", icon="FORCE_TURBULENCE")
        
        # Отрисовка каждого эффектора
        for effector in effectors:
            self.draw_individual_effector(layout, effector)

    def draw_individual_effector(self, layout, effector):
        """Отрисовка индивидуального эффектора"""
        # Получаем объект и модификатор
        obj = bpy.data.objects.get(effector['object'])
        if not obj:
            return
        
        modifier = obj.modifiers.get(effector['modifier'])
        if not modifier:
            return
        
        # Используем нашу систему отображения эффекторов
        from ...ui.generators.effector_ui import draw_effector_ui_simple
        draw_effector_ui_simple(bpy.context, layout, obj, modifier)

    def get_scene_effectors(self, context):
        """Получить все эффекторы из сцены"""
        effectors = []
        
        # Поиск объектов с эффекторами
        for obj in context.scene.objects:
            for modifier in obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    effector_type = self.detect_effector_type(modifier.node_group.name)
                    if effector_type:
                        effectors.append({
                            'name': f"{effector_type} Effector",
                            'type': effector_type,
                            'object': obj.name,
                            'modifier': modifier.name,
                            'visible': modifier.show_viewport
                        })
        
        return effectors

    def detect_effector_type(self, node_group_name):
        """Определить тип эффектора по имени node group"""
        name_lower = node_group_name.lower()
        
        if 'noise' in name_lower and 'effector' in name_lower:
            return 'Noise'
        elif 'random' in name_lower and 'effector' in name_lower:
            return 'RANDOM'
        elif 'plain' in name_lower and 'effector' in name_lower:
            return 'Plain'
        elif 'step' in name_lower and 'effector' in name_lower:
            return 'Step'
        elif 'delay' in name_lower and 'effector' in name_lower:
            return 'Delay'
        elif 'target' in name_lower and 'effector' in name_lower:
            return 'Target'
        
        return None


# Операторы для управления эффекторами
class CLONERPRO_OT_move_effector_up(bpy.types.Operator):
    """Переместить эффектор вверх"""
    bl_idname = "clonerpro.move_effector_up"
    bl_label = "Move Effector Up"
    bl_description = "Move effector up in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved effector up")
        return {'FINISHED'}


class CLONERPRO_OT_move_effector_down(bpy.types.Operator):
    """Переместить эффектор вниз"""
    bl_idname = "clonerpro.move_effector_down"
    bl_label = "Move Effector Down"
    bl_description = "Move effector down in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved effector down")
        return {'FINISHED'}


def register():
    """Регистрация панели эффекторов"""
    bpy.utils.register_class(CLONERPRO_OT_move_effector_up)
    bpy.utils.register_class(CLONERPRO_OT_move_effector_down)
    bpy.utils.register_class(EFFECTOR_PT_main_panel)


def unregister():
    """Отмена регистрации панели эффекторов"""
    bpy.utils.unregister_class(EFFECTOR_PT_main_panel)
    bpy.utils.unregister_class(CLONERPRO_OT_move_effector_down)
    bpy.utils.unregister_class(CLONERPRO_OT_move_effector_up)