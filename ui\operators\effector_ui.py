"""
Effector UI Operators для ClonerPro
Операторы для управления эффекторами в UI
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty
from ...ui.utils.ui_state import is_effector_expanded, set_effector_expanded


class CLONERPRO_OT_toggle_effector_expanded(Operator):
    """Переключить состояние expand/collapse эффектора"""
    bl_idname = "clonerpro.toggle_effector_expanded"
    bl_label = "Toggle Effector Expanded"
    bl_description = "Expand or collapse effector parameters"
    bl_options = {'REGISTER'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        current_state = is_effector_expanded(context, self.object_name, self.modifier_name)
        new_state = not current_state
        
        set_effector_expanded(context, self.object_name, self.modifier_name, new_state)
        
        return {'FINISHED'}


class CLONERPRO_OT_toggle_effector_visibility(Operator):
    """Переключить видимость эффектора"""
    bl_idname = "clonerpro.toggle_effector_visibility"
    bl_label = "Toggle Effector Visibility"
    bl_description = "Toggle effector visibility in viewport"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        # Проверяем безопасность операции
        from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
        
        if not is_safe_to_perform_effector_operations():
            self.report({'ERROR'}, "Effector operations are currently blocked for safety")
            return {'CANCELLED'}
        
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        # Безопасно выполняем переключение видимости
        def _safe_visibility_operation():
            modifier.show_viewport = not modifier.show_viewport
            return modifier.show_viewport
        
        success, result = safe_execute_effector_operation(_safe_visibility_operation)
        
        if success:
            status = "visible" if result else "hidden"
            self.report({'INFO'}, f"Effector {self.modifier_name} is now {status}")
        else:
            self.report({'ERROR'}, f"Failed to toggle effector visibility safely")
            return {'CANCELLED'}
        
        return {'FINISHED'}


class CLONERPRO_OT_delete_effector(Operator):
    """Удалить эффектор"""
    bl_idname = "clonerpro.delete_effector"
    bl_label = "Delete Effector"
    bl_description = "Delete effector modifier"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        # Проверяем безопасность операции
        from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
        
        if not is_safe_to_perform_effector_operations():
            self.report({'ERROR'}, "Effector operations are currently blocked for safety")
            return {'CANCELLED'}
        
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        # Безопасно выполняем удаление эффектора
        def _safe_delete_operation():
            return self._safe_delete_effector_with_nodegraph_protection(obj, modifier)
        
        success, result = safe_execute_effector_operation(_safe_delete_operation)
        
        if success and result:
            self.report({'INFO'}, f"Deleted effector {self.modifier_name}")
        else:
            self.report({'ERROR'}, f"Failed to delete effector safely")
            return {'CANCELLED'}
        
        return {'FINISHED'}
    
    def _safe_delete_effector_with_nodegraph_protection(self, obj, modifier):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Безопасное удаление эффектора с защитой NodeTree.
        
        Эта функция ОБЯЗАТЕЛЬНА для предотвращения крашей в rna_NodeTree_refine
        при повторном создании эффекторов после удаления.
        """
        try:
            print(f"[NodeTree Protection] Безопасное удаление эффектора {modifier.name}")
            
            # ШАГ 1: КРИТИЧНО - Сохраняем ссылку на node_group ПЕРЕД удалением модификатора
            node_group = modifier.node_group
            effector_name = modifier.name
            
            # ШАГ 2: КРИТИЧНО - Отключаем все live sync handlers для этого эффектора
            self._disable_live_sync_handlers_for_effector(effector_name)
            
            # ШАГ 3: КРИТИЧНО - Безопасно удаляем эффектор из всех клонеров
            self._remove_effector_from_all_cloners_safely(obj, effector_name)
            
            # ШАГ 4: КРИТИЧНО - Очищаем все ссылки на node_group ПЕРЕД удалением модификатора
            self._clear_all_nodegraph_references(modifier, node_group)
            
            # ШАГ 5: КРИТИЧНО - Invalidate node_group до удаления модификатора
            self._invalidate_node_tree_safely(node_group)
            
            # ШАГ 6: КРИТИЧНО - Удаляем модификатор только ПОСЛЕ очистки всех ссылок
            modifier.node_group = None  # Критично: обнуляем ссылку
            obj.modifiers.remove(modifier)
            
            # ШАГ 7: КРИТИЧНО - Безопасно удаляем node_group если она больше не используется
            self._safe_remove_node_group(node_group)
            
            # ШАГ 8: КРИТИЧНО - Полная очистка памяти от всех следов эффектора
            from ...core.system.dependency_safety import complete_effector_memory_cleanup
            complete_effector_memory_cleanup(effector_name)
            
            # ШАГ 9: КРИТИЧНО - Принудительное обновление dependency graph
            bpy.context.view_layer.update()
            
            print(f"[NodeTree Protection] ✅ Эффектор {effector_name} безопасно удален")
            return True
            
        except Exception as e:
            print(f"[NodeTree Protection] ❌ Ошибка безопасного удаления: {e}")
            import traceback
            traceback.print_exc()
            
            # КРИТИЧНО: В случае ошибки выполняем экстренную очистку
            try:
                from ...core.system.dependency_safety import emergency_system_reset
                emergency_system_reset()
            except:
                pass
            
            return False
    
    def _disable_live_sync_handlers_for_effector(self, effector_name):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Отключает все live sync handlers для эффектора.
        
        Это предотвращает попытки обращения к удаленной node_group через handlers.
        """
        try:
            print(f"[Handler Protection] Отключаем handlers для {effector_name}")
            
            # Отключаем депsgraph handlers
            handlers_to_remove = []
            for handler in bpy.app.handlers.depsgraph_update_post:
                try:
                    if (hasattr(handler, 'effector_name') and 
                        handler.effector_name == effector_name):
                        handlers_to_remove.append(handler)
                    elif (hasattr(handler, '__name__') and 
                          effector_name in str(handler)):
                        handlers_to_remove.append(handler)
                except (AttributeError, ReferenceError):
                    # Handler уже поврежден, добавляем в список удаления
                    handlers_to_remove.append(handler)
            
            for handler in handlers_to_remove:
                try:
                    bpy.app.handlers.depsgraph_update_post.remove(handler)
                    print(f"[Handler Protection] Удален depsgraph handler")
                except (ValueError, ReferenceError):
                    pass
            
            # Отключаем frame_change handlers
            handlers_to_remove = []
            for handler in bpy.app.handlers.frame_change_post:
                try:
                    if (hasattr(handler, 'effector_name') and 
                        handler.effector_name == effector_name):
                        handlers_to_remove.append(handler)
                except (AttributeError, ReferenceError):
                    handlers_to_remove.append(handler)
            
            for handler in handlers_to_remove:
                try:
                    bpy.app.handlers.frame_change_post.remove(handler)
                    print(f"[Handler Protection] Удален frame_change handler")
                except (ValueError, ReferenceError):
                    pass
            
            print(f"[Handler Protection] ✅ Handlers отключены для {effector_name}")
            
        except Exception as e:
            print(f"[Handler Protection] ❌ Ошибка отключения handlers: {e}")
    
    def _remove_effector_from_all_cloners_safely(self, obj, effector_name):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Безопасно удаляет эффектор из всех клонеров.
        """
        try:
            print(f"[Cloner Cleanup] Удаляем {effector_name} из всех клонеров")
            
            for mod in obj.modifiers:
                try:
                    if (mod.type == 'NODES' and mod.node_group and 
                        hasattr(mod.node_group, 'get')):
                        
                        # Проверяем метаданные
                        linked_effectors = mod.node_group.get("linked_effectors", [])
                        if effector_name in linked_effectors:
                            # Безопасно удаляем из метаданных
                            updated_effectors = [e for e in linked_effectors if e != effector_name]
                            mod.node_group["linked_effectors"] = updated_effectors
                            print(f"[Cloner Cleanup] Удален из метаданных {mod.name}")
                        
                        # Безопасно удаляем физические узлы
                        nodes_to_remove = []
                        try:
                            for node in mod.node_group.nodes:
                                if (hasattr(node, 'name') and 
                                    "Effector" in node.name and 
                                    effector_name in node.name):
                                    nodes_to_remove.append(node)
                        except ReferenceError:
                            print(f"[Cloner Cleanup] Узлы уже недоступны в {mod.name}")
                            continue
                        
                        # ИСПРАВЛЕНИЕ: Используем правильное удаление с восстановлением цепочки
                        for node in nodes_to_remove:
                            try:
                                # КРИТИЧНО: Правильно восстанавливаем цепочку перед удалением узла
                                if self._properly_remove_effector_node_from_cloner(mod, node, effector_name):
                                    print(f"[Cloner Cleanup] ✅ Правильно удален узел из {mod.name}")
                                else:
                                    print(f"[Cloner Cleanup] ⚠️ Проблема удаления узла из {mod.name}")
                            except (ReferenceError, RuntimeError):
                                print(f"[Cloner Cleanup] Узел уже удален из {mod.name}")
                
                except (ReferenceError, AttributeError) as e:
                    print(f"[Cloner Cleanup] Модификатор {mod.name} недоступен: {e}")
                    continue
            
            print(f"[Cloner Cleanup] ✅ {effector_name} удален из всех клонеров")
            
        except Exception as e:
            print(f"[Cloner Cleanup] ❌ Ошибка удаления из клонеров: {e}")
    
    def _properly_remove_effector_node_from_cloner(self, cloner_mod, effector_node, effector_name):
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Правильно удаляет узел эффектора с восстановлением цепочки анти-рекурсии.
        """
        try:
            print(f"[PROPER CLEANUP] Правильное удаление узла {effector_node.name} из {cloner_mod.name}")
            
            # 1. Находим Final Realize Instances узел в клонере
            final_realize_node = None
            for node in cloner_mod.node_group.nodes:
                if node.type == 'REALIZE_INSTANCES' and "Final Realize" in node.name:
                    final_realize_node = node
                    break
            
            if not final_realize_node:
                print(f"[PROPER CLEANUP] ОШИБКА: Final Realize Instances не найден в {cloner_mod.name}")
                # Fallback: простое удаление связей
                for socket in effector_node.inputs:
                    for link in socket.links[:]:
                        cloner_mod.node_group.links.remove(link)
                for socket in effector_node.outputs:
                    for link in socket.links[:]:
                        cloner_mod.node_group.links.remove(link)
                cloner_mod.node_group.nodes.remove(effector_node)
                return False
            
            # 2. Получаем входящую связь эффектора
            input_socket = None
            for link in cloner_mod.node_group.links:
                if link.to_node == effector_node and link.to_socket.name == 'Geometry':
                    input_socket = link.from_socket
                    print(f"[PROPER CLEANUP] Входящая связь: {link.from_socket.node.name}")
                    break
            
            # 3. Проверяем, что эффектор идет в Final Realize
            goes_to_final_realize = False
            for link in cloner_mod.node_group.links:
                if (link.from_node == effector_node and 
                    link.to_node == final_realize_node):
                    goes_to_final_realize = True
                    break
            
            # 4. Удаляем все связи эффектора
            links_to_remove = []
            for link in cloner_mod.node_group.links:
                if link.from_node == effector_node or link.to_node == effector_node:
                    links_to_remove.append(link)
            
            for link in links_to_remove:
                cloner_mod.node_group.links.remove(link)
            
            # 5. КРИТИЧНО: Правильно восстанавливаем цепочку
            if input_socket and goes_to_final_realize:
                # ПРАВИЛЬНО: Подключаем предыдущий узел к Final Realize Instances
                cloner_mod.node_group.links.new(input_socket, final_realize_node.inputs['Geometry'])
                print(f"[PROPER CLEANUP] ✅ Цепочка восстановлена: {input_socket.node.name} -> Final Realize")
            elif input_socket:
                print(f"[PROPER CLEANUP] ВНИМАНИЕ: Эффектор не шел в Final Realize, восстанавливаем как можем")
                # Пытаемся восстановить к Final Realize всё равно
                cloner_mod.node_group.links.new(input_socket, final_realize_node.inputs['Geometry'])
            
            # 6. Удаляем узел эффектора
            cloner_mod.node_group.nodes.remove(effector_node)
            print(f"[PROPER CLEANUP] ✅ Узел эффектора удален из {cloner_mod.name}")
            
            return True
            
        except Exception as e:
            print(f"[PROPER CLEANUP] ❌ Ошибка правильного удаления: {e}")
            # Fallback: простое удаление
            try:
                for socket in effector_node.inputs:
                    for link in socket.links[:]:
                        cloner_mod.node_group.links.remove(link)
                for socket in effector_node.outputs:
                    for link in socket.links[:]:
                        cloner_mod.node_group.links.remove(link)
                cloner_mod.node_group.nodes.remove(effector_node)
                return False
            except:
                return False
    
    def _clear_all_nodegraph_references(self, modifier, node_group):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Очищает все ссылки на node_group.
        """
        try:
            print(f"[NodeGraph Cleanup] Очищаем ссылки на {node_group.name if node_group else 'None'}")
            
            if not node_group:
                return
            
            # Очищаем все метаданные в node_group
            try:
                # ИСПРАВЛЕНИЕ: Очищаем custom properties правильно
                custom_props_to_remove = []
                for key in node_group.keys():
                    # Сохраняем только системные ключи, удаляем наши метаданные
                    if key not in ['_RNA_UI']:  # _RNA_UI - системный ключ Blender
                        custom_props_to_remove.append(key)
                
                for key in custom_props_to_remove:
                    del node_group[key]
                    print(f"[NodeGraph Cleanup] Удален custom property: {key}")
                    
            except (ReferenceError, RuntimeError):
                print(f"[NodeGraph Cleanup] Node group уже недоступна")
                return
            except Exception as clear_error:
                print(f"[NodeGraph Cleanup] Ошибка очистки метаданных: {clear_error}")
                # Продолжаем выполнение
            
            # Очищаем связи с другими объектами
            for obj in bpy.data.objects:
                try:
                    for mod in obj.modifiers:
                        if (mod.type == 'NODES' and 
                            hasattr(mod, 'node_group') and 
                            mod.node_group == node_group):
                            
                            # Если это не наш модификатор, обнуляем ссылку
                            if mod != modifier:
                                mod.node_group = None
                                print(f"[NodeGraph Cleanup] Обнулена ссылка в {obj.name}.{mod.name}")
                except (ReferenceError, AttributeError):
                    continue
            
            print(f"[NodeGraph Cleanup] ✅ Ссылки очищены")
            
        except Exception as e:
            print(f"[NodeGraph Cleanup] ❌ Ошибка очистки ссылок: {e}")
    
    def _invalidate_node_tree_safely(self, node_group):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Безопасно invalidate NodeTree.
        
        Это КРИТИЧНО для предотвращения rna_NodeTree_refine краша.
        """
        try:
            if not node_group:
                return
            
            print(f"[NodeTree Invalidation] Invalidating {node_group.name}")
            
            # КРИТИЧНО: Обновляем node_group перед invalidation
            try:
                node_group.update_tag()
            except (ReferenceError, RuntimeError):
                print(f"[NodeTree Invalidation] Node group уже invalidated")
                return
            
            # КРИТИЧНО: Принудительно обновляем dependency graph
            bpy.context.view_layer.update()
            
            # КРИТИЧНО: Invalidate через Blender API
            try:
                if hasattr(bpy.context, 'evaluated_depsgraph_get'):
                    depsgraph = bpy.context.evaluated_depsgraph_get()
                    depsgraph.update()
            except Exception as dep_error:
                print(f"[NodeTree Invalidation] Ошибка depsgraph: {dep_error}")
            
            print(f"[NodeTree Invalidation] ✅ NodeTree invalidated")
            
        except Exception as e:
            print(f"[NodeTree Invalidation] ❌ Ошибка invalidation: {e}")
    
    def _safe_remove_node_group(self, node_group):
        """
        КРИТИЧЕСКАЯ ЗАЩИТА: Безопасно удаляет node_group.
        """
        try:
            if not node_group:
                return
            
            print(f"[NodeGroup Removal] Проверяем возможность удаления {node_group.name}")
            
            # Проверяем количество пользователей
            try:
                users_count = node_group.users
                print(f"[NodeGroup Removal] Users count: {users_count}")
                
                if users_count <= 0:
                    # Безопасно удаляем
                    node_group_name = node_group.name
                    bpy.data.node_groups.remove(node_group)
                    print(f"[NodeGroup Removal] ✅ NodeGroup {node_group_name} удалена")
                else:
                    print(f"[NodeGroup Removal] NodeGroup {node_group.name} еще используется ({users_count} users)")
                    
            except (ReferenceError, RuntimeError):
                print(f"[NodeGroup Removal] NodeGroup уже удалена")
            
        except Exception as e:
            print(f"[NodeGroup Removal] ❌ Ошибка удаления NodeGroup: {e}")


class CLONERPRO_OT_auto_link_effector(Operator):
    """Автоматически связать эффектор с клонерами"""
    bl_idname = "clonerpro.auto_link_effector"
    bl_label = "Auto-link Effector"
    bl_description = "Automatically link effector to all cloners on this object"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        # Устанавливаем активный объект для контекста
        old_active = context.view_layer.objects.active
        context.view_layer.objects.active = obj
        
        try:
            # Проверяем безопасность операции
            from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
            
            if not is_safe_to_perform_effector_operations():
                self.report({'ERROR'}, "Effector operations are currently blocked for safety")
                return {'CANCELLED'}
            
            # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем старые handlers ПЕРЕД созданием новых
            self._force_cleanup_handlers_before_link(modifier.name)
            
            # Безопасно выполняем авто-связывание
            def _safe_auto_link_operation():
                from ...core.managers.effector_linking import auto_link_effector_to_cloners
                return auto_link_effector_to_cloners(context, modifier)
            
            success, result = safe_execute_effector_operation(_safe_auto_link_operation)
            
            if success and result:
                success_result, message = result
                if success_result:
                    self.report({'INFO'}, f"Effector auto-linked: {message}")
                else:
                    self.report({'WARNING'}, f"Auto-link failed: {message}")
            else:
                self.report({'ERROR'}, "Failed to auto-link effector safely")
        
        finally:
            # Восстанавливаем активный объект
            context.view_layer.objects.active = old_active
        
        return {'FINISHED'}
    
    def _force_cleanup_handlers_before_link(self, effector_name):
        """
        КРИТИЧЕСКИ ВАЖНАЯ функция: Принудительная очистка handlers перед auto-link операцией.
        
        Предотвращает накопление handlers и краши rna_NodeTree_refine при повторных операциях.
        """
        try:
            print(f"[AUTO LINK CLEANUP] Очищаем handlers перед auto-link для {effector_name}")
            
            # Используем централизованную функцию очистки handlers из effector_creation
            from ...core.managers.effector_creation import _force_cleanup_effector_handlers
            _force_cleanup_effector_handlers(effector_name)
            
            # Дополнительная очистка специфичных для auto-link handlers
            handlers_removed = 0
            
            # Очищаем все handlers, которые могут содержать имя эффектора
            handlers_to_remove = []
            for handler in bpy.app.handlers.depsgraph_update_post:
                try:
                    # Проверяем различные способы привязки handler к эффектору
                    handler_contains_effector = False
                    
                    if hasattr(handler, 'effector_name'):
                        if handler.effector_name == effector_name:
                            handler_contains_effector = True
                    
                    if hasattr(handler, '__name__'):
                        if effector_name in str(handler.__name__):
                            handler_contains_effector = True
                    
                    if effector_name in str(handler):
                        handler_contains_effector = True
                    
                    if handler_contains_effector:
                        handlers_to_remove.append(handler)
                        
                except (AttributeError, ReferenceError, TypeError):
                    # Handler поврежден или содержит невалидные ссылки - удаляем
                    handlers_to_remove.append(handler)
            
            for handler in handlers_to_remove:
                try:
                    bpy.app.handlers.depsgraph_update_post.remove(handler)
                    handlers_removed += 1
                    print(f"[AUTO LINK CLEANUP] Удален depsgraph handler #{handlers_removed}")
                except (ValueError, ReferenceError):
                    pass
            
            # Аналогично очищаем frame_change handlers
            handlers_to_remove = []
            for handler in bpy.app.handlers.frame_change_post:
                try:
                    handler_contains_effector = False
                    
                    if hasattr(handler, 'effector_name'):
                        if handler.effector_name == effector_name:
                            handler_contains_effector = True
                    
                    if effector_name in str(handler):
                        handler_contains_effector = True
                    
                    if handler_contains_effector:
                        handlers_to_remove.append(handler)
                        
                except (AttributeError, ReferenceError, TypeError):
                    handlers_to_remove.append(handler)
            
            for handler in handlers_to_remove:
                try:
                    bpy.app.handlers.frame_change_post.remove(handler)
                    handlers_removed += 1
                    print(f"[AUTO LINK CLEANUP] Удален frame_change handler #{handlers_removed}")
                except (ValueError, ReferenceError):
                    pass
            
            print(f"[AUTO LINK CLEANUP] ✅ Очищено {handlers_removed} handlers для {effector_name}")
            
        except Exception as e:
            print(f"[AUTO LINK CLEANUP] Ошибка очистки handlers: {e}")
            # При ошибке выполняем экстренную очистку
            try:
                from ...core.system.safe_operations import emergency_cleanup_invalid_references
                emergency_cleanup_invalid_references()
            except:
                pass


class CLONERPRO_OT_unlink_effector(Operator):
    """Unlink an effector from a cloner"""
    bl_idname = "clonerpro.unlink_effector"
    bl_label = "Unlink Effector"
    bl_description = "Remove effector from the cloner chain"
    bl_options = {'REGISTER', 'UNDO'}

    effector_name: StringProperty(
        name="Effector Name",
        description="Name of the effector to unlink",
        default=""
    )

    cloner_name: StringProperty(
        name="Cloner Name",
        description="Name of the cloner to unlink from",
        default=""
    )

    @classmethod
    def poll(cls, context):
        return context.active_object is not None

    def execute(self, context):
        # Проверяем безопасность операции
        from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
        
        if not is_safe_to_perform_effector_operations():
            self.report({'ERROR'}, "Effector operations are currently blocked for safety")
            return {'CANCELLED'}
        
        obj = context.active_object

        # Получаем модификатор клонера
        cloner_mod = obj.modifiers.get(self.cloner_name)
        if not cloner_mod or not cloner_mod.node_group:
            self.report({'ERROR'}, f"Cloner '{self.cloner_name}' not found")
            return {'CANCELLED'}

        # Проверяем, что эффектор связан с клонером
        if "linked_effectors" not in cloner_mod.node_group:
            self.report({'WARNING'}, f"Cloner '{self.cloner_name}' has no linked effectors")
            return {'CANCELLED'}

        linked_effectors = list(cloner_mod.node_group["linked_effectors"])

        if self.effector_name not in linked_effectors:
            self.report({'WARNING'}, f"Effector '{self.effector_name}' is not linked to this cloner")
            return {'CANCELLED'}

        # Безопасно выполняем удаление эффектора
        def _safe_unlink_operation():
            # Удаляем эффектор из цепочки узлов
            success = self._remove_effector_from_chain(cloner_mod, self.effector_name)
            
            if success:
                # Удаляем из списка связанных эффекторов
                linked_effectors.remove(self.effector_name)
                cloner_mod.node_group["linked_effectors"] = linked_effectors
                
                # Обновляем метаданные цепочки
                self._update_chain_metadata(cloner_mod, self.effector_name)
                
                # Проверяем, используется ли эффектор другими клонерами
                self._check_and_disable_unused_effector(obj, self.effector_name)
                
                return True
            else:
                return False
        
        success, result = safe_execute_effector_operation(_safe_unlink_operation)
        
        if success and result:
            self.report({'INFO'}, f"Effector '{self.effector_name}' unlinked from '{self.cloner_name}'")
        else:
            self.report({'ERROR'}, f"Failed to remove effector from chain safely")
            return {'CANCELLED'}

        return {'FINISHED'}

    def _remove_effector_from_chain(self, cloner_mod, effector_name):
        """
        ИСПРАВЛЕНО: Правильно удаляет эффектор из цепочки с сохранением архитектуры анти-рекурсии.
        
        КРИТИЧНО: Должно правильно восстановить цепочку через Final Realize Instances,
        а НЕ подключать Transform Geometry напрямую к Switch True.
        """
        try:
            print(f"[CHAIN REMOVAL] Начинаем правильное удаление эффектора {effector_name} из цепочки")
            
            # 1. Ищем узел эффектора в wrapper группе
            effector_node = None
            for node in cloner_mod.node_group.nodes:
                if (node.type == 'GROUP' and 
                    node.name.startswith("Effector_") and
                    effector_name in node.name):
                    effector_node = node
                    break
            
            if not effector_node:
                print(f"[CHAIN REMOVAL] Узел эффектора {effector_name} не найден в клонере")
                return False
            
            print(f"[CHAIN REMOVAL] Найден узел эффектора: {effector_node.name}")
            
            # 2. КРИТИЧНО: Определяем архитектуру клонера и находим ключевые узлы
            final_realize_node = None
            transform_geometry_node = None
            logic_group_node = None
            
            for node in cloner_mod.node_group.nodes:
                if node.type == 'REALIZE_INSTANCES' and "Final Realize" in node.name:
                    final_realize_node = node
                    print(f"[CHAIN REMOVAL] Найден Final Realize: {node.name}")
                elif node.type == 'TRANSFORM_GEOMETRY':
                    transform_geometry_node = node  
                    print(f"[CHAIN REMOVAL] Найден Transform Geometry: {node.name}")
                elif node.type == 'GROUP' and hasattr(node, 'node_tree') and node.node_tree:
                    if "Logic" in node.node_tree.name:
                        logic_group_node = node
                        print(f"[CHAIN REMOVAL] Найдена Logic группа: {node.name}")
            
            # 3. КРИТИЧНО: Определяем правильную точку восстановления цепочки
            if not final_realize_node:
                print(f"[CHAIN REMOVAL] ОШИБКА: Final Realize Instances не найден!")
                return False
            
            # 4. Получаем входящую связь эффектора (что было ДО эффектора)
            input_socket = None
            for link in cloner_mod.node_group.links:
                if link.to_node == effector_node and link.to_socket.name == 'Geometry':
                    input_socket = link.from_socket
                    print(f"[CHAIN REMOVAL] Входящая связь: {link.from_socket.node.name} -> эффектор")
                    break
            
            # 5. КРИТИЧНО: Проверяем, что эффектор действительно идет в Final Realize
            effector_goes_to_final_realize = False
            for link in cloner_mod.node_group.links:
                if (link.from_node == effector_node and 
                    link.from_socket.name == 'Geometry' and
                    link.to_node == final_realize_node):
                    effector_goes_to_final_realize = True
                    print(f"[CHAIN REMOVAL] Подтверждено: эффектор идет в Final Realize")
                    break
            
            if not effector_goes_to_final_realize:
                print(f"[CHAIN REMOVAL] ПРЕДУПРЕЖДЕНИЕ: Эффектор не подключен к Final Realize!")
            
            # 6. Удаляем ВСЕ связи эффектора
            links_to_remove = []
            for link in cloner_mod.node_group.links:
                if link.from_node == effector_node or link.to_node == effector_node:
                    links_to_remove.append(link)
                    print(f"[CHAIN REMOVAL] Помечаем для удаления: {link.from_node.name} -> {link.to_node.name}")
            
            for link in links_to_remove:
                cloner_mod.node_group.links.remove(link)
                print(f"[CHAIN REMOVAL] Удалена связь")
            
            # 7. КРИТИЧНО: Правильно восстанавливаем цепочку
            if input_socket:
                # ПРАВИЛЬНО: Подключаем предыдущий узел НАПРЯМУЮ к Final Realize Instances
                cloner_mod.node_group.links.new(input_socket, final_realize_node.inputs['Geometry'])
                print(f"[CHAIN REMOVAL] ✅ ПРАВИЛЬНО восстановлена цепочка: {input_socket.node.name} -> Final Realize Instances")
                
                # КРИТИЧНО: НЕ подключаем к Switch True напрямую!
                print(f"[CHAIN REMOVAL] ✅ Архитектура анти-рекурсии сохранена")
            else:
                print(f"[CHAIN REMOVAL] ОШИБКА: Не найдена входящая связь для восстановления")
                return False
            
            # 8. Удаляем узел эффектора
            cloner_mod.node_group.nodes.remove(effector_node)
            print(f"[DEBUG] Узел эффектора {effector_name} удален из цепочки")
            
            return True
            
        except Exception as e:
            print(f"[ERROR] Ошибка при удалении эффектора из цепочки: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _update_chain_metadata(self, cloner_mod, effector_name):
        """Обновляет метаданные цепочки эффекторов"""
        try:
            # Удаляем из chain_order
            chain_order = list(cloner_mod.node_group.get("effector_chain_order", []))
            if effector_name in chain_order:
                chain_order.remove(effector_name)
                cloner_mod.node_group["effector_chain_order"] = chain_order
            
            # Удаляем из nodes_map
            nodes_map = dict(cloner_mod.node_group.get("effector_nodes_map", {}))
            if effector_name in nodes_map:
                del nodes_map[effector_name]
                cloner_mod.node_group["effector_nodes_map"] = nodes_map
            
            print(f"[DEBUG] Обновлены метаданные цепочки для {effector_name}")
            
        except Exception as e:
            print(f"[ERROR] Ошибка обновления метаданных: {e}")

    def _check_and_disable_unused_effector(self, obj, effector_name):
        """Проверяет и отключает эффектор, если он больше не используется"""
        try:
            # Проверяем, используется ли эффектор другими клонерами на этом объекте
            effector_still_used = False
            for mod in obj.modifiers:
                if (mod.type == 'NODES' and mod.node_group and 
                    "linked_effectors" in mod.node_group):
                    linked_effectors = mod.node_group.get("linked_effectors", [])
                    if effector_name in linked_effectors:
                        effector_still_used = True
                        break
            
            # Если эффектор больше не используется, отключаем его
            if not effector_still_used:
                effector_mod = obj.modifiers.get(effector_name)
                if effector_mod and effector_mod.node_group:
                    # Отключаем видимость
                    effector_mod.show_viewport = False
                    
                    # Отключаем параметры эффектора
                    for socket in effector_mod.node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                            try:
                                if socket.name == "Enable":
                                    effector_mod[socket.identifier] = False
                                elif socket.name == "Strength":
                                    effector_mod[socket.identifier] = 0.0
                            except:
                                pass
                    
                    print(f"[DEBUG] Эффектор {effector_name} отключен (больше не используется)")
            
        except Exception as e:
            print(f"[ERROR] Ошибка при проверке использования эффектора: {e}")


class CLONERPRO_OT_link_effector(Operator):
    """Link an effector to a cloner"""
    bl_idname = "clonerpro.link_effector"
    bl_label = "Link Effector"
    bl_description = "Link selected effector to the cloner"
    bl_options = {'REGISTER', 'UNDO'}

    effector_name: StringProperty(
        name="Effector Name",
        description="Name of the effector to link",
        default=""
    )

    cloner_name: StringProperty(
        name="Cloner Name", 
        description="Name of the cloner to link to",
        default=""
    )

    @classmethod
    def poll(cls, context):
        return context.active_object is not None

    def execute(self, context):
        # Проверяем безопасность операции
        from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
        
        if not is_safe_to_perform_effector_operations():
            self.report({'ERROR'}, "Effector operations are currently blocked for safety")
            return {'CANCELLED'}
        
        obj = context.active_object
        if not obj:
            self.report({'ERROR'}, "No active object")
            return {'CANCELLED'}

        # ИСПРАВЛЕНИЕ: Двойная проверка существования модификаторов
        cloner_mod = obj.modifiers.get(self.cloner_name)
        if not cloner_mod:
            self.report({'ERROR'}, f"Cloner '{self.cloner_name}' not found")
            return {'CANCELLED'}
        
        if cloner_mod.type != 'NODES' or not cloner_mod.node_group:
            self.report({'ERROR'}, f"Cloner '{self.cloner_name}' is not a valid node group")
            return {'CANCELLED'}

        effector_mod = obj.modifiers.get(self.effector_name)
        if not effector_mod:
            self.report({'ERROR'}, f"Effector '{self.effector_name}' not found - it may have been deleted")
            return {'CANCELLED'}
        
        if effector_mod.type != 'NODES' or not effector_mod.node_group:
            self.report({'ERROR'}, f"Effector '{self.effector_name}' is not a valid node group")
            return {'CANCELLED'}
        
        # ИСПРАВЛЕНИЕ: Проверяем, что эффектор все еще существует в списке модификаторов
        current_modifiers = [m.name for m in obj.modifiers]
        if self.effector_name not in current_modifiers:
            self.report({'ERROR'}, f"Effector '{self.effector_name}' was removed during operation")
            return {'CANCELLED'}

        # Безопасно выполняем связывание эффектора
        def _safe_link_operation():
            from ...core.managers.effector_linking import link_effector_to_cloner_simple
            return link_effector_to_cloner_simple(obj, cloner_mod, effector_mod)
        
        success, result = safe_execute_effector_operation(_safe_link_operation)
        
        if success and result:
            self.report({'INFO'}, f"Effector '{self.effector_name}' linked to '{self.cloner_name}'")
        else:
            self.report({'ERROR'}, f"Failed to link effector to cloner safely")
            return {'CANCELLED'}

        return {'FINISHED'}


class CLONERPRO_OT_cleanup_orphaned_effectors(Operator):
    """Cleanup orphaned effectors from all cloner chains"""
    bl_idname = "clonerpro.cleanup_orphaned_effectors"
    bl_label = "Cleanup Orphaned Effectors"
    bl_description = "Remove deleted effectors from all cloner chains"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        return True  # Всегда доступен

    def execute(self, context):
        # Проверяем безопасность операции
        from ...core.system.dependency_safety import is_safe_to_perform_effector_operations, safe_execute_effector_operation
        
        if not is_safe_to_perform_effector_operations():
            self.report({'ERROR'}, "Effector operations are currently blocked for safety")
            return {'CANCELLED'}
        
        def _safe_cleanup_operation():
            from ...core.cleanup.effector_cleanup import cleanup_unused_effectors
            return cleanup_unused_effectors()
        
        success, cleaned_count = safe_execute_effector_operation(_safe_cleanup_operation)
        
        if success:
            if cleaned_count > 0:
                self.report({'INFO'}, f"Cleaned up {cleaned_count} orphaned effectors")
            else:
                self.report({'INFO'}, "No orphaned effectors found")
        else:
            self.report({'ERROR'}, "Failed to cleanup orphaned effectors safely")
            return {'CANCELLED'}

        return {'FINISHED'}


class CLONERPRO_OT_emergency_reset_safety(Operator):
    """Emergency reset of all safety systems"""
    bl_idname = "clonerpro.emergency_reset_safety"
    bl_label = "Emergency Reset Safety Systems"
    bl_description = "Reset all safety locks and clear crashed state (use if system becomes unresponsive)"
    bl_options = {'REGISTER'}

    @classmethod
    def poll(cls, context):
        return True  # Всегда доступен для экстренных случаев

    def execute(self, context):
        try:
            from ...core.system.dependency_safety import emergency_reset_all_locks
            emergency_reset_all_locks()
            
            self.report({'INFO'}, "Emergency reset completed - all safety locks cleared")
            
        except Exception as e:
            self.report({'ERROR'}, f"Emergency reset failed: {e}")
            
        return {'FINISHED'}


class CLONERPRO_OT_diagnose_safety_system(Operator):
    """Диагностика системы безопасности"""
    bl_idname = "clonerpro.diagnose_safety_system"
    bl_label = "Diagnose Safety System"
    bl_description = "Diagnose effector-cloner relationships and safety system status"
    bl_options = {'REGISTER'}

    @classmethod
    def poll(cls, context):
        return True

    def execute(self, context):
        try:
            from ...core.system.dependency_safety import diagnose_effector_cloner_relationships
            
            diagnosis = diagnose_effector_cloner_relationships()
            
            relationships = diagnosis.get("relationships_found", 0)
            problems = diagnosis.get("problems_detected", 0)
            
            if problems == 0:
                self.report({'INFO'}, f"System healthy: {relationships} relationships found, no problems detected")
            else:
                self.report({'WARNING'}, f"Issues detected: {relationships} relationships, {problems} problems found")
            
        except Exception as e:
            self.report({'ERROR'}, f"Diagnosis failed: {e}")
            
        return {'FINISHED'}


class CLONERPRO_OT_repair_safety_system(Operator):
    """Восстановление системы безопасности"""
    bl_idname = "clonerpro.repair_safety_system"
    bl_label = "Repair Safety System"
    bl_description = "Repair all effector-cloner relationships and fix safety issues"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        return True

    def execute(self, context):
        try:
            from ...core.system.dependency_safety import repair_all_effector_cloner_relationships
            
            success = repair_all_effector_cloner_relationships()
            
            if success:
                self.report({'INFO'}, "Safety system repaired successfully")
            else:
                self.report({'WARNING'}, "Safety system repair completed with some issues")
            
        except Exception as e:
            self.report({'ERROR'}, f"Repair failed: {e}")
            
        return {'FINISHED'}


def register():
    """Регистрация операторов эффекторов"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_effector_expanded)
    bpy.utils.register_class(CLONERPRO_OT_toggle_effector_visibility)
    bpy.utils.register_class(CLONERPRO_OT_delete_effector)
    bpy.utils.register_class(CLONERPRO_OT_auto_link_effector)
    bpy.utils.register_class(CLONERPRO_OT_unlink_effector)
    bpy.utils.register_class(CLONERPRO_OT_link_effector)
    bpy.utils.register_class(CLONERPRO_OT_cleanup_orphaned_effectors)
    bpy.utils.register_class(CLONERPRO_OT_emergency_reset_safety)
    bpy.utils.register_class(CLONERPRO_OT_diagnose_safety_system)
    bpy.utils.register_class(CLONERPRO_OT_repair_safety_system)


def unregister():
    """Отмена регистрации операторов эффекторов"""
    bpy.utils.unregister_class(CLONERPRO_OT_repair_safety_system)
    bpy.utils.unregister_class(CLONERPRO_OT_diagnose_safety_system)
    bpy.utils.unregister_class(CLONERPRO_OT_emergency_reset_safety)
    bpy.utils.unregister_class(CLONERPRO_OT_cleanup_orphaned_effectors)
    bpy.utils.unregister_class(CLONERPRO_OT_link_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_unlink_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_auto_link_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_delete_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_effector_visibility)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_effector_expanded)