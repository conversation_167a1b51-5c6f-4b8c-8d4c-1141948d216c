"""
Chain Deletion - Безопасное удаление клонеров с восстановлением цепей
"""

import bpy
from ...core.core import get_cloner_modifier, get_cloner_info
from .utils import chain_debug_print


def delete_cloner_with_chain_recovery(context, cloner_obj, modifier_name):
    """
    Удалить клонер с автоматическим восстановлением цепи
    Основная функция для использования в операторах удаления
    """
    print(f"🗑️ [CHAIN] Deleting cloner {cloner_obj.name}::{modifier_name} with chain recovery")
    
    # Найдем модификатор для удаления
    modifier = None
    for mod in cloner_obj.modifiers:
        if mod.name == modifier_name:
            modifier = mod
            break
    
    if not modifier:
        print(f"⚠️ [CHAIN] Modifier {modifier_name} not found")
        return False
    
    # ИСПРАВЛЕНИЕ: Проверяем тип клонера и количество клонеров на объекте
    is_stacked = modifier.get("is_stacked_cloner", False)
    cloner_mode = modifier.get("cloner_mode", "OBJECT")
    
    # Подсчитываем количество клонеров на объекте
    cloner_count = 0
    cloner_names = []
    for mod in cloner_obj.modifiers:
        if (mod.type == 'NODES' and mod.node_group and 
            (mod.get("cloner_type") or mod.get("cloner_mode"))):
            cloner_count += 1
            cloner_names.append(f"{mod.name}({mod.get('cloner_mode', 'UNKNOWN')})")
    will_delete_object = cloner_mode != "STACKED" and cloner_count == 1
    
    print(f"🗑️ [CHAIN] Cloner analysis:")
    print(f"    Type: is_stacked={is_stacked}, mode={cloner_mode}")
    print(f"    Cloners on object: {cloner_count} - {cloner_names}")
    print(f"    Will delete object: {will_delete_object}")
    
    # ШАГ 1: Подготавливаем информацию о цепи ТОЛЬКО если удаляем весь объект
    chain_info = None
    if will_delete_object:
        from .restoration import prepare_chain_restoration_info
        chain_info = prepare_chain_restoration_info(context, cloner_obj)
        chain_debug_print(2, f"🔗 [CHAIN] Chain info prepared for object deletion: {chain_info}")
    else:
        chain_debug_print(2, f"🔗 [CHAIN] Modifier-only deletion - skipping chain preparation")
    
    # ШАГ 2: Выполняем удаление используя упрощённую логику
    try:
        success = execute_cloner_deletion(context, cloner_obj, modifier, will_delete_object)
        
        if not success:
            print(f"🚨 [CHAIN] Standard deletion failed")
            return False
            
        print(f"✓ [CHAIN] Standard deletion completed")
    
    except Exception as e:
        print(f"🚨 [CHAIN] Deletion failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # ШАГ 3: Восстанавливаем цепь ПОСЛЕ удаления (только если удалили весь объект)
    if will_delete_object and chain_info and chain_info.get("is_chained", False):
        from .restoration import restore_chain_connections_after_deletion
        success = restore_chain_connections_after_deletion(context, chain_info)
        if success:
            print("✅ [CHAIN] Chain recovery completed successfully")
        else:
            print("⚠️ [CHAIN] Chain recovery had issues")
    else:
        if will_delete_object:
            print("✅ [CHAIN] Object deletion completed (no chain to restore)")
        else:
            print("✅ [CHAIN] Modifier deletion completed (object preserved)")
        success = True
    
    # ШАГ 4: Принудительно обновляем браузер клонеров
    try:
        from ...core.services.cloner_scanner import force_refresh_cloner_browser
        force_refresh_cloner_browser()
        print("🔄 [CHAIN] Browser refreshed after deletion")
    except Exception as e:
        print(f"⚠️ [CHAIN] Browser refresh failed: {e}")
    
    return success


def execute_cloner_deletion(context, cloner_obj, modifier, will_delete_object=True):
    """Выполнить стандартное удаление клонера"""
    try:
        print(f"🗑️ [CHAIN] Executing deletion of {cloner_obj.name}::{modifier.name}")
        print(f"🗑️ [CHAIN] Will delete object: {will_delete_object}")
        
        # Определяем тип удаления
        cloner_mode = modifier.get("cloner_mode", "OBJECT")
        
        if cloner_mode == "STACKED" or not will_delete_object:
            # Стековые клонеры или случай когда на объекте несколько клонеров
            return delete_stacked_cloner_simple(context, cloner_obj, modifier)
        elif cloner_mode == "COLLECTION":
            return delete_collection_cloner_simple(context, cloner_obj, modifier)
        else:
            return delete_object_cloner_simple(context, cloner_obj, modifier)
            
    except Exception as e:
        print(f"❌ [CHAIN] Error executing cloner deletion: {e}")
        return False


def delete_stacked_cloner_simple(context, cloner_obj, modifier):
    """Удалить Stacked клонер (просто удаляем модификатор)"""
    try:
        print(f"🗑️ [CHAIN] Deleting stacked cloner modifier {modifier.name}")
        
        # Для Stacked клонеров просто удаляем модификатор
        cloner_obj.modifiers.remove(modifier)
        
        # УСИЛЕННОЕ обновление для корректного отображения в браузере
        bpy.context.view_layer.update()
        
        # Дополнительно обновляем dependency graph
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            try:
                depsgraph = bpy.context.evaluated_depsgraph_get()
                depsgraph.update()
            except:
                pass
        
        print(f"✅ [CHAIN] Stacked cloner deletion completed")
        return True
        
    except Exception as e:
        print(f"❌ [CHAIN] Error deleting stacked cloner: {e}")
        return False


def delete_object_cloner_simple(context, cloner_obj, modifier):
    """Удалить Object клонер (восстанавливаем видимость оригинального объекта)"""
    try:
        print(f"🗑️ [CHAIN] Deleting object cloner {cloner_obj.name}")
        
        # 1. Восстановить видимость оригинального объекта
        original_obj_name = modifier.get("original_object", "")
        if original_obj_name and original_obj_name in bpy.data.objects:
            try:
                original_obj = bpy.data.objects[original_obj_name]
                original_obj.hide_viewport = False
                print(f"🗑️ [CHAIN] Restored visibility of original object: {original_obj_name}")
            except Exception as e:
                print(f"⚠️ [CHAIN] Error restoring original object visibility: {e}")
        
        # 2. Сохранить node group перед удалением для очистки
        node_group = modifier.node_group
        
        # 3. Удалить объект клонера
        try:
            # Удаляем объект из всех коллекций
            for collection in cloner_obj.users_collection:
                collection.objects.unlink(cloner_obj)
            
            # Удаляем mesh data если она больше не используется
            mesh_data = cloner_obj.data
            bpy.data.objects.remove(cloner_obj)
            
            if mesh_data and mesh_data.users == 0:
                bpy.data.meshes.remove(mesh_data)
                print(f"🗑️ [CHAIN] Removed unused mesh data")
                
        except Exception as e:
            print(f"⚠️ [CHAIN] Error removing object: {e}")
        
        # 4. Очистить node group если больше не используется
        if node_group and node_group.users <= 1:
            try:
                bpy.data.node_groups.remove(node_group)
                print(f"🗑️ [CHAIN] Removed unused node group: {node_group.name}")
            except Exception as e:
                print(f"⚠️ [CHAIN] Error removing node group: {e}")
        
        # 5. Обновить view layer
        bpy.context.view_layer.update()
        
        print(f"✅ [CHAIN] Object cloner deletion completed")
        return True
        
    except Exception as e:
        print(f"❌ [CHAIN] Error deleting object cloner: {e}")
        return False


def delete_collection_cloner_simple(context, cloner_obj, modifier):
    """Удалить Collection клонер (удаляем объект + очищаем коллекцию)"""
    try:
        print(f"🗑️ [CHAIN] Deleting collection cloner {cloner_obj.name}")
        
        # 1. Получить информацию о коллекции клонера
        cloner_collection_name = modifier.get("cloner_collection", "")
        
        # 2. Сохранить node group перед удалением для очистки
        node_group = modifier.node_group
        
        # 3. Удалить объект клонера
        try:
            # Удаляем объект из всех коллекций
            for collection in cloner_obj.users_collection:
                collection.objects.unlink(cloner_obj)
            
            # Удаляем mesh data если она больше не используется
            mesh_data = cloner_obj.data
            bpy.data.objects.remove(cloner_obj)
            
            if mesh_data and mesh_data.users == 0:
                bpy.data.meshes.remove(mesh_data)
                print(f"🗑️ [CHAIN] Removed unused mesh data")
                
        except Exception as e:
            print(f"⚠️ [CHAIN] Error removing cloner object: {e}")
        
        # 4. Безопасно удалить коллекцию клонера
        if cloner_collection_name:
            remove_collection_safely(cloner_collection_name, exclude_obj=None)
        
        # 5. Очистить node group если больше не используется
        if node_group and node_group.users <= 1:
            try:
                bpy.data.node_groups.remove(node_group)
                print(f"🗑️ [CHAIN] Removed unused node group: {node_group.name}")
            except Exception as e:
                print(f"⚠️ [CHAIN] Error removing node group: {e}")
        
        # 6. Обновить view layer
        bpy.context.view_layer.update()
        
        print(f"✅ [CHAIN] Collection cloner deletion completed")
        return True
        
    except Exception as e:
        print(f"❌ [CHAIN] Error deleting collection cloner: {e}")
        return False


def remove_collection_safely(collection_name, exclude_obj=None):
    """Безопасно удалить коллекцию с проверками"""
    if not collection_name or collection_name not in bpy.data.collections:
        return
    
    collection = bpy.data.collections[collection_name]
    
    try:
        print(f"🗑️ [CHAIN] Safely removing collection: {collection_name}")
        
        # Проверяем, есть ли объекты в коллекции (кроме исключенного)
        objects_to_keep = []
        for obj in collection.objects:
            if exclude_obj is None or obj != exclude_obj:
                objects_to_keep.append(obj)
        
        if objects_to_keep:
            print(f"🗑️ [CHAIN] Collection {collection_name} contains {len(objects_to_keep)} objects, keeping it")
            return
        
        # Удаляем коллекцию из parent коллекций
        for parent_collection in bpy.data.collections:
            if collection.name in parent_collection.children:
                parent_collection.children.unlink(collection)
        
        # Удаляем из scene collection если нужно
        if collection.name in bpy.context.scene.collection.children:
            bpy.context.scene.collection.children.unlink(collection)
        
        # Удаляем коллекцию
        bpy.data.collections.remove(collection)
        print(f"✅ [CHAIN] Successfully removed collection: {collection_name}")
        
    except Exception as e:
        print(f"⚠️ [CHAIN] Error removing collection {collection_name}: {e}")


def restore_collection_visibility_simple(context, collection):
    """Восстановить видимость коллекции в viewport"""
    if not collection:
        return
    
    try:
        def find_layer_collection(layer_collection, target_collection):
            if layer_collection.collection == target_collection:
                return layer_collection
            for child in layer_collection.children:
                result = find_layer_collection(child, target_collection)
                if result:
                    return result
            return None
        
        layer_collection = find_layer_collection(context.view_layer.layer_collection, collection)
        if layer_collection:
            layer_collection.hide_viewport = False
    except Exception as e:
        print(f"⚠️ [CHAIN] Error restoring collection visibility: {e}")