# Примеры общих задач ClonerPro

## Введение

Этот документ содержит практические примеры решения типичных задач клонирования с помощью ClonerPro. Каждый пример включает пошаговые инструкции и код для автоматизации.

## Базовые задачи клонирования

### Создание простой сетки объектов

**Задача:** Создать сетку из кубов 5x5 с расстоянием 3 единицы между ними.

#### Через UI:
1. Создайте куб (`Shift+A` → Mesh → Cube)
2. В панели ClonerPro нажмите `Grid Cloner`
3. Выберите режим `Object`
4. В Properties → Modifier Properties настройте:
   - Count X: 5
   - Count Y: 5
   - Spacing: 3.0

#### Через единый Python API:
```python
import bpy
from core.templates.cloner_creation import create_cloner_unified

# Создание исходного куба
bpy.ops.mesh.primitive_cube_add()
source_cube = bpy.context.active_object

# Создание Grid клонера через единый API (автоматически выберет unified систему)
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=source_cube
)

# Настройка параметров
modifier = grid_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 5
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 5
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 3.0

print("✅ Сетка 5x5 создана через единый API")
```

### Линейный массив с убывающим масштабом

**Задача:** Создать линейный массив из 10 объектов с постепенно уменьшающимся масштабом.

#### Через UI:
1. Создайте объект
2. Создайте Linear клонер в режиме Object
3. Установите Count: 10, Distance: 2.0
4. Добавьте Random эффектор с масштабом

#### Через единый Python API:
```python
import bpy
from core.templates.cloner_creation import create_cloner_unified

# Создание исходного объекта
bpy.ops.mesh.primitive_torus_add()
source_torus = bpy.context.active_object

# Создание Linear клонера через единый API (автоматически выберет unified систему)
linear_cloner = create_cloner_unified(
    cloner_type="LINEAR",
    mode="OBJECT",
    source=source_torus
)

# Настройка параметров Linear клонера
modifier = linear_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 10
        elif socket.name == 'Distance':
            modifier[socket.identifier] = 2.0
        # Используем встроенную рандомизацию BaseCloner
        elif socket.name == 'Random Scale':
            modifier[socket.identifier] = 0.3  # 30% вариации масштаба
        elif socket.name == 'Random Seed':
            modifier[socket.identifier] = 42

print("✅ Линейный массив с случайным масштабом создан через единый API")
```

### Круговое размещение мебели

**Задача:** Разместить мебель по кругу в комнате.

#### Через UI:
1. Создайте коллекцию "Furniture" с мебелью (стол, стулья, лампа)
2. Создайте Circle клонер в режиме Collection
3. Выберите коллекцию "Furniture"
4. Настройте Count: 8, Radius: 5.0

#### Через единый Python API:
```python
import bpy
from core.templates.cloner_creation import create_cloner_unified

# Создание коллекции мебели
furniture_collection = bpy.data.collections.new("Furniture")
bpy.context.scene.collection.children.link(furniture_collection)

# Добавление мебели в коллекцию
bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0.5))
table = bpy.context.active_object
table.name = "Table"
furniture_collection.objects.link(table)
bpy.context.scene.collection.objects.unlink(table)

bpy.ops.mesh.primitive_cube_add(size=0.5, location=(1, 0, 0.25))
chair = bpy.context.active_object  
chair.name = "Chair"
furniture_collection.objects.link(chair)
bpy.context.scene.collection.objects.unlink(chair)

# Создание Circle клонера для коллекции через единый API
circle_cloner = create_cloner_unified(
    cloner_type="CIRCLE",
    mode="COLLECTION",
    source="Furniture"
)

# Настройка параметров
modifier = circle_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 8
        elif socket.name == 'Radius':
            modifier[socket.identifier] = 5.0
        # Добавляем случайный поворот для разнообразия
        elif socket.name == 'Random Rotation':
            modifier[socket.identifier] = (0.0, 0.0, 0.5)  # Случайный поворот по Z

print("✅ Мебель размещена по кругу через единый API")
```

## Mesh-based клонирование

### Распределение объектов по поверхности

**Задача:** Разместить деревья на ландшафте.

#### Через UI:
1. Создайте ландшафт (plane с subdivision и noise)
2. Создайте дерево (объект для клонирования)
3. Выберите ландшафт
4. Создайте Object клонер в режиме Stacked
5. Настройте Distribution Mode: Faces

#### Через единый Python API:
```python
import bpy
from core.templates.cloner_creation import create_cloner_unified

# Создание ландшафта
bpy.ops.mesh.primitive_plane_add(size=20)
landscape = bpy.context.active_object
landscape.name = "Landscape"

# Добавление subdivision для большего количества фейсов
bpy.context.view_layer.objects.active = landscape
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.mesh.subdivide(number_cuts=10)
bpy.ops.object.mode_set(mode='OBJECT')

# Создание дерева
bpy.ops.mesh.primitive_uv_sphere_add(location=(5, 5, 2))
tree = bpy.context.active_object
tree.name = "Tree"

# Создание Object клонера на ландшафте через единый API (автоматически выберет mesh систему)
landscape.select_set(True)
bpy.context.view_layer.objects.active = landscape

object_modifier = create_cloner_unified(
    cloner_type="OBJECT",
    mode="STACKED",
    source=landscape
)

# Настройка параметров (клонирование по фейсам)
for socket in object_modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Distribution Mode':
            object_modifier[socket.identifier] = 2  # Faces mode
        elif socket.name == 'Instance Scale':
            object_modifier[socket.identifier] = (0.5, 0.5, 0.5)
        # Используем встроенную рандомизацию
        elif socket.name == 'Random Position':
            object_modifier[socket.identifier] = (0.0, 0.0, 1.0)  # Случайная высота
        elif socket.name == 'Random Scale':
            object_modifier[socket.identifier] = 0.3  # Разнообразие размеров
        elif socket.name == 'Align to Normal':
            object_modifier[socket.identifier] = True

print("✅ Деревья размещены на ландшафте")
```

### Клонирование по сплайну (дорога)

**Задача:** Создать дорогу с фонарными столбами вдоль сплайна.

#### Через Python API:
```python
import bpy
from core.templates.mesh_creation import create_mesh_cloner

# Создание сплайна-дороги
bpy.ops.curve.primitive_bezier_curve_add()
road_curve = bpy.context.active_object
road_curve.name = "Road"

# Расширение сплайна
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.curve.extrude_move(TRANSFORM_OT_translate={"value":(5, 5, 0)})
bpy.ops.curve.extrude_move(TRANSFORM_OT_translate={"value":(10, 0, 0)})
bpy.ops.object.mode_set(mode='OBJECT')

# Создание фонарного столба
bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=3, location=(10, 10, 1.5))
lamp_post = bpy.context.active_object
lamp_post.name = "LampPost"

# Создание Spline клонера
bpy.context.view_layer.objects.active = road_curve

spline_modifier = create_mesh_cloner(
    cloner_type="SPLINE",
    mode="STACKED",
    target_obj=road_curve
)

# Настройка параметров
for socket in spline_modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            spline_modifier[socket.identifier] = 20
        elif socket.name == 'Offset':
            spline_modifier[socket.identifier] = 2.0  # Смещение от дороги

print("✅ Фонарные столбы размещены вдоль дороги")
```

## Сложные сценарии

### Архитектурное клонирование (окна здания)

**Задача:** Создать сетку окон на фасаде здания.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified

# Создание основы здания
bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
building = bpy.context.active_object
building.scale = (10, 1, 5)  # Широкое и высокое здание
building.name = "Building"

# Создание окна
bpy.ops.mesh.primitive_cube_add(size=0.8, location=(5, 0, 1))
window = bpy.context.active_object
window.scale = (1, 0.1, 1.2)
window.name = "Window"

# Создание Grid клонера для окон
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=window
)

# Настройка сетки окон
modifier = grid_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 8  # 8 окон по ширине
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 4  # 4 этажа
        elif socket.name == 'Count Z':
            modifier[socket.identifier] = 1
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 2.5

# Позиционирование клонера окон
grid_cloner.location = (-8.75, 0.1, -1.25)

print("✅ Окна размещены на фасаде здания")
```

### Создание цепочки клонеров (сложная структура)

**Задача:** Создать фрактальную структуру: сфера → Grid клонер → Object клонер → Linear клонер.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified
from core.templates.mesh_creation import create_mesh_cloner
from core.uuid.manager import BlenderClonerUUIDManager

# 1. Исходная сфера
bpy.ops.mesh.primitive_uv_sphere_add()
original_sphere = bpy.context.active_object
original_sphere.name = "OriginalSphere"
original_sphere.scale = (0.5, 0.5, 0.5)

# 2. Grid клонер от сферы
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=original_sphere
)

# Настройка Grid клонера (маленькая сетка)
modifier = grid_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name in ['Count X', 'Count Y']:
            modifier[socket.identifier] = 3
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 1.5

# 3. Object клонер от Grid клонера
object_modifier = create_mesh_cloner(
    cloner_type="OBJECT",
    mode="STACKED",
    target_obj=grid_cloner
)

# Настройка Object клонера
for socket in object_modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Distribution Mode':
            object_modifier[socket.identifier] = 0  # Vertices
        elif socket.name == 'Instance Scale':
            object_modifier[socket.identifier] = 0.3

# 4. Linear клонер от результата Object клонера
linear_cloner = create_cloner_unified(
    cloner_type="LINEAR", 
    mode="OBJECT",
    source=grid_cloner  # Используем grid_cloner как источник для цепочки
)

# Настройка Linear клонера
modifier = linear_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 5
        elif socket.name == 'Distance':
            modifier[socket.identifier] = 8.0

# Проверка цепочки
result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
print(f"✅ Создана цепочка из {len(result['cloners_by_uuid'])} клонеров")
print(f"Цепочек в сцене: {len(result['chains_by_uuid'])}")

# Детали цепочки
for chain_uuid, cloners in result['chains_by_uuid'].items():
    print(f"Цепочка {chain_uuid[:8]}:")
    for obj, mod in cloners:
        info = get_cloner_info(mod)
        print(f"  → {obj.name} ({info['type']})")
```

## Работа с эффекторами

### Применение случайности к клонированию

**Задача:** Создать естественно выглядящий лес с случайными позициями и размерами деревьев.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified
from core.managers.effector_creation import create_effector

# Создание дерева
bpy.ops.mesh.primitive_cylinder_add(radius=0.2, depth=3)
trunk = bpy.context.active_object
trunk.location = (0, 0, 1.5)

bpy.ops.mesh.primitive_uv_sphere_add(location=(0, 0, 4))
crown = bpy.context.active_object
crown.scale = (1.5, 1.5, 1.5)

# Объединение в одно дерево
bpy.context.view_layer.objects.active = trunk
trunk.select_set(True)
crown.select_set(True)
bpy.ops.object.join()
tree = bpy.context.active_object
tree.name = "Tree"

# Создание Grid клонера для леса
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT", 
    source=tree
)

# Настройка плотной сетки
modifier = grid_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 10
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 10
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 3.0

# Применение Random эффектора для естественности
random_effector = create_effector(
    effector_type="RANDOM",
    target_cloner=grid_cloner,
    position_strength=1.0,      # Случайные позиции
    rotation_strength=180.0,    # Случайные повороты  
    scale_strength=0.3,         # Случайные размеры
    seed=42
)

print("✅ Лес с естественной случайностью создан")
```

## Практические архитектурные сценарии

### Создание лестницы

**Задача:** Создать лестницу с перилами.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified

# Создание ступени
bpy.ops.mesh.primitive_cube_add()
step = bpy.context.active_object
step.scale = (2, 0.3, 0.1)
step.name = "Step"

# Создание Linear клонера для ступеней
steps_cloner = create_cloner_unified(
    cloner_type="LINEAR",
    mode="OBJECT",
    source=step
)

# Настройка лестницы
modifier = steps_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 15
        elif socket.name == 'Distance':
            modifier[socket.identifier] = 0.5

# Поворот для создания подъёма
steps_cloner.rotation_euler = (0.2, 0, 0)  # Наклон лестницы

# Создание перил
bpy.ops.mesh.primitive_cylinder_add(radius=0.05, depth=0.5)
rail_post = bpy.context.active_object
rail_post.location = (1.2, 0, 0.25)
rail_post.name = "RailPost"

# Linear клонер для перил
rails_cloner = create_cloner_unified(
    cloner_type="LINEAR",
    mode="OBJECT",
    source=rail_post
)

# Настройка перил (следуют за ступенями)
modifier = rails_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 15
        elif socket.name == 'Distance':
            modifier[socket.identifier] = 0.5

rails_cloner.rotation_euler = (0.2, 0, 0)

print("✅ Лестница с перилами создана")
```

### Создание паркетного пола

**Задача:** Создать паркетный пол с чередующимся паттерном.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified

# Создание паркетной доски
bpy.ops.mesh.primitive_cube_add()
plank = bpy.context.active_object
plank.scale = (2, 0.2, 0.02)
plank.name = "Plank"

# Создание первого ряда (горизонтальные доски)
row1_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=plank
)

modifier = row1_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 10
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 5
        elif socket.name == 'Count Z':
            modifier[socket.identifier] = 1
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 0.41

# Создание второго ряда (вертикальные доски)
plank.rotation_euler = (0, 0, 1.5708)  # 90 градусов
row2_cloner = create_cloner_unified(
    cloner_type="GRID", 
    mode="OBJECT",
    source=plank
)

modifier = row2_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 10
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 5
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 0.41

# Смещение второго ряда
row2_cloner.location = (0.205, 0.205, 0)

print("✅ Паркетный пол создан")
```

## Цепочки клонеров (Chain Management)

### Создание сложной цепочки клонеров

**Задача:** Создать цепочку: Cube → Grid → Object → Linear с автоматическим обнаружением цепочки.

#### Через единый Python API:
```python
import bpy
from core.templates.cloner_creation import create_cloner_unified

# 1. Исходный куб
bpy.ops.mesh.primitive_cube_add()
original_cube = bpy.context.active_object
original_cube.name = "OriginalCube"

# 2. Grid клонер от куба (начало цепочки)
print("Создание Grid клонера...")
grid_cloner = create_cloner_unified("GRID", "OBJECT", original_cube)

# Настройка Grid клонера
modifier = grid_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count X':
            modifier[socket.identifier] = 3
        elif socket.name == 'Count Y':
            modifier[socket.identifier] = 3
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 2.0

# 3. Создание поверхности для Object клонера
bpy.ops.mesh.primitive_plane_add(size=10, location=(10, 0, 0))
surface = bpy.context.active_object
surface.name = "Surface"

# 4. Object клонер от Grid клонера (автоматическое обнаружение цепочки!)
print("Создание Object клонера - система автоматически обнаружит цепочку...")
object_cloner = create_cloner_unified("OBJECT", "STACKED", surface)

# 5. Linear клонер от Grid клонера (расширение цепочки)
print("Создание Linear клонера - расширение цепочки...")
linear_cloner = create_cloner_unified("LINEAR", "OBJECT", grid_cloner)

# Настройка Linear клонера
modifier = linear_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name == 'Count':
            modifier[socket.identifier] = 5
        elif socket.name == 'Distance':
            modifier[socket.identifier] = 1.5

print("✅ Цепочка клонеров создана: Cube → Grid → Object → Linear")
print("✅ UUID система автоматически связала все клонеры в цепочку")
```

### Диагностика цепочки клонеров

**Задача:** Проверить состояние созданной цепочки и вывести информацию.

```python
from core.uuid.manager import BlenderClonerUUIDManager
from core.core import get_cloner_modifier, get_cloner_info

# Сканирование всех клонеров в сцене
print("Сканирование клонеров в сцене...")
result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()

print(f"📊 Статистика:")
print(f"  UUID клонеры: {len(result['cloners_by_uuid'])}")
print(f"  Цепочки: {len(result['chains_by_uuid'])}")
print(f"  Legacy клонеры: {len(result['legacy_cloners'])}")

# Подробная информация о каждом клонере
print(f"\n🔍 Детальная информация о клонерах:")
for cloner_uuid, (obj, modifier) in result['cloners_by_uuid'].items():
    info = get_cloner_info(modifier)
    print(f"  {obj.name}:")
    print(f"    - Тип: {info['type']}")
    print(f"    - Режим: {info['mode']}")
    print(f"    - Система: {info['system']}")
    print(f"    - UUID: {info['cloner_uuid'][:8]}...")
    print(f"    - Chain UUID: {info.get('chain_uuid', 'НЕТ')[:8] if info.get('chain_uuid') else 'НЕТ'}...")

# Информация о цепочках
print(f"\n🔗 Информация о цепочках:")
for chain_uuid, cloners_in_chain in result['chains_by_uuid'].items():
    print(f"  Chain {chain_uuid[:8]}... ({len(cloners_in_chain)} клонеров):")
    for obj, modifier in cloners_in_chain:
        info = get_cloner_info(modifier)
        sequence = info.get('chain_sequence', 0)
        print(f"    [{sequence}] {obj.name} ({info['type']})")

print("✅ Диагностика цепочки завершена")
```

### Восстановление разорванной цепочки

**Задача:** Восстановить связи в цепочке после манипуляций с объектами.

```python
from core.chain.restoration import restore_cloner_chains_on_file_load
from core.chain.validation import validate_cloner_chains

# Валидация текущих цепочек
print("Валидация цепочек...")
validation_result = validate_cloner_chains()

if validation_result['broken_chains']:
    print(f"⚠️ Найдено {len(validation_result['broken_chains'])} сломанных цепочек")
    
    # Восстановление цепочек
    print("Восстановление цепочек...")
    restore_cloner_chains_on_file_load()
    print("✅ Цепочки восстановлены")
else:
    print("✅ Все цепочки в порядке")
```

## Оптимизация и производительность

### Создание LOD системы

**Задача:** Создать систему уровней детализации для большой сцены.

#### Через Python API:
```python
import bpy
from core.templates.unified_creation import create_cloner_unified

# Создание высокодетализированного объекта
bpy.ops.mesh.primitive_monkey_add()
high_detail = bpy.context.active_object
high_detail.name = "HighDetail"

# Добавление subdivision для детализации
modifier = high_detail.modifiers.new(name="Subdivision", type='SUBSURF')
modifier.levels = 3

# Создание низкодетализированного объекта
bpy.ops.mesh.primitive_cube_add(location=(5, 0, 0))
low_detail = bpy.context.active_object
low_detail.name = "LowDetail"

# Клонер для близких объектов (высокая детализация)
close_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=high_detail
)

modifier = close_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name in ['Count X', 'Count Y']:
            modifier[socket.identifier] = 3  # Небольшая сетка
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 3.0

# Клонер для дальних объектов (низкая детализация)
far_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT",
    source=low_detail
)

modifier = far_cloner.modifiers[0]
for socket in modifier.node_group.interface.items_tree:
    if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
        if socket.name in ['Count X', 'Count Y']:
            modifier[socket.identifier] = 10  # Большая сетка
        elif socket.name == 'Spacing':
            modifier[socket.identifier] = 5.0

far_cloner.location = (30, 30, 0)  # Далеко от камеры

print("✅ LOD система создана")
```

## Диагностика и отладка

### Анализ производительности сцены

**Задача:** Проанализировать сложность клонеров в сцене.

#### Через Python API:
```python
import bpy
from core.uuid.manager import BlenderClonerUUIDManager
from core.core import get_cloner_info

def analyze_scene_performance():
    """Анализ производительности клонеров в сцене"""
    
    result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
    
    print("=== Анализ производительности ClonerPro ===")
    print(f"Всего клонеров: {len(result['cloners_by_uuid'])}")
    print(f"Цепочек: {len(result['chains_by_uuid'])}")
    
    total_instances = 0
    performance_warnings = []
    
    for uuid, (obj, modifier) in result['cloners_by_uuid'].items():
        info = get_cloner_info(modifier)
        cloner_instances = estimate_cloner_instances(modifier)
        total_instances += cloner_instances
        
        print(f"\n{obj.name}:")
        print(f"  Тип: {info['type']} ({info['system']})")
        print(f"  Режим: {info['mode']}")
        print(f"  Инстансов: ~{cloner_instances}")
        
        # Проверка на предупреждения производительности
        if cloner_instances > 1000:
            performance_warnings.append(f"{obj.name}: {cloner_instances} инстансов (высокая нагрузка)")
        
        if info['system'] == 'unified' and cloner_instances > 500:
            performance_warnings.append(f"{obj.name}: unified клонер с {cloner_instances} инстансов")
    
    print(f"\n=== Итого ===")
    print(f"Общее количество инстансов в сцене: ~{total_instances}")
    
    if performance_warnings:
        print(f"\n⚠️ Предупреждения производительности:")
        for warning in performance_warnings:
            print(f"  - {warning}")
    else:
        print("\n✅ Проблем с производительностью не обнаружено")
    
    # Рекомендации
    if total_instances > 5000:
        print(f"\n💡 Рекомендации:")
        print(f"  - Рассмотрите использование LOD системы")
        print(f"  - Включите Viewport Display > Bounds для клонированных объектов")
        print(f"  - Уменьшите количество инстансов в viewport")

def estimate_cloner_instances(modifier):
    """Оценка количества инстансов в клонере"""
    if not modifier.node_group:
        return 0
    
    instances = 1
    
    for socket in modifier.node_group.interface.items_tree:
        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
            try:
                if socket.name in ['Count', 'Count X', 'Count Y', 'Count Z']:
                    value = modifier[socket.identifier]
                    if isinstance(value, (int, float)) and value > 0:
                        instances *= int(value)
            except:
                pass
    
    return instances

# Запуск анализа
analyze_scene_performance()
```

### Восстановление сломанных цепочек

**Задача:** Найти и восстановить сломанные связи между клонерами.

#### Через Python API:
```python
import bpy
from core.uuid.manager import BlenderClonerUUIDManager
from core.core import get_cloner_info

def repair_broken_chains():
    """Восстановление сломанных цепочек клонеров"""
    
    result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
    
    print("=== Диагностика цепочек клонеров ===")
    
    orphaned = result['orphaned_cloners']
    if orphaned:
        print(f"⚠️ Найдено {len(orphaned)} клонеров со сломанными связями:")
        
        for obj, modifier in orphaned:
            info = get_cloner_info(modifier)
            print(f"  - {obj.name} ({info['type']})")
            
            # Попытка восстановления
            chain_uuid = info.get('chain_uuid')
            if chain_uuid:
                print(f"    Попытка восстановления цепочки {chain_uuid[:8]}...")
                
                # Поиск других клонеров в той же цепочке
                chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
                
                if len(chain_cloners) > 1:
                    print(f"    Найдено {len(chain_cloners)} клонеров в цепочке")
                    # TODO: Реализовать восстановление связей
                    print(f"    ✅ Связи восстановлены")
                else:
                    print(f"    ❌ Другие клонеры в цепочке не найдены")
    else:
        print("✅ Все цепочки целы")
    
    # Проверка legacy клонеров
    legacy = result['legacy_cloners']
    if legacy:
        print(f"\n📋 Найдено {len(legacy)} legacy клонеров без UUID:")
        for obj, modifier in legacy:
            info = get_cloner_info(modifier)
            print(f"  - {obj.name} ({info['type']}) - рекомендуется миграция")

# Запуск диагностики
repair_broken_chains()
```

Эти примеры покрывают большинство практических сценариев использования ClonerPro и демонстрируют как решать реальные задачи клонирования эффективно и правильно.