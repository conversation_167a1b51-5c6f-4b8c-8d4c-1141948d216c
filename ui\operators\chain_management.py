"""
Операторы управления цепочками клонеров
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, BoolProperty
from ...core.chain.validation import validate_and_repair_chains
from ...core.chain.detection import rebuild_chain_metadata_for_scene


class CLONERPRO_OT_validate_chains(Operator):
    """Проверить и восстановить цепочки клонеров"""
    bl_idname = "clonerpro.validate_chains"
    bl_label = "Validate Cloner Chains"
    bl_description = "Check and repair all cloner chains in the scene"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        print("🔍 [CHAIN_OPS] Starting chain validation...")
        
        try:
            # Проверяем и восстанавливаем цепи
            is_valid = validate_and_repair_chains(context)
            
            if is_valid:
                self.report({'INFO'}, "All cloner chains are valid")
            else:
                self.report({'INFO'}, "Cloner chains repaired successfully")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Chain validation failed: {e}")
            print(f"🚨 [CHAIN_OPS] Validation error: {e}")
            import traceback
            traceback.print_exc()
            return {'CANCELLED'}


class CLONERPRO_OT_rebuild_chain_metadata(Operator):
    """Перестроить метаданные всех цепочек"""
    bl_idname = "clonerpro.rebuild_chain_metadata"
    bl_label = "Rebuild Chain Metadata"
    bl_description = "Rebuild metadata for all cloner chains in the scene"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        print("🔧 [CHAIN_OPS] Starting chain metadata rebuild...")
        
        try:
            # Перестраиваем метаданные цепей
            rebuild_chain_metadata_for_scene(context)
            
            self.report({'INFO'}, "Chain metadata rebuilt successfully")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Chain metadata rebuild failed: {e}")
            print(f"🚨 [CHAIN_OPS] Rebuild error: {e}")
            import traceback
            traceback.print_exc()
            return {'CANCELLED'}


class CLONERPRO_OT_show_chain_info(Operator):
    """Показать информацию о цепочке активного клонера"""
    bl_idname = "clonerpro.show_chain_info"
    bl_label = "Show Chain Info"
    bl_description = "Show information about the chain of the active cloner"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not context.active_object:
            self.report({'ERROR'}, "No active object")
            return {'CANCELLED'}
        
        from ...core.core import get_cloner_modifier, get_cloner_info
        
        # Проверяем, является ли активный объект клонером
        modifier = get_cloner_modifier(context.active_object)
        if not modifier:
            self.report({'ERROR'}, "Active object is not a cloner")
            return {'CANCELLED'}
        
        # Получаем информацию о клонере
        cloner_info = get_cloner_info(modifier)
        
        # Информация о цепи
        info_lines = []
        info_lines.append(f"Cloner: {context.active_object.name}")
        info_lines.append(f"Type: {cloner_info.get('type', 'Unknown')}")
        info_lines.append(f"Mode: {cloner_info.get('mode', 'Unknown')}")
        
        if cloner_info.get("is_chained", False):
            info_lines.append("--- CHAIN INFO ---")
            info_lines.append(f"Chain Index: {cloner_info.get('chain_index', 0)}")
            info_lines.append(f"Previous: {cloner_info.get('previous_cloner', 'None')}")
            info_lines.append(f"Next: {cloner_info.get('next_cloners', 'None')}")
            info_lines.append(f"Chain Source: {cloner_info.get('chain_source_object', '') or cloner_info.get('chain_source_collection', 'None')}")
            
            # Показываем всю цепь через UUID систему
            try:
                from ...core.uuid.manager import BlenderClonerUUIDManager
                chain_uuid = cloner_info.get('chain_uuid')
                if chain_uuid:
                    chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
                    info_lines.append(f"Chain Length: {len(chain_cloners)}")
                    info_lines.append("Chain Objects:")
                    for i, (cloner_obj, mod) in enumerate(chain_cloners):
                        marker = " -> " if cloner_obj == context.active_object else "    "
                        info_lines.append(f"{marker}{i}: {cloner_obj.name}")
                else:
                    info_lines.append("Chain Length: 1 (no UUID chain)")
            except:
                info_lines.append("Error analyzing chain")
        else:
            info_lines.append("--- STANDALONE CLONER ---")
            info_lines.append("This cloner is not part of a chain")
        
        # Выводим информацию
        info_text = "\n".join(info_lines)
        print("📊 [CHAIN_INFO]")
        print(info_text)
        
        self.report({'INFO'}, "Chain info printed to console")
        return {'FINISHED'}


class CLONERPRO_OT_break_chain(Operator):
    """Разорвать цепочку в месте активного клонера"""
    bl_idname = "clonerpro.break_chain"
    bl_label = "Break Chain"
    bl_description = "Break the cloner chain at the active cloner"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        if not context.active_object:
            self.report({'ERROR'}, "No active object")
            return {'CANCELLED'}
        
        from ...core.core import get_cloner_modifier, get_cloner_info, set_cloner_metadata
        
        # Проверяем, является ли активный объект клонером
        modifier = get_cloner_modifier(context.active_object)
        if not modifier:
            self.report({'ERROR'}, "Active object is not a cloner")
            return {'CANCELLED'}
        
        cloner_info = get_cloner_info(modifier)
        
        if not cloner_info.get("is_chained", False):
            self.report({'ERROR'}, "Object is not part of a chain")
            return {'CANCELLED'}
        
        try:
            # Разрываем цепь - делаем клонер standalone
            set_cloner_metadata(
                modifier,
                cloner_info.get("type", "UNKNOWN"),
                cloner_info.get("mode", "OBJECT"),
                is_chained=False,
                previous_cloner="",
                next_cloners="",
                chain_index=0
                # Сохраняем chain_source для возможного восстановления
            )
            
            # TODO: Обновить метаданные соседних клонеров
            
            self.report({'INFO'}, f"Broke chain at {context.active_object.name}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to break chain: {e}")
            return {'CANCELLED'}


def register():
    """Регистрация операторов управления цепочками"""
    bpy.utils.register_class(CLONERPRO_OT_validate_chains)
    bpy.utils.register_class(CLONERPRO_OT_rebuild_chain_metadata)
    bpy.utils.register_class(CLONERPRO_OT_show_chain_info)
    bpy.utils.register_class(CLONERPRO_OT_break_chain)


def unregister():
    """Отмена регистрации операторов управления цепочками"""
    bpy.utils.unregister_class(CLONERPRO_OT_break_chain)
    bpy.utils.unregister_class(CLONERPRO_OT_show_chain_info)
    bpy.utils.unregister_class(CLONERPRO_OT_rebuild_chain_metadata)
    bpy.utils.unregister_class(CLONERPRO_OT_validate_chains)