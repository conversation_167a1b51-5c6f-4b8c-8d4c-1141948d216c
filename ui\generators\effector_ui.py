"""
Effector UI Generator для ClonerPro
Прямая генерация UI для эффекторов БЕЗ сложных абстракций
"""

import bpy
from ...config.effectors.random_config import get_random_effector_parameters
from ...config.effectors.noise_config import get_noise_effector_parameters
from ...core.managers.effector_linking import get_linked_cloners_for_effector
from ...ui.utils.ui_state import is_effector_expanded


def draw_effector_ui_simple(context, layout, obj, modifier):
    """
    Простая отрисовка UI эффектора - ПРЯМАЯ РЕАЛИЗАЦИЯ
    Копия логики из advanced_cloners/ui/panels/effector_interface/effector_display.py БЕЗ сложности
    """
    try:
        # Определяем тип эффектора
        effector_type = detect_effector_type_simple(modifier)
        
        if effector_type == "UNKNOWN":
            return
        
        box = layout.box()
        
        # 1. Заголовок эффектора
        draw_effector_header_simple(context, box, obj, modifier, effector_type)
        
        # 2. Проверяем, развернут ли эффектор
        expanded = is_effector_expanded(context, obj.name, modifier.name)
        if not expanded:
            return
        
        # 3. Информация о связях с клонерами
        draw_effector_links_simple(context, box, modifier)
        
        # 4. Кнопка автоматической привязки
        draw_auto_link_button_simple(context, box, obj, modifier)
        
        # 5. Параметры эффектора
        draw_effector_parameters(box, modifier)
        
    except Exception as e:
        print(f"Error drawing effector UI: {e}")


def detect_effector_type_simple(modifier):
    """
    Простое определение типа эффектора
    """
    if not modifier.node_group:
        return "UNKNOWN"
    
    node_group_name = modifier.node_group.name.lower()
    
    if 'random' in node_group_name and 'effector' in node_group_name:
        return 'RANDOM'
    elif 'noise' in node_group_name and 'effector' in node_group_name:
        return 'NOISE'
    elif 'plain' in node_group_name and 'effector' in node_group_name:
        return 'PLAIN'
    
    # Проверяем метаданные
    effector_type = modifier.get("effector_type")
    if effector_type:
        return effector_type
    
    return "UNKNOWN"


def draw_effector_header_simple(context, layout, obj, modifier, effector_type):
    """
    Простой заголовок эффектора
    """
    row = layout.row(align=True)
    
    # Треугольник expand/collapse
    expanded = is_effector_expanded(context, obj.name, modifier.name)
    icon = 'TRIA_DOWN' if expanded else 'TRIA_RIGHT'
    
    toggle_op = row.operator("clonerpro.toggle_effector_expanded", text="", icon=icon, emboss=False)
    toggle_op.object_name = obj.name
    toggle_op.modifier_name = modifier.name
    
    # Иконка и название эффектора
    effector_icons = {
        'RANDOM': 'FORCE_HARMONIC',
        'NOISE': 'FORCE_TURBULENCE', 
        'PLAIN': 'EMPTY_ARROWS'
    }
    
    icon = effector_icons.get(effector_type, 'MODIFIER')
    row.label(text=f"{effector_type.title()} Effector", icon=icon)
    
    # Кнопки управления
    controls = row.row(align=True)
    controls.scale_x = 0.8
    
    # Видимость
    vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
    vis_op = controls.operator("clonerpro.toggle_effector_visibility", text="", icon=vis_icon, emboss=False)
    vis_op.object_name = obj.name
    vis_op.modifier_name = modifier.name
    
    # Удаление
    del_op = controls.operator("clonerpro.delete_effector", text="", icon="X", emboss=False)
    del_op.object_name = obj.name
    del_op.modifier_name = modifier.name


def draw_effector_links_simple(context, layout, modifier):
    """
    Отображение связей с клонерами
    """
    linked_cloners = get_linked_cloners_for_effector(context, modifier)
    
    if linked_cloners:
        link_box = layout.box()
        link_row = link_box.row()
        link_row.label(text="Linked to cloners:", icon='LINKED')
        
        for linked_obj, linked_mod in linked_cloners:
            row = link_box.row()
            row.label(text=f"{linked_obj.name} - {linked_mod.name}")


def draw_auto_link_button_simple(context, layout, obj, modifier):
    """
    Кнопка автоматической привязки к клонерам
    """
    # Проверяем наличие клонеров на объекте
    has_cloners = False
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group and mod != modifier:
            cloner_patterns = ["GridCloner", "Cloner"]
            node_group_name = mod.node_group.name
            
            for pattern in cloner_patterns:
                if pattern in node_group_name:
                    has_cloners = True
                    break
            
            if has_cloners:
                break
    
    if has_cloners:
        auto_link_box = layout.box()
        auto_link_row = auto_link_box.row()
        op = auto_link_row.operator("clonerpro.auto_link_effector", text="Auto-link to Cloners", icon='LINKED')
        op.object_name = obj.name
        op.modifier_name = modifier.name


# Removed - using parameter_display_simple.py instead


# Removed - using ui_state.py functions instead


def draw_effector_parameters(layout, modifier):
    """Отображает параметры эффектора в группированном виде"""
    from ...config.effectors.random_config import get_random_effector_parameters
    from ...config.effectors.noise_config import get_noise_effector_parameters
    from ..utils.ui_helpers import display_socket_prop
    
    # Определяем тип эффектора
    effector_type = detect_effector_type_from_modifier(modifier)
    
    if effector_type == "RANDOM":
        parameters = get_random_effector_parameters()
    elif effector_type == "NOISE":
        parameters = get_noise_effector_parameters()
    else:
        # Неизвестный тип эффектора
        layout.label(text=f"Unknown effector type: {effector_type}", icon='ERROR')
        return
    
    # Отображаем группы параметров
    for group_name, group_params in parameters.items():
        if group_params:
            # Создаем бокс для группы
            box = layout.box()
            col = box.column(align=True)
            
            # Заголовок группы с иконкой
            icon = get_group_icon(group_name)
            col.label(text=f"{group_name}:", icon=icon)
            
            # Добавляем параметры
            col.separator()
            for param in group_params:
                socket_name = param["socket_name"]
                display_name = param["name"]
                display_socket_prop(col, modifier, socket_name, text=display_name)


def detect_effector_type_from_modifier(modifier):
    """Определяет тип эффектора по модификатору"""
    if not modifier.node_group:
        return "UNKNOWN"
    
    node_group_name = modifier.node_group.name.lower()
    
    if "random" in node_group_name:
        return "RANDOM"
    elif "noise" in node_group_name:
        return "NOISE"
    
    # Проверяем метаданные
    effector_type = modifier.get("effector_type")
    if effector_type:
        return effector_type.upper()
    
    return "UNKNOWN"


def get_group_icon(group_name):
    """Получить иконку для группы параметров"""
    icon_map = {
        "Effector Settings": "SETTINGS",
        "Transform Parameters": "OBJECT_DATA",
        "Noise Parameters": "FORCE_TURBULENCE", 
        "Random Parameters": "FORCE_TURBULENCE",
        "Animation": "TIME",
        "Falloff": "IPO_EASE_IN_OUT"
    }
    return icon_map.get(group_name, "SETTINGS")