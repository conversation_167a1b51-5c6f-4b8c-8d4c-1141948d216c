"""
UI Utils для ClonerPro - ТОЧНЫЕ паттерны из advanced_cloners
"""

import bpy


def find_socket_by_name(modifier, socket_name):
    """Находит сокет в интерфейсе модификатора по имени - ТОЧНАЯ КОПИЯ"""
    if not modifier or not modifier.node_group:
        return None

    # Полное соответствие по имени - всегда ищем сначала точное соответствие
    for socket in modifier.node_group.interface.items_tree:
        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == socket_name:
            return socket.identifier

    return None


def display_socket_prop(layout, modifier, socket_name, text=None, **kwargs):
    """Безопасно отображает свойство сокета модификатора - ТОЧНАЯ КОПИЯ"""
    socket_id = find_socket_by_name(modifier, socket_name)
    if socket_id:
        if text is None:
            text = socket_name

        # Специальная обработка для Collection сокетов (ObjectCloner)
        if socket_name == "Source Collection":
            # Используем prop_search для коллекций
            layout.prop_search(modifier, f'["{socket_id}"]', bpy.data, "collections", text=text, **kwargs)
        else:
            layout.prop(modifier, f'["{socket_id}"]', text=text, **kwargs)
        return True
    return False


# Функции для состояний раскрытия (expanded states) - ТОЧНАЯ КОПИЯ
def get_expanded_states_key(obj_name, modifier_name):
    """Создает ключ для хранения состояния раскрытия модификатора"""
    # Ограничиваем длину ключа до 60 символов (63 - максимум для IDProperty)
    if len(obj_name) + len(modifier_name) + 1 > 60:
        obj_hash = str(hash(obj_name) % 10000).zfill(4)
        mod_hash = str(hash(modifier_name) % 10000).zfill(4)
        return f"{obj_name[:25]}_{obj_hash}_{mod_hash}"
    else:
        return f"{obj_name}_{modifier_name}"


def is_element_expanded(context, obj_name, modifier_name, state_property="cloner_expanded_states"):
    """Проверяет, развернут ли элемент в UI - ТОЧНАЯ КОПИЯ"""
    key = get_expanded_states_key(obj_name, modifier_name)
    expanded_states = context.scene.get(state_property, {})
    return expanded_states.get(key, False)


def set_element_expanded(context, obj_name, modifier_name, state, state_property="cloner_expanded_states"):
    """Устанавливает состояние раскрытия элемента в UI - ТОЧНАЯ КОПИЯ"""
    key = get_expanded_states_key(obj_name, modifier_name)
    expanded_states = context.scene.get(state_property, {})
    expanded_states[key] = state
    context.scene[state_property] = expanded_states

    # Принудительное обновление UI
    for area in context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()


# Регистрация свойств для хранения состояний раскрытия
def register_expanded_states_property(property_name="cloner_expanded_states"):
    """Регистрирует свойство для хранения состояний раскрытия"""
    if not hasattr(bpy.types.Scene, property_name):
        setattr(bpy.types.Scene, property_name, {})


def unregister_expanded_states_property(property_name="cloner_expanded_states"):
    """Удаляет свойство для хранения состояний раскрытия"""
    if hasattr(bpy.types.Scene, property_name):
        delattr(bpy.types.Scene, property_name)


# ================================
# ObjectCloner Helper Functions
# ================================

def get_socket_value(modifier, socket_name):
    """Получает значение сокета модификатора с fallback логикой"""
    # Сначала пробуем новую socket mapping систему
    socket_id = find_socket_by_name(modifier, socket_name)
    if socket_id and socket_id in modifier:
        return modifier[socket_id]
    
    # Fallback для ObjectCloner: проверяем старые Input_X ключи
    if "ObjectCloner" in (modifier.node_group.name if modifier.node_group else ""):
        # Маппинг для основных параметров ObjectCloner из legacy системы
        legacy_mapping = {
            "Source Mode": "Input_2",
            "Source Object": "Input_3", 
            "Source Collection": "Input_4",
            "Distribution Mode": "Input_5",
            "Instance Count": "Input_6",
            "Density": "Input_7",
            "Hide Original": "Input_8",
            "Instance Scale": "Input_9",
            "Instance Rotation": "Input_10",
            "Random Position": "Input_11",
            "Random Rotation": "Input_12",
            "Random Scale": "Input_13",
            "Uniform Scale": "Input_14",
            "Random Seed": "Input_15",
            "Offset": "Input_16",
            "Face Center Mode": "Input_17",
            "Align to Normal": "Input_18",
            "Collection Pick Instance": "Input_19",
            "Collection Instance Index": "Input_20",
            "Collection Object Count": "Input_21",
            "Collection Random Seed": "Input_22"
        }
        
        legacy_key = legacy_mapping.get(socket_name)
        if legacy_key and legacy_key in modifier:
            return modifier[legacy_key]
    
    # Fallback для Effectors: тоже могут использовать Input_X систему
    node_group_name = modifier.node_group.name if modifier.node_group else ""
    if "Effector" in node_group_name:
        # Для эффекторов пробуем стандартные Input_X ключи
        for i in range(1, 20):  # Проверяем Input_1 до Input_19
            input_key = f"Input_{i}"
            if input_key in modifier:
                # Это примитивный fallback, но может помочь
                pass
    
    return None


def set_socket_value(modifier, socket_name, value):
    """Устанавливает значение сокета модификатора с fallback логикой"""
    # Сначала пробуем новую socket mapping систему
    socket_id = find_socket_by_name(modifier, socket_name)
    if socket_id:
        modifier[socket_id] = value
        return True
    
    # Fallback для ObjectCloner: используем старые Input_X ключи
    if "ObjectCloner" in (modifier.node_group.name if modifier.node_group else ""):
        # Маппинг для основных параметров ObjectCloner из legacy системы
        legacy_mapping = {
            "Source Mode": "Input_2",
            "Source Object": "Input_3", 
            "Source Collection": "Input_4",
            "Distribution Mode": "Input_5",
            "Instance Count": "Input_6",
            "Density": "Input_7",
            "Hide Original": "Input_8",
            "Instance Scale": "Input_9",
            "Instance Rotation": "Input_10",
            "Random Position": "Input_11",
            "Random Rotation": "Input_12",
            "Random Scale": "Input_13",
            "Uniform Scale": "Input_14",
            "Random Seed": "Input_15",
            "Offset": "Input_16",
            "Face Center Mode": "Input_17",
            "Align to Normal": "Input_18",
            "Collection Pick Instance": "Input_19",
            "Collection Instance Index": "Input_20",
            "Collection Object Count": "Input_21",
            "Collection Random Seed": "Input_22"
        }
        
        legacy_key = legacy_mapping.get(socket_name)
        if legacy_key:
            modifier[legacy_key] = value
            return True
    
    return False


def force_ui_update(context):
    """Принудительно обновляет UI в Blender 4.4+"""
    try:
        # Обновляем view layer
        context.view_layer.update()
        
        # Обновляем все 3D viewport области
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
    except:
        # Fallback для случаев когда context недоступен
        pass


def force_geometry_nodes_update(context, modifier):
    """
    ТОЧНАЯ КОПИЯ из ObjectCloner_Standalone - принудительно обновляет Geometry Nodes модификатор
    Специально для критических параметров как Distribution Mode
    """
    try:
        # 1. Обновляем объект
        if context.object:
            context.object.update_tag()
        
        # 2. Принудительно обновляем модификатор
        if modifier and modifier.node_group:
            # Временно отключаем и включаем модификатор для принудительного обновления
            show_viewport = modifier.show_viewport
            modifier.show_viewport = False
            context.view_layer.update()
            modifier.show_viewport = show_viewport
        
        # 3. Обновляем view layer
        context.view_layer.update()
        
        # 4. Обновляем все области
        for area in context.screen.areas:
            area.tag_redraw()
            
        # 5. Дополнительное обновление сцены
        bpy.context.scene.frame_set(bpy.context.scene.frame_current)
        
    except Exception as e:
        print(f"[ERROR] Ошибка при обновлении Geometry Nodes: {e}")
        # Fallback к обычному обновлению
        force_ui_update(context)


def print_operator_action(action, **kwargs):
    """Выводит информацию о действии оператора"""
    print(f"[OPERATOR] {action}: {kwargs}")


# ================================
# ObjectCloner Specific Helpers
# ================================

def get_objectcloner_modifier(obj):
    """Находит ObjectCloner модификатор на объекте"""
    if not obj:
        return None
    
    for modifier in obj.modifiers:
        if (modifier.type == 'NODES' and 
            modifier.node_group and 
            'object' in modifier.node_group.name.lower() and
            'cloner' in modifier.node_group.name.lower()):
            return modifier
    
    return None


def is_object_mode(modifier):
    """Проверяет, находится ли ObjectCloner в Object режиме"""
    source_mode = get_socket_value(modifier, "Source Mode")
    return source_mode == 0 if source_mode is not None else True


def is_collection_mode(modifier):
    """Проверяет, находится ли ObjectCloner в Collection режиме"""
    source_mode = get_socket_value(modifier, "Source Mode")
    return source_mode == 1 if source_mode is not None else False


def should_show_face_center_mode(modifier):
    """Определяет, показывать ли параметр Face Center Mode"""
    mode = get_socket_value(modifier, "Distribution Mode")
    return mode == 2  # Faces mode


def should_show_collection_params(modifier):
    """Определяет, показывать ли параметры коллекции"""
    return is_collection_mode(modifier)


def get_distribution_mode_info(modifier):
    """Получает информацию о текущем режиме распределения"""
    mode = get_socket_value(modifier, "Distribution Mode")
    if mode is None:
        mode = 0  # Default to Vertices
    
    if mode == 0:  # Vertices
        return {
            'name': 'Vertices',
            'count_param': None,
            'density_param': None,
            'description': 'Uses mesh vertices',
            'user_controllable': False
        }
    elif mode == 1:  # Edges  
        return {
            'name': 'Edges',
            'count_param': 'Instance Count',
            'density_param': None,
            'description': 'Points along edges',
            'user_controllable': True
        }
    elif mode == 2:  # Faces
        return {
            'name': 'Faces', 
            'count_param': None,
            'density_param': 'Density',
            'description': 'Random points on faces',
            'user_controllable': True
        }
    
    return {
        'name': 'Vertices',
        'count_param': None,
        'density_param': None,
        'description': 'Unknown mode',
        'user_controllable': False
    }