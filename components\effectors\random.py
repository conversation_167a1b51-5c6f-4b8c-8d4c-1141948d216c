"""
Random Effector - Simplified implementation for ClonerPro
Adapted from advanced_cloners logic_builder.py without complex architecture
"""

import bpy


def create_random_effector(name_suffix=""):
    """
    Create Random Effector node group.
    
    This function creates the complete Random Effector with all parameters
    and logic for applying random transformations to instances.
    
    Args:
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # !>7405< =>2CN 3@C??C C7;>2
    effector_group = bpy.data.node_groups.new(
        type='GeometryNodeTree', 
        name=f"RandomEffector{name_suffix}"
    )

    # --- 0AB@>9:0 8=B5@D59A0 ---
    # KE>4K
    effector_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # E>4K
    effector_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    
    # 07>2K5 ?0@0<5B@K MDD5:B>@0
    enable_input = effector_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
    enable_input.default_value = True
    
    strength_input = effector_group.interface.new_socket(name="Strength", in_out='INPUT', socket_type='NodeSocketFloat')
    strength_input.default_value = 1.0
    strength_input.min_value = 0.0
    strength_input.max_value = 2.0

    # 0@0<5B@K B@0=AD>@<0F88
    position_input = effector_group.interface.new_socket(name="Position", in_out='INPUT', socket_type='NodeSocketVector')
    position_input.default_value = (0.0, 0.0, 0.0)
    
    rotation_input = effector_group.interface.new_socket(name="Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_input.default_value = (0.0, 0.0, 0.0)
    
    scale_input = effector_group.interface.new_socket(name="Scale", in_out='INPUT', socket_type='NodeSocketVector')
    scale_input.default_value = (0.0, 0.0, 0.0)

    # !?5F8D8G=K5 4;O RandomEffector
    uniform_scale_input = effector_group.interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')
    uniform_scale_input.default_value = True

    seed_input = effector_group.interface.new_socket(name="Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0

    # --- !>740=85 C7;>2 ---
    nodes = effector_group.nodes
    links = effector_group.links

    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)
    
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)

    # Basic switch for enabling/disabling the effector
    switch = nodes.new('GeometryNodeSwitch')
    switch.input_type = 'GEOMETRY'
    switch.location = (600, 0)
    links.new(group_input.outputs['Enable'], switch.inputs[0])  # Switch
    links.new(group_input.outputs['Geometry'], switch.inputs[2])  # False (bypass)

    # Get index for random per-instance values
    index = nodes.new('GeometryNodeInputIndex')
    index.location = (-600, -200)

    # Random position
    random_position = nodes.new('FunctionNodeRandomValue')
    random_position.data_type = 'FLOAT_VECTOR'
    random_position.location = (-400, 200)

    # Link seed and ID
    links.new(group_input.outputs['Seed'], random_position.inputs['Seed'])
    links.new(index.outputs['Index'], random_position.inputs['ID'])

    # Set random position range (-Position to +Position)
    vector_math_neg = nodes.new('ShaderNodeVectorMath')
    vector_math_neg.operation = 'MULTIPLY'
    vector_math_neg.inputs[1].default_value = (-1.0, -1.0, -1.0)
    vector_math_neg.location = (-600, 300)
    links.new(group_input.outputs['Position'], vector_math_neg.inputs[0])

    links.new(vector_math_neg.outputs['Vector'], random_position.inputs['Min'])
    links.new(group_input.outputs['Position'], random_position.inputs['Max'])

    # Random rotation
    random_rotation = nodes.new('FunctionNodeRandomValue')
    random_rotation.data_type = 'FLOAT_VECTOR'
    random_rotation.location = (-400, 0)

    # Link seed and ID
    links.new(group_input.outputs['Seed'], random_rotation.inputs['Seed'])
    links.new(index.outputs['Index'], random_rotation.inputs['ID'])

    # Set rotation range (-Rotation to +Rotation)
    vector_math_neg_rot = nodes.new('ShaderNodeVectorMath')
    vector_math_neg_rot.operation = 'MULTIPLY'
    vector_math_neg_rot.inputs[1].default_value = (-1.0, -1.0, -1.0)
    vector_math_neg_rot.location = (-600, 100)
    links.new(group_input.outputs['Rotation'], vector_math_neg_rot.inputs[0])

    links.new(vector_math_neg_rot.outputs['Vector'], random_rotation.inputs['Min'])
    links.new(group_input.outputs['Rotation'], random_rotation.inputs['Max'])

    # Random scale
    random_scale = nodes.new('FunctionNodeRandomValue')
    random_scale.data_type = 'FLOAT_VECTOR'
    random_scale.location = (-400, -200)

    # For uniform scale
    random_uniform_scale = nodes.new('FunctionNodeRandomValue')
    random_uniform_scale.data_type = 'FLOAT'
    random_uniform_scale.location = (-400, -400)

    # Link seed and ID
    links.new(group_input.outputs['Seed'], random_scale.inputs['Seed'])
    links.new(index.outputs['Index'], random_scale.inputs['ID'])
    links.new(group_input.outputs['Seed'], random_uniform_scale.inputs['Seed'])
    links.new(index.outputs['Index'], random_uniform_scale.inputs['ID'])

    # Set scale range (1-Scale to 1+Scale)
    one_minus_scale = nodes.new('ShaderNodeVectorMath')
    one_minus_scale.operation = 'SUBTRACT'
    one_minus_scale.inputs[0].default_value = (1.0, 1.0, 1.0)
    one_minus_scale.location = (-600, -100)
    links.new(group_input.outputs['Scale'], one_minus_scale.inputs[1])

    one_plus_scale = nodes.new('ShaderNodeVectorMath')
    one_plus_scale.operation = 'ADD'
    one_plus_scale.inputs[0].default_value = (1.0, 1.0, 1.0)
    one_plus_scale.location = (-600, -200)
    links.new(group_input.outputs['Scale'], one_plus_scale.inputs[1])

    links.new(one_minus_scale.outputs['Vector'], random_scale.inputs['Min'])
    links.new(one_plus_scale.outputs['Vector'], random_scale.inputs['Max'])

    # For uniform scale (single float)
    scale_max = nodes.new('ShaderNodeMath')
    scale_max.operation = 'MAXIMUM'
    scale_max.location = (-600, -300)
    links.new(group_input.outputs['Scale'], scale_max.inputs[0])  # X
    
    scale_max_temp = nodes.new('ShaderNodeMath')
    scale_max_temp.operation = 'MAXIMUM'
    scale_max_temp.location = (-700, -350)
    links.new(group_input.outputs['Scale'], scale_max_temp.inputs[0])  # Y
    links.new(group_input.outputs['Scale'], scale_max_temp.inputs[1])  # Z
    links.new(scale_max_temp.outputs[0], scale_max.inputs[1])

    one_minus_uniform = nodes.new('ShaderNodeMath')
    one_minus_uniform.operation = 'SUBTRACT'
    one_minus_uniform.inputs[0].default_value = 1.0
    one_minus_uniform.location = (-600, -400)
    links.new(scale_max.outputs[0], one_minus_uniform.inputs[1])

    one_plus_uniform = nodes.new('ShaderNodeMath')
    one_plus_uniform.operation = 'ADD'
    one_plus_uniform.inputs[0].default_value = 1.0
    one_plus_uniform.location = (-600, -500)
    links.new(scale_max.outputs[0], one_plus_uniform.inputs[1])

    links.new(one_minus_uniform.outputs[0], random_uniform_scale.inputs['Min'])
    links.new(one_plus_uniform.outputs[0], random_uniform_scale.inputs['Max'])

    # Switch between uniform and non-uniform scale
    scale_switch = nodes.new('GeometryNodeSwitch')
    scale_switch.input_type = 'VECTOR'
    scale_switch.location = (-200, -300)
    links.new(group_input.outputs['Uniform Scale'], scale_switch.inputs[0])  # Switch
    links.new(random_scale.outputs['Value'], scale_switch.inputs['False'])  # False (vector scale)

    # Create uniform scale vector
    uniform_vector = nodes.new('ShaderNodeCombineXYZ')
    uniform_vector.location = (-300, -400)
    links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['X'])
    links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['Y'])
    links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['Z'])

    links.new(uniform_vector.outputs['Vector'], scale_switch.inputs['True'])  # True (uniform scale)

    # Apply global strength multiplier to position and rotation
    strength_mul_pos = nodes.new('ShaderNodeVectorMath')
    strength_mul_pos.operation = 'MULTIPLY'
    strength_mul_pos.location = (-200, 200)
    links.new(random_position.outputs['Value'], strength_mul_pos.inputs[0])
    links.new(group_input.outputs['Strength'], strength_mul_pos.inputs[1])  # Strength

    strength_mul_rot = nodes.new('ShaderNodeVectorMath')
    strength_mul_rot.operation = 'MULTIPLY'
    strength_mul_rot.location = (-200, 0)
    links.new(random_rotation.outputs['Value'], strength_mul_rot.inputs[0])
    links.new(group_input.outputs['Strength'], strength_mul_rot.inputs[1])  # Strength

    # Apply transformations to instances
    # Start with the input geometry
    translate_instances = nodes.new('GeometryNodeTranslateInstances')
    translate_instances.location = (0, 200)
    links.new(group_input.outputs['Geometry'], translate_instances.inputs['Instances'])
    links.new(strength_mul_pos.outputs['Vector'], translate_instances.inputs['Translation'])

    # Rotate instances
    rotate_instances = nodes.new('GeometryNodeRotateInstances')
    rotate_instances.location = (200, 100)
    links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
    links.new(strength_mul_rot.outputs['Vector'], rotate_instances.inputs['Rotation'])

    # Scale instances
    scale_instances = nodes.new('GeometryNodeScaleInstances')
    scale_instances.location = (400, 0)
    links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
    links.new(scale_switch.outputs['Output'], scale_instances.inputs['Scale'])

    # Connect the transformed geometry to the switch (if enabled)
    links.new(scale_instances.outputs['Instances'], switch.inputs['True'])  # True (with effect)

    # Output
    links.new(switch.outputs['Output'], group_output.inputs['Geometry'])

    return effector_group


# Compatibility function for easy integration
def create_random_effector_logic_group(name_suffix=""):
    """Create Random Effector logic group - compatibility function"""
    return create_random_effector(name_suffix)