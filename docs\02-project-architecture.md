# Архитектура проекта ClonerPro

## Обзор архитектуры

ClonerPro реализует **единую классовую архитектуру** с автоматическим разделением на типы клонеров и интеллентной маршрутизацией. Архитектура построена на базе BaseCloner класса для максимальной унификации, гибкости и безопасности.

## Принципы архитектуры

### 1. Единая классовая база
```python
# Базовый класс для всех клонеров
class BaseCloner(BaseComponent):
    """Базовый класс для всех клонеров ClonerPro"""
    
    def create_node_group(self, mode="OBJECT"):
        """Единый метод создания node group с анти-рекурсией"""
        
    def _create_cloner_logic(self, base_nodes, mode):
        """Переопределяется в дочерних классах"""
        raise NotImplementedError
```

### 2. Автоматическая маршрутизация
```python
# Разделение по типам клонеров в core/core.py
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE"}
MESH_CLONERS = {"OBJECT", "SPLINE"}

def get_cloner_system(cloner_type: str) -> str:
    if cloner_type in UNIFIED_CLONERS:
        return "unified"
    elif cloner_type in MESH_CLONERS:
        return "mesh"
    else:
        return "unknown"
```

### 3. Единая точка входа
- **cloner_creation.py** - объединённая система для всех клонеров
- **BaseCloner** - базовый класс с общей функциональностью
- **Автоматическая маршрутизация** между unified и mesh системами

### 4. Слоистая архитектура
```
UI Layer          → Панели, операторы, генераторы UI
API Layer         → cloner_creation.py (единый API)
Class Layer       → BaseCloner + дочерние классы клонеров
Component Layer   → Конкретные реализации (Grid, Linear, Object, Spline)
Core Layer        → Базовые утилиты, UUID система, безопасность
```

## Детальная архитектура

### Единая система создания клонеров

#### Архитектурный поток
```
create_cloner_unified() 
    ↓
Автоматическое определение системы (unified/mesh)
    ↓
Маршрутизация к соответствующей подсистеме
    ↓
BaseCloner.create_node_group() с анти-рекурсией
    ↓
Финальный клонер с полной функциональностью
```

#### Компоненты единой системы

**1. cloner_creation.py (Объединённая система)**
```python
def create_cloner_unified(cloner_type, mode, source, use_anti_recursion=True, mesh_stacked=False):
    """Единая точка создания для ВСЕХ клонеров"""
    
    # Автоматическое определение системы
    if cloner_type in ["OBJECT", "SPLINE"]:
        return _create_mesh_cloner_via_mesh_system(
            cloner_instance, mode, source, use_anti_recursion, name_suffix, mesh_stacked
        )
    elif cloner_type in ["GRID", "LINEAR", "CIRCLE"]:
        return _create_unified_cloner_via_unified_system(
            cloner_instance, mode, source, use_anti_recursion, name_suffix
        )
```

**2. BaseCloner (Единая базовая архитектура)**
```python
class BaseCloner(BaseComponent):
    def create_node_group(self, mode="OBJECT"):
        """Базовый метод создания node group с анти-рекурсией для всех клонеров"""
        
        # Создаем интерфейс (базовые + специфичные сокеты)
        specific_sockets = self.get_specific_sockets()
        socket_mapping = self.create_cloner_interface(node_group, specific_sockets)
        
        # Создаем базовые ноды
        base_nodes = self.create_base_nodes(node_group, mode)
        
        # Реализуем логику клонера (переопределяется в дочерних классах)
        final_geometry = self._create_cloner_logic(base_nodes, mode)
        
        # Применяем анти-рекурсию - ВСЕГДА (Switch управляется сокетом)
        anti_recursion_result = self.apply_anti_recursion(base_nodes, final_geometry)
        
        return node_group, socket_mapping
```

**3. Конкретные реализации клонеров**
```python
class GridCloner(BaseCloner):
    """Grid клонер с 2D/3D сеточным распределением"""
    
    def _create_cloner_logic(self, base_nodes, mode):
        """Специфичная логика Grid клонера"""
        # Создание сетки точек
        grid_points = self._create_grid_points(base_nodes)
        # Центрирование
        centered_points = self._apply_grid_centering(base_nodes, grid_points)
        # Инстансирование
        return self.apply_instance_transforms(base_nodes, instance_output)

class ObjectCloner(BaseCloner):
    """Object клонер для распределения по поверхностям"""
    
    def _create_cloner_logic(self, base_nodes, mode):
        """Специфичная логика Object клонера"""
        # Распределение по вертексам/рёбрам/фейсам
        distribution = self._create_object_distribution(base_nodes)
        # Применение трансформаций
        return self.apply_transforms_and_randomization(base_nodes, distribution)
```

#### Преимущества единой архитектуры
- **Консистентность** - Все клонеры используют одинаковый BaseCloner API
- **Безопасность** - Встроенная anti-recursion защита для всех клонеров
- **Гибкость** - Поддержка всех режимов через единый интерфейс
- **Расширяемость** - Простое добавление новых клонеров через наследование
- **Унификация** - Одинаковые паттерны для unified и mesh клонеров

### Подсистемы mesh и unified

#### Unified клонеры (Grid, Linear, Circle)
```
create_cloner_unified("GRID", "OBJECT", source)
    ↓
_create_unified_cloner_via_unified_system()
    ↓
GridCloner.create_node_group(mode="OBJECT")
    ↓
GridCloner._create_cloner_logic() + BaseCloner.apply_anti_recursion()
    ↓
Объект или модификатор с полной функциональностью
```

#### Mesh клонеры (Object, Spline)  
```
create_cloner_unified("OBJECT", "STACKED", source)
    ↓
_create_mesh_cloner_via_mesh_system()
    ↓
ObjectCloner.create_node_group(mode="STACKED", target_obj=source)
    ↓
ObjectCloner._create_cloner_logic() + BaseCloner.apply_anti_recursion()
    ↓
Модификатор с mesh-based функциональностью
```

#### Преимущества mesh архитектуры
- **Производительность** - Оптимизация для geometry-based операций
- **Специализация** - Прямая работа с mesh данными  
- **Гибкость** - Поддержка сложных геометрических алгоритмов
- **Эффективность** - Минимальные промежуточные преобразования

## UUID система и Chain Management

### Архитектура UUID системы

#### UUID Manager
```python
class BlenderClonerUUIDManager:
    """Централизованное управление UUID в Blender"""
    
    @staticmethod
    def set_cloner_uuid_metadata(modifier, cloner_type: str, mode: str, **kwargs):
        """Установка полных UUID метаданных"""
        
    @staticmethod  
    def find_cloner_by_uuid(cloner_uuid: str):
        """Поиск клонера по UUID"""
        
    @staticmethod
    def find_cloners_in_chain_by_uuid(chain_uuid: str):
        """Поиск всех клонеров в цепочке"""
```

#### Метаданные UUID
```python
# Полный набор UUID метаданных в custom properties
{
    # Основные UUID
    "cloner_uuid": "уникальный ID клонера",
    "chain_uuid": "ID цепочки клонеров", 
    "chain_source_uuid": "ID источника цепочки",
    
    # Связи в цепочке
    "previous_cloner_uuid": "UUID предыдущего клонера",
    "next_cloner_uuids": "UUID1,UUID2,UUID3",
    
    # Blender-specific данные
    "blender_session_uid": 123456,
    "blender_persistent_uid": "blender_persistent_id",
    "object_fingerprint": "hash_based_fingerprint",
    
    # Пользовательские данные
    "display_name": "Grid Cloner",
    "user_notes": "Пользовательские заметки",
    "creation_timestamp": 1702023456.789
}
```

### Chain Detection архитектура

#### Автоматическое обнаружение
```python
def setup_cloner_chain_links_only(context, new_cloner, source_cloner, source_collection):
    """Автоматическое связывание клонеров в цепочки"""
    
    # 1. Определение источника цепочки
    source_uuid = _get_or_create_source_uuid(source_object_or_collection)
    
    # 2. Поиск существующих клонеров с тем же источником
    related_cloners = _find_cloners_by_source_uuid(source_uuid)
    
    # 3. Установка связей в цепочке
    if related_cloners:
        _link_to_existing_chain(new_cloner, related_cloners)
    else:
        _create_new_chain(new_cloner, source_uuid)
```

#### Smart Recovery
```python
def restore_cloner_chains_on_file_load():
    """Восстановление цепочек после загрузки файла"""
    
    # Сканирование всех клонеров
    all_cloners = scan_all_cloners_with_uuid()
    
    # Группировка по chain_uuid
    chains = group_cloners_by_chain_uuid(all_cloners)
    
    # Восстановление связей
    for chain_uuid, cloners in chains.items():
        restore_chain_links(cloners)
```

## Системы безопасности

### Anti-Recursion система

#### Архитектура защиты
```python
def apply_anti_recursion_architecture(logic_group, name_suffix=""):
    """Применение anti-recursion к node group"""
    
    # Создание защищённого wrapper
    safe_wrapper = create_anti_recursion_wrapper(logic_group, name_suffix)
    
    # Встраивание оригинальной логики
    embed_logic_in_safe_wrapper(safe_wrapper, logic_group)
    
    # Настройка защитных механизмов
    setup_recursion_detection(safe_wrapper)
    
    return safe_wrapper
```

### Dependency Safety

#### Система безопасных зависимостей
```python
def register_dependency_safety_handlers():
    """Регистрация обработчиков безопасности"""
    
    # Мониторинг событий Blender
    bpy.app.handlers.depsgraph_update_post.append(dependency_update_handler)
    bpy.app.handlers.undo_post.append(undo_safety_handler)
    bpy.app.handlers.redo_post.append(redo_safety_handler)
    
    # Защита от критических операций
    register_object_deletion_protection()
    register_modifier_change_protection()
```

### Event Handling система

#### Мониторинг событий
```python
def setup_event_monitoring():
    """Настройка мониторинга событий Blender"""
    
    # File load events
    bpy.app.handlers.load_post.append(on_file_loaded)
    
    # Object modification events
    bpy.app.handlers.depsgraph_update_post.append(on_depsgraph_update)
    
    # Scene update events  
    bpy.app.handlers.scene_update_post.append(on_scene_update)
```

## Модульная структура

### Core модули
```
core/
├── core.py                 # Центральные константы и утилиты
├── templates/              # Системы создания клонеров
│   ├── unified_creation.py
│   ├── mesh_creation.py
│   ├── cloner_templates.py
│   └── anti_recursion.py
├── uuid/                   # UUID система
│   ├── manager.py
│   └── chain_management.py
├── chain/                  # Chain management
│   ├── detection.py
│   ├── restoration.py
│   └── metadata.py
├── managers/               # Менеджеры ресурсов
│   ├── cloner_modes.py
│   ├── object_creation.py
│   └── effector_creation.py
└── system/                 # Системы безопасности
    ├── dependency_safety.py
    ├── event_handling.py
    └── error_handling.py
```

### Component модули
```
components/
├── cloners/                # Реализации клонеров
│   ├── grid.py            # Grid клонер (unified)
│   ├── linear.py          # Linear клонер (unified)
│   ├── circle.py          # Circle клонер (unified)
│   ├── object.py          # Object клонер (mesh)
│   └── spline.py          # Spline клонер (mesh)
├── effectors/              # Эффекторы
│   ├── random.py
│   └── noise.py
└── fields/                 # Поля (планируется)
    └── sphere.py
```

### UI модули
```
ui/
├── panels/                 # Панели интерфейса
│   ├── cloners.py
│   ├── effectors.py
│   └── fields.py
├── operators/              # Операторы
│   ├── creation.py
│   ├── management.py
│   └── browser.py
└── generators/             # Автогенерация UI
    ├── cloner_ui.py
    └── effector_ui.py
```

## Потоки данных

### Создание unified клонера
```
User Input → UI Operator → create_cloner_unified() → logic_function → 
wrapper creation → anti-recursion → metadata setup → UUID assignment → 
chain detection → final cloner object
```

### Создание mesh клонера  
```
User Input → UI Operator → create_mesh_cloner() → logic_function →
direct modifier creation → parameter setup → metadata setup → 
UUID assignment → chain detection → final modifier
```

### Chain управление
```
New Cloner → source analysis → UUID lookup → existing chain detection →
link establishment → metadata update → relationship tracking
```

## Производительность и оптимизации

### Ленивая загрузка
- Компоненты загружаются только при необходимости
- Node groups создаются по требованию
- UI генерируется динамически

### Кэширование
```python
# Временно отключено из-за ReferenceError проблем
# TODO: Реимплементировать с fake_user подходом
NODE_GROUP_CACHE = {}  # Будет восстановлено в будущих версиях
```

### Batch операции
- Групповое создание клонеров
- Массовое обновление параметров
- Оптимизированное восстановление цепочек

## Расширяемость

### Добавление новых unified клонеров
1. Создать класс наследник BaseCloner в `components/cloners/`
2. Переопределить `_create_cloner_logic()` и `get_specific_sockets()`
3. Добавить тип в `UNIFIED_CLONERS` в `core/core.py`
4. Обновить UI с новым типом клонера

### Добавление новых mesh клонеров
1. Создать класс наследник BaseCloner в `components/cloners/`
2. Переопределить `_create_cloner_logic()` для mesh операций
3. Добавить тип в `MESH_CLONERS` в `core/core.py`
4. Обновить UI панели для нового типа

### Добавление новых эффекторов
1. Создать файл в `components/effectors/`
2. Добавить конфигурацию в `config/effectors/`
3. Зарегистрировать в `EFFECTOR_REGISTRY`
4. Добавить в UI генераторы

## Совместимость и миграция

### Legacy поддержка
- Автоматическая миграция старых клонеров на UUID систему
- Обратная совместимость с metadata форматами
- Graceful fallback для отсутствующих UUID

### Версионирование
```python
ADDON_VERSION = "1.0.0"
METADATA_VERSION = "2.0"  # UUID система
LEGACY_METADATA_VERSION = "1.0"  # Старая система
```

Эта архитектура обеспечивает гибкость, производительность и безопасность, позволяя ClonerPro эффективно решать широкий спектр задач клонирования в Blender.