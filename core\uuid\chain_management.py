"""
Blender UUID Chain Management for ClonerPro
Provides chain management functionality using UUID-based identification
"""

import bpy
from typing import Dict, List, Optional, Tuple
from .manager import BlenderClonerUUIDManager

def _uuid_chain_debug_print(level, message):
    """Print UUID chain debug messages"""
    # Упрощенное логирование: всегда печатаем важные сообщения (level 1+)
    if level <= 1:
        print(message)

class BlenderUUIDChainManager:
    """Управление цепочками клонеров через UUID в Blender"""
    
    @staticmethod
    def create_cloner_chain_in_blender(context, source_obj, cloner_configs: List[Dict]):
        """
        Создание цепочки клонеров в Blender с UUID связями
        
        Args:
            context: Blender context
            source_obj: Исходный объект
            cloner_configs: Список конфигураций клонеров
        """
        chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
        created_cloners = []
        
        previous_uuid = ""
        current_source = source_obj
        
        for i, config in enumerate(cloner_configs):
            _uuid_chain_debug_print(2, f"🔗 Creating Blender cloner {i+1}/{len(cloner_configs)}: {config['type']}")
            
            # Создаем клонер используя Blender операторы
            cloner_obj = BlenderUUIDChainManager._create_blender_cloner(
                context,
                current_source,
                config["type"],
                config["mode"],
                **config.get("params", {})
            )
            
            if not cloner_obj:
                _uuid_chain_debug_print(1, f"❌ Failed to create Blender cloner {i+1}")
                break
            
            # Получаем модификатор и устанавливаем UUID метаданные
            modifier = None
            for mod in cloner_obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    modifier = mod
                    break
            
            if modifier:
                BlenderClonerUUIDManager.set_cloner_uuid_metadata(
                    modifier,
                    config["type"],
                    config["mode"],
                    chain_uuid=chain_uuid,
                    previous_cloner_uuid=previous_uuid,
                    chain_sequence=i,
                    display_name=config.get("display_name", f"{config['type']} #{i+1}")
                )
                
                # Обновляем next_cloner_uuids у предыдущего клонера
                if previous_uuid:
                    BlenderUUIDChainManager._update_next_cloner_uuid(previous_uuid, modifier["cloner_uuid"])
                
                # Устанавливаем связи для следующей итерации
                previous_uuid = modifier["cloner_uuid"]
                current_source = cloner_obj
                created_cloners.append((cloner_obj, modifier))
        
        _uuid_chain_debug_print(1, f"✅ Blender chain creation complete: {len(created_cloners)} cloners with UUID {chain_uuid}")
        return chain_uuid, created_cloners
    
    @staticmethod
    def _create_blender_cloner(context, source_obj, cloner_type, mode, **params):
        """Создать клонер используя Blender API"""
        try:
            # Используем существующую логику создания клонеров
            # Но адаптируем под Blender контекст
            
            if mode == "OBJECT":
                # Создаем новый объект клонера
                return BlenderUUIDChainManager._create_object_mode_cloner(
                    context, source_obj, cloner_type, **params
                )
            elif mode == "STACKED":
                # Добавляем модификатор на исходный объект
                return BlenderUUIDChainManager._create_stacked_mode_cloner(
                    context, source_obj, cloner_type, **params
                )
            elif mode == "COLLECTION":
                # Создаем клонер коллекций
                return BlenderUUIDChainManager._create_collection_mode_cloner(
                    context, source_obj, cloner_type, **params
                )
            
        except Exception as e:
            print(f"Error creating Blender cloner: {e}")
            return None
    
    @staticmethod
    def _create_object_mode_cloner(context, source_obj, cloner_type, **params):
        """Создать Object mode клонер в Blender"""
        # Создаем новый mesh объект
        mesh = bpy.data.meshes.new(f"{cloner_type}_Cloner_Mesh")
        cloner_obj = bpy.data.objects.new(f"{cloner_type}_Cloner", mesh)
        
        # Добавляем в сцену
        context.collection.objects.link(cloner_obj)
        
        # Добавляем Geometry Nodes модификатор
        modifier = cloner_obj.modifiers.new(name="GeometryNodes", type='NODES')
        
        # Загружаем соответствующую node group
        node_group = BlenderUUIDChainManager._get_or_create_node_group(cloner_type)
        modifier.node_group = node_group
        
        # Настраиваем входы модификатора
        BlenderUUIDChainManager._setup_cloner_inputs(modifier, source_obj, **params)
        
        return cloner_obj
    
    @staticmethod
    def _create_stacked_mode_cloner(context, source_obj, cloner_type, **params):
        """Создать Stacked mode клонер в Blender"""
        # Добавляем модификатор прямо на исходный объект
        modifier = source_obj.modifiers.new(name=f"{cloner_type}_Cloner", type='NODES')
        
        # Загружаем соответствующую node group
        node_group = BlenderUUIDChainManager._get_or_create_node_group(cloner_type)
        modifier.node_group = node_group
        
        # Настраиваем входы модификатора
        BlenderUUIDChainManager._setup_cloner_inputs(modifier, source_obj, **params)
        
        return source_obj
    
    @staticmethod
    def _create_collection_mode_cloner(context, source_obj, cloner_type, **params):
        """Создать Collection mode клонер в Blender"""
        # Создаем пустой объект для коллекций
        cloner_obj = bpy.data.objects.new(f"{cloner_type}_Collection_Cloner", None)
        context.collection.objects.link(cloner_obj)
        
        # Добавляем Geometry Nodes модификатор
        modifier = cloner_obj.modifiers.new(name="GeometryNodes", type='NODES')
        
        # Загружаем соответствующую node group
        node_group = BlenderUUIDChainManager._get_or_create_node_group(cloner_type)
        modifier.node_group = node_group
        
        # Настраиваем входы модификатора для работы с коллекциями
        BlenderUUIDChainManager._setup_collection_cloner_inputs(modifier, source_obj, **params)
        
        return cloner_obj
    
    @staticmethod
    def _get_or_create_node_group(cloner_type: str):
        """Получить или создать node group для клонера"""
        # Ищем существующую node group
        node_group_name = f"{cloner_type}Cloner"
        
        if node_group_name in bpy.data.node_groups:
            return bpy.data.node_groups[node_group_name]
        
        # Если не найдена, создаем новую (это должно интегрироваться с существующей системой)
        # Здесь должна быть интеграция с существующими компонентами клонеров
        print(f"⚠️ Node group {node_group_name} not found, creating basic template")
        
        # Создаем базовую node group
        node_group = bpy.data.node_groups.new(node_group_name, 'GeometryNodeTree')
        
        # Добавляем базовые input/output узлы
        input_node = node_group.nodes.new('NodeGroupInput')
        output_node = node_group.nodes.new('NodeGroupOutput')
        
        # Добавляем базовые интерфейсы
        node_group.interface.new_socket("Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
        node_group.interface.new_socket("Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
        
        return node_group
    
    @staticmethod
    def _setup_cloner_inputs(modifier, source_obj, **params):
        """Настроить входы клонера"""
        if not modifier.node_group:
            return
        
        # Настраиваем основные параметры клонера
        for item in modifier.node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT':
                param_name = item.identifier
                
                # Устанавливаем значения из параметров
                if param_name in params:
                    try:
                        modifier[param_name] = params[param_name]
                    except Exception as e:
                        print(f"Warning: Could not set parameter {param_name}: {e}")
    
    @staticmethod
    def _setup_collection_cloner_inputs(modifier, source_obj, **params):
        """Настроить входы клонера коллекций"""
        # Специальная настройка для коллекций
        BlenderUUIDChainManager._setup_cloner_inputs(modifier, source_obj, **params)
        
        # Дополнительные настройки для collection mode
        collection_name = params.get("collection_name")
        if collection_name and collection_name in bpy.data.collections:
            # Устанавливаем коллекцию для клонирования
            for item in modifier.node_group.interface.items_tree:
                if (item.item_type == 'SOCKET' and 
                    item.in_out == 'INPUT' and 
                    getattr(item, 'socket_type', '') == 'NodeSocketCollection'):
                    try:
                        modifier[item.identifier] = bpy.data.collections[collection_name]
                        break
                    except Exception as e:
                        print(f"Warning: Could not set collection: {e}")
    
    @staticmethod
    def _update_next_cloner_uuid(previous_uuid: str, next_uuid: str):
        """Обновить next_cloner_uuids у предыдущего клонера"""
        prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
        if prev_obj and prev_modifier:
            current_next = prev_modifier.get("next_cloner_uuids", "")
            if current_next:
                prev_modifier["next_cloner_uuids"] = f"{current_next},{next_uuid}"
            else:
                prev_modifier["next_cloner_uuids"] = next_uuid
    
    @staticmethod
    def delete_cloner_with_uuid_recovery_blender(context, cloner_uuid: str):
        """Удаление клонера с восстановлением UUID цепочки в Blender"""
        print(f"🗑️ Deleting Blender cloner by UUID: {cloner_uuid}")
        
        # Находим клонер для удаления
        cloner_obj, cloner_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid)
        if not cloner_obj or not cloner_modifier:
            _uuid_chain_debug_print(1, f"❌ Blender cloner with UUID {cloner_uuid} not found")
            return False
        
        # Получаем информацию о связях
        chain_uuid = cloner_modifier.get("chain_uuid", "")
        previous_uuid = cloner_modifier.get("previous_cloner_uuid", "")
        next_uuids = cloner_modifier.get("next_cloner_uuids", "").split(",")
        next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
        
        _uuid_chain_debug_print(2, f"🔗 Blender chain info: chain={chain_uuid}, prev={previous_uuid}, next={next_uuids}")
        
        # Выполняем удаление объекта через Blender API
        success = BlenderUUIDChainManager._execute_blender_cloner_deletion(
            context, cloner_obj, cloner_modifier
        )
        if not success:
            return False
        
        # Восстанавливаем цепочку
        if previous_uuid and next_uuids:
            # Связываем предыдущий с первым следующим
            BlenderUUIDChainManager._reconnect_blender_chain_after_deletion(
                context, previous_uuid, next_uuids[0], cloner_uuid
            )
            
            # Обновляем метаданные всех следующих клонеров
            for next_uuid in next_uuids:
                BlenderUUIDChainManager._update_blender_cloner_after_deletion(
                    next_uuid, previous_uuid, cloner_uuid
                )
        
        elif next_uuids and not previous_uuid:
            # Удаляем первый клонер в цепочке
            for next_uuid in next_uuids:
                BlenderUUIDChainManager._update_first_blender_cloner_after_deletion(
                    next_uuid, cloner_uuid
                )
        
        # Обновляем Blender dependency graph
        bpy.context.view_layer.update()
        
        _uuid_chain_debug_print(1, f"✅ Blender UUID-based deletion and recovery complete")
        return True
    
    @staticmethod
    def _execute_blender_cloner_deletion(context, cloner_obj, cloner_modifier):
        """Выполнить удаление клонера через Blender API"""
        try:
            # Сохраняем данные для очистки
            cloner_obj_name = cloner_obj.name
            mesh_data = cloner_obj.data
            node_group = cloner_modifier.node_group
            
            # Восстанавливаем видимость оригинального объекта если нужно
            original_obj_name = cloner_modifier.get("original_object", "")
            if original_obj_name and original_obj_name in bpy.data.objects:
                original_obj = bpy.data.objects[original_obj_name]
                original_obj.hide_viewport = False
            
            # Удаляем объект клонера
            bpy.data.objects.remove(cloner_obj)
            
            # Очищаем неиспользуемые данные
            if mesh_data and mesh_data.users == 0:
                bpy.data.meshes.remove(mesh_data)
            
            # Не удаляем node_group если она используется другими клонерами
            if node_group and node_group.users == 1:
                bpy.data.node_groups.remove(node_group)
            
            print(f"✓ Blender cloner {cloner_obj_name} deleted successfully")
            return True
            
        except Exception as e:
            print(f"Error deleting Blender cloner: {e}")
            return False
    
    @staticmethod
    def _reconnect_blender_chain_after_deletion(context, previous_uuid: str, next_uuid: str, deleted_uuid: str):
        """Переподключить цепочку после удаления клонера в Blender"""
        print(f"🔌 Reconnecting Blender chain: {previous_uuid} → {next_uuid}")
        
        # Находим клонеры
        prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
        next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
        
        if not prev_obj or not next_obj:
            _uuid_chain_debug_print(1, f"❌ Could not find Blender cloners for reconnection")
            return False
        
        # Обновляем метаданные связей
        current_next_list = prev_modifier.get("next_cloner_uuids", "").split(",")
        current_next_list = [uuid.strip() for uuid in current_next_list if uuid.strip()]
        
        if deleted_uuid in current_next_list:
            idx = current_next_list.index(deleted_uuid)
            current_next_list[idx] = next_uuid
        
        prev_modifier["next_cloner_uuids"] = ",".join(current_next_list)
        next_modifier["previous_cloner_uuid"] = previous_uuid
        
        # Физически переподключаем входы модификатора
        success = BlenderUUIDChainManager._reconnect_blender_cloner_input(next_obj, prev_obj)
        
        # Обновляем Blender dependency graph
        bpy.context.view_layer.update()
        
        return success
    
    @staticmethod
    def _reconnect_blender_cloner_input(target_cloner_obj, source_cloner_obj):
        """Физическое переподключение входа клонера в Blender"""
        target_modifier = None
        for mod in target_cloner_obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                target_modifier = mod
                break
        
        if not target_modifier:
            return False
        
        try:
            # Находим активный объектный input socket
            for item in target_modifier.node_group.interface.items_tree:
                if (item.item_type == 'SOCKET' and 
                    item.in_out == 'INPUT' and 
                    getattr(item, 'socket_type', '') == 'OBJECT'):
                    
                    current_value = target_modifier.get(item.identifier)
                    if current_value is not None:  # Активный сокет
                        target_modifier[item.identifier] = source_cloner_obj
                        print(f"✓ Reconnected {target_cloner_obj.name} to {source_cloner_obj.name}")
                        
                        # Принудительное обновление Blender
                        target_cloner_obj.update_tag()
                        bpy.context.view_layer.update()
                        return True
            
            return False
            
        except Exception as e:
            print(f"Error reconnecting Blender cloner input: {e}")
            return False
    
    @staticmethod
    def _update_blender_cloner_after_deletion(cloner_uuid: str, new_previous_uuid: str, deleted_uuid: str):
        """Обновить клонер после удаления в цепочке"""
        obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid)
        if obj and modifier:
            modifier["previous_cloner_uuid"] = new_previous_uuid
            # Обновляем chain_sequence если нужно
            current_sequence = modifier.get("chain_sequence", 0)
            if current_sequence > 0:
                modifier["chain_sequence"] = current_sequence - 1
    
    @staticmethod
    def _update_first_blender_cloner_after_deletion(cloner_uuid: str, deleted_uuid: str):
        """Обновить первый клонер после удаления предыдущего"""
        obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid)
        if obj and modifier:
            modifier["previous_cloner_uuid"] = ""
            modifier["chain_sequence"] = 0