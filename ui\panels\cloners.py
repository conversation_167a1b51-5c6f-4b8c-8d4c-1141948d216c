"""
Main Cloners Panel для ClonerPro
Точная копия интерфейса из Advanced Cloners
"""

import bpy
from bpy.types import Panel
from ...core.system.settings import get_settings


class CLONERS_PT_Main(Panel):
    """Главная панель клонеров - точная копия advanced_cloners"""
    bl_label = "Cloners"
    bl_idname = "CLONERS_PT_Main"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 0

    def draw(self, context):
        layout = self.layout
        settings = get_settings()
        
        # Секция 1: Creation Interface
        self.draw_creation_interface(layout, settings, context)
        
        
        # Секция 2: Universal Cloner Browser
        self.draw_universal_browser(layout, context)
        
        # Секция 3: Individual Cloner Settings
        self.draw_cloner_list(layout, context)

    def draw_creation_interface(self, layout, settings, context):
        """Секция создания клонеров с фильтрацией по системам"""
        creation_box = layout.box()
        
        # System Filter (новое!)
        self.draw_system_filter(creation_box, context)
        
        # Clone Source: Object/Collection radio buttons
        source_row = creation_box.row(align=True)
        source_row.label(text="Clone Source:")
        
        # Режимы как radio buttons
        mode_row = source_row.row(align=True)
        mode_row.scale_x = 1.5
        
        obj_op = mode_row.operator("clonerpro.set_creation_mode", 
                                 text="Object", 
                                 depress=(settings.creation_mode == 'OBJECT'))
        obj_op.mode = 'OBJECT'
        
        coll_op = mode_row.operator("clonerpro.set_creation_mode",
                                  text="Collection", 
                                  depress=(settings.creation_mode == 'COLLECTION'))
        coll_op.mode = 'COLLECTION'
        
        # Collection selector (только для Collection mode)
        if settings.creation_mode == 'COLLECTION':
            coll_row = creation_box.row()
            coll_row.label(text="Collection:")
            coll_row.prop_search(settings, "collection_name", bpy.data, "collections", text="")
        
        # Anti-Recursion checkbox (для Object и Collection режимов)
        anti_row = creation_box.row()
        anti_row.alignment = 'RIGHT'
        anti_row.prop(context.scene, "use_anti_recursion", text="Anti-Recursion")
        
        # Отображение секций клонеров с учётом фильтров
        self.draw_cloner_sections(creation_box, settings, context)
    
    def draw_system_filter(self, layout, context):
        """Фильтрация по системам клонеров"""
        scene = context.scene
        
        # UI для фильтров (свойства уже зарегистрированы в __init__.py)
        filter_box = layout.box()
        filter_row = filter_box.row()
        filter_row.label(text="Show Systems:")
        filter_row.prop(scene, "clonerpro_show_unified", text="Unified", toggle=True)
        filter_row.prop(scene, "clonerpro_show_mesh", text="Mesh", toggle=True)
    
    def draw_cloner_sections(self, layout, settings, context):
        """Отрисовка секций клонеров с учётом фильтров"""
        scene = context.scene
        show_unified = getattr(scene, 'clonerpro_show_unified', True)
        show_mesh = getattr(scene, 'clonerpro_show_mesh', True)
        
        # Base Cloners section (показывать только если unified включен)
        if show_unified:
            layout.separator()
            base_label = layout.row()
            base_label.label(text="Base Cloners:", icon='MESH_DATA')
            
            # Base Stacking toggle (только для Object mode)
            if settings.creation_mode == 'OBJECT':
                toggles_row = layout.row(align=True)
                toggles_row.prop(context.scene, "use_stacked_modifiers", text="Base Stacking", toggle=True)
            
            # 2x2 сетка базовых клонеров
            grid_box = layout.box()
            
            # Первый ряд: Grid, Linear
            row1 = grid_box.row(align=True)
            row1.scale_y = 1.2
            
            grid_op = row1.operator("clonerpro.create_cloner", text="Grid", icon="MESH_GRID")
            grid_op.component_type = "GRID"
            
            linear_op = row1.operator("clonerpro.create_cloner", text="Linear", icon="CURVE_PATH")
            linear_op.component_type = "LINEAR"
            
            # Второй ряд: Circle, Spiral  
            row2 = grid_box.row(align=True)
            row2.scale_y = 1.2
            
            circle_op = row2.operator("clonerpro.create_cloner", text="Circle", icon="MESH_CIRCLE")
            circle_op.component_type = "CIRCLE"
            
            spiral_op = row2.operator("clonerpro.create_cloner", text="Spiral", icon="FORCE_VORTEX")
            spiral_op.component_type = "SPIRAL"
        
        # Mesh Cloners section (показывать только если mesh включен)
        if show_mesh:
            layout.separator()
            mesh_label = layout.row()
            mesh_label.label(text="Mesh Cloners:", icon='OBJECT_DATA')
            
            # Mesh Stacking toggle (для Object и Collection mode)
            if settings.creation_mode in ['OBJECT', 'COLLECTION']:
                mesh_toggles_row = layout.row(align=True)
                mesh_toggles_row.prop(context.scene, "use_mesh_stacked_modifiers", text="Mesh Stacking", toggle=True)
        
            # 2x1 сетка mesh клонеров
            mesh_box = layout.box()
            
            # Первый ряд: Object, Spline
            mesh_row1 = mesh_box.row(align=True)
            mesh_row1.scale_y = 1.2
            
            # ObjectCloner - используем унифицированный оператор
            obj_op = mesh_row1.operator("clonerpro.create_cloner", text="Object", icon="OBJECT_DATA")
            obj_op.component_type = "OBJECT"
            
            spline_op = mesh_row1.operator("clonerpro.create_cloner", text="Spline", icon="CURVE_BEZCURVE")
            spline_op.component_type = "SPLINE"
            
            # Второй ряд: Volume
            mesh_row2 = mesh_box.row(align=True)
            mesh_row2.scale_y = 1.2
            
            vol_op = mesh_row2.operator("clonerpro.create_cloner", text="Volume", icon="MESH_CUBE")
            vol_op.component_type = "VOLUME"
            
            # Заполнить пустое место
            mesh_row2.label(text="")

    def draw_universal_browser(self, layout, context):
        """Universal Cloner Browser с НОВОЙ СИСТЕМОЙ ОБНАРУЖЕНИЯ"""
        from ...core.services.cloner_scanner import scan_scene_cloners
        
        browser_box = layout.box()
        
        # Заголовок с expand/collapse и refresh кнопками
        header_row = browser_box.row(align=True)
        
        # Кнопка expand/collapse
        expanded = getattr(context.scene, "browser_expanded_state", False)
        expand_icon = "TRIA_DOWN" if expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_expand", text="", icon=expand_icon, emboss=False)
        expand_op.obj_name = "global"
        expand_op.modifier_name = "browser"
        expand_op.state_property = "browser_expanded_state"
        
        header_row.label(text="Universal Cloner Browser", icon="WORLD")
        header_row.operator("clonerpro.refresh_browser", text="", icon="FILE_REFRESH", emboss=False)
        
        # Показываем содержимое только если развернуто
        if not expanded:
            return
        
        
        # Browser content
        content_box = browser_box.box()
        
        # НОВОЕ: Используем систему обнаружения с фильтрацией
        all_cloners = scan_scene_cloners(context)
        filtered_cloners = self.filter_cloners_by_system(all_cloners, context)
        
        if not filtered_cloners:
            content_box.label(text="No cloners found (check system filters)", icon="INFO")
        else:
            # Группировка клонеров по режимам
            groups = self.group_discovered_cloners(filtered_cloners)
            for group_name, group_cloners in groups.items():
                self.draw_browser_group_new(content_box, group_name, group_cloners)
        
        
        # Статистика
        stats_row = browser_box.row()
        stats_row.alignment = 'CENTER'
        stats_row.scale_y = 0.8
        if filtered_cloners:
            groups = self.group_discovered_cloners(filtered_cloners)
            stats_row.label(text=f"Groups: {len(groups)} | Cloners: {len(filtered_cloners)}")
        else:
            stats_row.label(text="Groups: 0 | Cloners: 0")
    
    def filter_cloners_by_system(self, cloners, context):
        """Фильтрует клонеры по системам в зависимости от настроек"""
        from ...core.core import get_cloner_system
        
        scene = context.scene
        show_unified = getattr(scene, 'clonerpro_show_unified', True)
        show_mesh = getattr(scene, 'clonerpro_show_mesh', True)
        
        if show_unified and show_mesh:
            return cloners  # Показываем все
        
        filtered = []
        for cloner in cloners:
            cloner_type = cloner.get('cloner_type', '')
            system = get_cloner_system(cloner_type)
            
            if ((system == 'unified' and show_unified) or 
                (system == 'mesh' and show_mesh)):
                filtered.append(cloner)
        
        return filtered

    def draw_browser_group(self, layout, group_name, cloners):
        """Отрисовка группы клонеров в браузере"""
        group_row = layout.row(align=True)
        
        # Expand/collapse треугольник
        group_row.label(text="", icon='TRIA_DOWN')  # TODO: добавить состояние
        
        # Название группы с количеством
        group_row.label(text=f"{group_name} ({len(cloners)})", icon="MESH_DATA")
        
        # Отрисовка клонеров в группе
        for cloner in cloners:
            self.draw_browser_cloner_item(layout, cloner)

    def draw_browser_cloner_item(self, layout, cloner):
        """Отрисовка одного клонера в браузере"""
        item_row = layout.row(align=True)
        item_row.scale_y = 0.9
        
        # Отступ
        item_row.label(text="", icon='BLANK1')
        
        # Точка списка
        item_row.label(text="•")
        
        # Название клонера
        item_row.label(text=cloner['name'])
        
        # Тип в скобках
        if cloner.get('category'):
            item_row.label(text=f"[{cloner['category']}]")
        
        # Action buttons
        actions = item_row.row(align=True)
        actions.scale_x = 0.8
        
        # Видимость
        vis_icon = "HIDE_OFF" if cloner.get('visible', True) else "HIDE_ON"
        actions.operator("clonerpro.toggle_component_visibility", text="", icon=vis_icon, emboss=False)
        
        # Удаление - ТОЧНАЯ КОПИЯ ИЗ ADVANCED_CLONERS + поддержка браузера
        del_op = actions.operator("object.delete_cloner", text="", icon="X", emboss=False)
        del_op.modifier_name = cloner.get('modifier_name', 'MISSING')
        del_op.object_name = cloner.get('object_name', '')  # Указываем конкретный объект для браузера

    def draw_cloner_list(self, layout, context):
        """Список индивидуальных настроек клонеров с АВТОГЕНЕРАЦИЕЙ UI"""
        from ...core.services.cloner_scanner import get_active_object_cloners
        
        # Используем новую систему обнаружения
        cloners = get_active_object_cloners(context)
        
        if not cloners:
            return
        
        list_box = layout.box()
        list_box.label(text=f"Active Object Cloners: {len(cloners)}", icon="MESH_DATA")
        
        for cloner_info in cloners:
            self.draw_individual_cloner(list_box, cloner_info, context)

    def draw_individual_cloner(self, layout, cloner_info, context):
        """Отрисовка индивидуального клонера с ТОЧНЫМИ паттернами expand/collapse"""
        from ...ui.utils.ui_helpers import is_element_expanded
        
        cloner_box = layout.box()
        
        # Получаем данные
        cloner_obj = cloner_info['object']
        modifier = cloner_info['modifier']
        display_name = cloner_info.get('display_name', 'Unknown Cloner')
        
        # Получаем состояние раскрытия
        expanded = is_element_expanded(context, cloner_obj.name, modifier.name, "cloner_expanded_states")
        
        # Заголовок клонера
        header_row = cloner_box.row(align=True)
        
        # Expand/collapse кнопка - ТОЧНЫЙ паттерн
        expand_op = header_row.operator("clonerpro.toggle_cloner_expanded", text="", 
                                      icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
        expand_op.obj_name = cloner_obj.name
        expand_op.modifier_name = modifier.name
        
        # Название клонера
        header_row.label(text=display_name)
        
        # Видимость
        vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
        vis_op = header_row.operator("clonerpro.toggle_component_visibility", text="", icon=vis_icon, emboss=False)
        vis_op.object_name = cloner_obj.name
        vis_op.modifier_name = modifier.name
        
        # Control buttons
        controls = header_row.row(align=True)
        controls.scale_x = 0.8
        controls.operator("clonerpro.move_cloner_up", text="", icon="TRIA_UP", emboss=False)
        controls.operator("clonerpro.move_cloner_down", text="", icon="TRIA_DOWN", emboss=False)
        
        del_op = controls.operator("object.delete_cloner", text="", icon="X", emboss=False)
        del_op.object_name = cloner_obj.name
        del_op.modifier_name = modifier.name
        
        # АВТОГЕНЕРАЦИЯ ПАРАМЕТРОВ - только если развернуто
        if expanded:
            self.draw_cloner_parameters(cloner_box, cloner_info)
    
    def draw_cloner_parameters(self, layout, cloner_info):
        """Единый UI параметров для клонера в стиле ObjectCloner"""
        from ..generators.cloner_ui import UnifiedClonerUI
        
        cloner_type = cloner_info.get('cloner_type')
        modifier = cloner_info.get('modifier')
        
        if not cloner_type or not modifier:
            return
        
        # Получаем context из клонера
        context = bpy.context
        
        # Отрисовываем настройки конкретного клонера в едином стиле ObjectCloner
        UnifiedClonerUI.draw_cloner_settings(layout, context, modifier)

    def get_scene_cloners(self, context):
        """Получить все клонеры из сцены"""
        cloners = []
        
        # Поиск объектов с Geometry Nodes модификаторами
        for obj in context.scene.objects:
            for modifier in obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    cloner_type = self.detect_cloner_type(modifier.node_group.name)
                    if cloner_type:
                        # Получить метаданные из модификатора
                        original_obj = modifier.get("original_object", "Unknown")
                        source_type = modifier.get("source_type", "OBJECT")
                        collection_name = modifier.get("cloner_collection", "")
                        
                        cloners.append({
                            'name': f"{cloner_type} Object Cloner [{original_obj}]",
                            'type': cloner_type,
                            'object': obj.name,
                            'modifier': modifier.name,
                            'group': collection_name,
                            'category': source_type,
                            'original_object': original_obj,
                            'visible': modifier.show_viewport
                        })
        
        return cloners

    def detect_cloner_type(self, node_group_name):
        """Определить тип клонера по имени node group"""
        name_lower = node_group_name.lower()
        
        if 'grid' in name_lower:
            return 'Grid'
        elif 'linear' in name_lower:
            return 'Linear'
        elif 'circle' in name_lower:
            return 'Circle'
        elif 'spiral' in name_lower:
            return 'Spiral'
        elif 'object' in name_lower:
            return 'Object'
        elif 'spline' in name_lower:
            return 'Spline'
        elif 'volume' in name_lower:
            return 'Volume'
        
        return None

    def group_cloners(self, cloners):
        """Группировка клонеров для браузера"""
        groups = {}
        
        for cloner in cloners:
            category = cloner.get('category', 'Unknown')
            group_name = f"{category.title()} Cloners"
            
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(cloner)
        
        return groups
    
    def group_discovered_cloners(self, cloners):
        """Группировка обнаруженных клонеров для браузера"""
        groups = {}
        
        for cloner_info in cloners:
            cloner_mode = cloner_info.get('cloner_mode', 'Unknown')
            group_name = f"{cloner_mode.title()} Cloners"
            
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(cloner_info)
        
        return groups
    
    def draw_browser_group_new(self, layout, group_name, cloners):
        """Отрисовка группы обнаруженных клонеров в браузере с цветовым кодированием"""
        from ...core.system.settings import get_settings
        
        settings = get_settings()
        
        # Получить состояние группы
        group_state = None
        for state in settings.browser_group_states:
            if state.group_name == group_name:
                group_state = state
                break
        
        # Если состояние не найдено, используем значение по умолчанию (развернуто)
        if not group_state:
            is_expanded = True
        else:
            is_expanded = group_state.is_expanded
        
        group_row = layout.row(align=True)
        
        # Expand/collapse кнопка с правильной иконкой
        icon = 'TRIA_DOWN' if is_expanded else 'TRIA_RIGHT'
        toggle_op = group_row.operator("clonerpro.toggle_group_expanded", text="", icon=icon, emboss=False)
        toggle_op.group_name = group_name
        
        
        # Название группы с количеством
        group_row.label(text=f"{group_name} ({len(cloners)})")
        
        # Отрисовка клонеров в группе только если развернуто
        if is_expanded:
            for cloner_info in cloners:
                self.draw_browser_cloner_item_new(layout, cloner_info)
    
    def draw_browser_cloner_item_new(self, layout, cloner_info):
        """Отрисовка одного обнаруженного клонера в браузере с цветовым кодированием"""
        item_row = layout.row(align=True)
        item_row.scale_y = 0.9
        
        # Отступ
        item_row.label(text="", icon='BLANK1')
        
        # Получаем данные
        cloner_obj = cloner_info.get('object')
        modifier = cloner_info.get('modifier')
        display_name = cloner_info.get('display_name', 'Unknown Cloner')
        cloner_mode = cloner_info.get('cloner_mode', 'OBJECT')
        
        # Определяем активен ли клонер
        is_active = (bpy.context.active_object == cloner_obj if cloner_obj else False)
        
        # Простая кнопка для выбора клонера без цветового кодирования
        button_row = item_row.row(align=True)
        select_op = button_row.operator(
            "clonerpro.select_cloner",
            text=display_name,
            icon='RADIOBUT_ON' if is_active else 'RADIOBUT_OFF'
        )
            
        # Устанавливаем параметры оператора
        if cloner_obj:
            select_op.object_name = cloner_obj.name
        if modifier:
            select_op.modifier_name = modifier.name
        
        # Action buttons
        actions = item_row.row(align=True)
        actions.scale_x = 0.8
        
        # Видимость
        if modifier:
            vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
            vis_op = actions.operator("clonerpro.toggle_component_visibility", text="", icon=vis_icon, emboss=False)
            vis_op.object_name = cloner_obj.name if cloner_obj else ""
            vis_op.modifier_name = modifier.name
        
        # Удаление
        if modifier and cloner_obj:
            del_op = actions.operator("object.delete_cloner", text="", icon="X", emboss=False)
            del_op.object_name = cloner_obj.name
            del_op.modifier_name = modifier.name


# Новые операторы для управления клонерами
class CLONERPRO_OT_move_cloner_up(bpy.types.Operator):
    """Переместить клонер вверх"""
    bl_idname = "clonerpro.move_cloner_up"
    bl_label = "Move Cloner Up"
    bl_description = "Move cloner up in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved cloner up")
        return {'FINISHED'}


class CLONERPRO_OT_move_cloner_down(bpy.types.Operator):
    """Переместить клонер вниз"""
    bl_idname = "clonerpro.move_cloner_down"
    bl_label = "Move Cloner Down"
    bl_description = "Move cloner down in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved cloner down")
        return {'FINISHED'}


def register():
    """Регистрация главной панели клонеров"""
    bpy.utils.register_class(CLONERPRO_OT_move_cloner_up)
    bpy.utils.register_class(CLONERPRO_OT_move_cloner_down)
    bpy.utils.register_class(CLONERS_PT_Main)


def unregister():
    """Отмена регистрации главной панели клонеров"""
    bpy.utils.unregister_class(CLONERS_PT_Main)
    bpy.utils.unregister_class(CLONERPRO_OT_move_cloner_down)
    bpy.utils.unregister_class(CLONERPRO_OT_move_cloner_up)