"""
Noise Effector - Simplified implementation for ClonerPro
Adapted from advanced_cloners logic without complex architecture
"""

import bpy


def create_noise_effector(name_suffix=""):
    """
    Create Noise Effector node group.
    
    This function creates the complete Noise Effector with all parameters
    and logic for applying noise-based transformations to instances.
    
    Args:
        name_suffix: Optional suffix for the node group name
        
    Returns:
        The created node group
    """
    # Создаем новую группу узлов
    effector_group = bpy.data.node_groups.new(
        type='GeometryNodeTree', 
        name=f"NoiseEffector{name_suffix}"
    )

    # --- Настройка интерфейса ---
    # Выходы
    effector_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Входы
    effector_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    
    # Базовые параметры эффектора
    enable_input = effector_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
    enable_input.default_value = True
    
    strength_input = effector_group.interface.new_socket(name="Strength", in_out='INPUT', socket_type='NodeSocketFloat')
    strength_input.default_value = 1.0
    strength_input.min_value = 0.0
    strength_input.max_value = 2.0

    # Параметры трансформации
    position_input = effector_group.interface.new_socket(name="Position", in_out='INPUT', socket_type='NodeSocketVector')
    position_input.default_value = (0.0, 0.0, 0.0)
    
    rotation_input = effector_group.interface.new_socket(name="Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_input.default_value = (0.0, 0.0, 0.0)
    
    scale_input = effector_group.interface.new_socket(name="Scale", in_out='INPUT', socket_type='NodeSocketVector')
    scale_input.default_value = (0.0, 0.0, 0.0)

    # Специфические для Noise Effector параметры
    uniform_scale_input = effector_group.interface.new_socket(name="Uniform Scale", in_out='INPUT', socket_type='NodeSocketBool')
    uniform_scale_input.default_value = True

    symmetric_translation_input = effector_group.interface.new_socket(name="Symmetric Translation", in_out='INPUT', socket_type='NodeSocketBool')
    symmetric_translation_input.default_value = False

    symmetric_rotation_input = effector_group.interface.new_socket(name="Symmetric Rotation", in_out='INPUT', socket_type='NodeSocketBool')
    symmetric_rotation_input.default_value = False

    # Параметры шума
    noise_scale_input = effector_group.interface.new_socket(name="Noise Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_scale_input.default_value = 0.5
    noise_scale_input.min_value = 0.1
    noise_scale_input.max_value = 10.0

    noise_detail_input = effector_group.interface.new_socket(name="Noise Detail", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_detail_input.default_value = 2.0
    noise_detail_input.min_value = 0.0
    noise_detail_input.max_value = 15.0

    noise_roughness_input = effector_group.interface.new_socket(name="Noise Roughness", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_roughness_input.default_value = 0.5
    noise_roughness_input.min_value = 0.0
    noise_roughness_input.max_value = 1.0

    noise_lacunarity_input = effector_group.interface.new_socket(name="Noise Lacunarity", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_lacunarity_input.default_value = 2.0
    noise_lacunarity_input.min_value = 0.0
    noise_lacunarity_input.max_value = 10.0

    noise_distortion_input = effector_group.interface.new_socket(name="Noise Distortion", in_out='INPUT', socket_type='NodeSocketFloat')
    noise_distortion_input.default_value = 0.0
    noise_distortion_input.min_value = -10.0
    noise_distortion_input.max_value = 10.0

    # Позиция шума и масштаб
    noise_position_input = effector_group.interface.new_socket(name="Noise Position", in_out='INPUT', socket_type='NodeSocketVector')
    noise_position_input.default_value = (0.0, 0.0, 0.0)

    noise_xyz_scale_input = effector_group.interface.new_socket(name="Noise XYZ Scale", in_out='INPUT', socket_type='NodeSocketVector')
    noise_xyz_scale_input.default_value = (1.0, 1.0, 1.0)

    # Анимация
    speed_input = effector_group.interface.new_socket(name="Speed", in_out='INPUT', socket_type='NodeSocketFloat')
    speed_input.default_value = 0.0
    speed_input.min_value = 0.0
    speed_input.max_value = 10.0

    seed_input = effector_group.interface.new_socket(name="Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0

    # --- Создание узлов ---
    nodes = effector_group.nodes
    links = effector_group.links

    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-1000, 0)
    
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (1000, 0)

    # Basic switch for enabling/disabling the effector
    switch = nodes.new('GeometryNodeSwitch')
    switch.input_type = 'GEOMETRY'
    switch.location = (800, 0)
    links.new(group_input.outputs['Enable'], switch.inputs[0])  # Switch
    links.new(group_input.outputs['Geometry'], switch.inputs[1])  # True (bypass)

    # Get position for noise input
    position = nodes.new('GeometryNodeInputPosition')
    position.location = (-800, -200)

    # Get index for per-instance noise offset
    index = nodes.new('GeometryNodeInputIndex')
    index.location = (-800, -300)

    # Scene time for animation
    scene_time = nodes.new('GeometryNodeInputSceneTime')
    scene_time.location = (-800, -400)

    # Calculate time factor for animation
    time_factor = nodes.new('ShaderNodeMath')
    time_factor.operation = 'MULTIPLY'
    time_factor.location = (-600, -400)
    links.new(scene_time.outputs[1], time_factor.inputs[0])  # Frame
    links.new(group_input.outputs['Speed'], time_factor.inputs[1])  # Speed

    # Add instance index for variation
    index_to_float = nodes.new('ShaderNodeMath')
    index_to_float.operation = 'MULTIPLY'
    index_to_float.location = (-600, -300)
    links.new(index.outputs['Index'], index_to_float.inputs[0])
    index_to_float.inputs[1].default_value = 123.456  # Arbitrary scaling factor

    # Add seed for more control
    seed_value = nodes.new('ShaderNodeMath')
    seed_value.operation = 'ADD'
    seed_value.location = (-500, -300)
    links.new(index_to_float.outputs[0], seed_value.inputs[0])
    links.new(group_input.outputs['Seed'], seed_value.inputs[1])

    # Add time for animation
    animated_value = nodes.new('ShaderNodeMath')
    animated_value.operation = 'ADD'
    animated_value.location = (-400, -350)
    links.new(seed_value.outputs[0], animated_value.inputs[0])
    links.new(time_factor.outputs[0], animated_value.inputs[1])

    # Apply noise position and scale to position input
    position_offset = nodes.new('ShaderNodeVectorMath')
    position_offset.operation = 'ADD'
    position_offset.location = (-600, -200)
    links.new(position.outputs[0], position_offset.inputs[0])
    links.new(group_input.outputs['Noise Position'], position_offset.inputs[1])

    position_scaled = nodes.new('ShaderNodeVectorMath')
    position_scaled.operation = 'MULTIPLY'
    position_scaled.location = (-500, -200)
    links.new(position_offset.outputs[0], position_scaled.inputs[0])
    links.new(group_input.outputs['Noise XYZ Scale'], position_scaled.inputs[1])

    # Position noise (for translation)
    position_noise = nodes.new('ShaderNodeTexNoise')
    position_noise.noise_dimensions = '4D'
    position_noise.location = (-300, -100)
    links.new(position_scaled.outputs[0], position_noise.inputs['Vector'])
    links.new(animated_value.outputs[0], position_noise.inputs['W'])
    links.new(group_input.outputs['Noise Scale'], position_noise.inputs['Scale'])
    links.new(group_input.outputs['Noise Detail'], position_noise.inputs['Detail'])
    links.new(group_input.outputs['Noise Roughness'], position_noise.inputs['Roughness'])
    links.new(group_input.outputs['Noise Lacunarity'], position_noise.inputs['Lacunarity'])
    links.new(group_input.outputs['Noise Distortion'], position_noise.inputs['Distortion'])

    # Rotation noise (with different offset)
    rotation_noise = nodes.new('ShaderNodeTexNoise')
    rotation_noise.noise_dimensions = '4D'
    rotation_noise.location = (-300, -300)
    rotation_offset = nodes.new('ShaderNodeMath')
    rotation_offset.operation = 'ADD'
    rotation_offset.location = (-400, -400)
    links.new(animated_value.outputs[0], rotation_offset.inputs[0])
    rotation_offset.inputs[1].default_value = 42.0  # Different offset
    links.new(position_scaled.outputs[0], rotation_noise.inputs['Vector'])
    links.new(rotation_offset.outputs[0], rotation_noise.inputs['W'])
    links.new(group_input.outputs['Noise Scale'], rotation_noise.inputs['Scale'])
    links.new(group_input.outputs['Noise Detail'], rotation_noise.inputs['Detail'])
    links.new(group_input.outputs['Noise Roughness'], rotation_noise.inputs['Roughness'])
    links.new(group_input.outputs['Noise Lacunarity'], rotation_noise.inputs['Lacunarity'])
    links.new(group_input.outputs['Noise Distortion'], rotation_noise.inputs['Distortion'])

    # Scale noise (with different offset)
    scale_noise = nodes.new('ShaderNodeTexNoise')
    scale_noise.noise_dimensions = '4D'
    scale_noise.location = (-300, -500)
    scale_offset = nodes.new('ShaderNodeMath')
    scale_offset.operation = 'ADD'
    scale_offset.location = (-400, -500)
    links.new(animated_value.outputs[0], scale_offset.inputs[0])
    scale_offset.inputs[1].default_value = 84.0  # Different offset
    links.new(position_scaled.outputs[0], scale_noise.inputs['Vector'])
    links.new(scale_offset.outputs[0], scale_noise.inputs['W'])
    links.new(group_input.outputs['Noise Scale'], scale_noise.inputs['Scale'])
    links.new(group_input.outputs['Noise Detail'], scale_noise.inputs['Detail'])
    links.new(group_input.outputs['Noise Roughness'], scale_noise.inputs['Roughness'])
    links.new(group_input.outputs['Noise Lacunarity'], scale_noise.inputs['Lacunarity'])
    links.new(group_input.outputs['Noise Distortion'], scale_noise.inputs['Distortion'])

    # Convert noise to vector transforms

    # Position transform (map 0-1 to -Position to +Position)
    position_sub = nodes.new('ShaderNodeVectorMath')
    position_sub.operation = 'MULTIPLY_ADD'
    position_sub.location = (-100, -100)
    position_sub.inputs[1].default_value = (2.0, 2.0, 2.0)
    position_sub.inputs[2].default_value = (-1.0, -1.0, -1.0)
    links.new(position_noise.outputs['Color'], position_sub.inputs[0])

    # Multiply by position range
    position_range = nodes.new('ShaderNodeVectorMath')
    position_range.operation = 'MULTIPLY'
    position_range.location = (100, -100)
    links.new(position_sub.outputs[0], position_range.inputs[0])
    links.new(group_input.outputs['Position'], position_range.inputs[1])

    # Rotation transform (map 0-1 to -Rotation to +Rotation)
    rotation_sub = nodes.new('ShaderNodeVectorMath')
    rotation_sub.operation = 'MULTIPLY_ADD'
    rotation_sub.location = (-100, -300)
    rotation_sub.inputs[1].default_value = (2.0, 2.0, 2.0)
    rotation_sub.inputs[2].default_value = (-1.0, -1.0, -1.0)
    links.new(rotation_noise.outputs['Color'], rotation_sub.inputs[0])

    # Multiply by rotation range
    rotation_range = nodes.new('ShaderNodeVectorMath')
    rotation_range.operation = 'MULTIPLY'
    rotation_range.location = (100, -300)
    links.new(rotation_sub.outputs[0], rotation_range.inputs[0])
    links.new(group_input.outputs['Rotation'], rotation_range.inputs[1])

    # Scale transform
    # Map noise to 1-x to 1+x scale range
    scale_add = nodes.new('ShaderNodeVectorMath')
    scale_add.operation = 'MULTIPLY_ADD'
    scale_add.location = (-100, -500)
    scale_add.inputs[1].default_value = (2.0, 2.0, 2.0)
    scale_add.inputs[2].default_value = (-1.0, -1.0, -1.0)
    links.new(scale_noise.outputs['Color'], scale_add.inputs[0])

    # Create base scale vector (1,1,1)
    scale_base = nodes.new('ShaderNodeCombineXYZ')
    scale_base.location = (50, -450)
    scale_base.inputs[0].default_value = 1.0
    scale_base.inputs[1].default_value = 1.0
    scale_base.inputs[2].default_value = 1.0

    # Apply scale variation
    scale_mul = nodes.new('ShaderNodeVectorMath')
    scale_mul.operation = 'MULTIPLY'
    scale_mul.location = (100, -500)
    links.new(scale_add.outputs[0], scale_mul.inputs[0])
    links.new(group_input.outputs['Scale'], scale_mul.inputs[1])

    # Add to base scale
    scale_final = nodes.new('ShaderNodeVectorMath')
    scale_final.operation = 'ADD'
    scale_final.location = (200, -500)
    links.new(scale_base.outputs[0], scale_final.inputs[0])
    links.new(scale_mul.outputs[0], scale_final.inputs[1])

    # For uniform scale (use only the X component)
    # Extract X component
    separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
    separate_xyz.location = (300, -500)
    links.new(scale_final.outputs[0], separate_xyz.inputs[0])

    # Create uniform vector from X
    uniform_scale = nodes.new('ShaderNodeCombineXYZ')
    uniform_scale.location = (400, -500)
    links.new(separate_xyz.outputs[0], uniform_scale.inputs[0])
    links.new(separate_xyz.outputs[0], uniform_scale.inputs[1])
    links.new(separate_xyz.outputs[0], uniform_scale.inputs[2])

    # Switch between uniform and non-uniform scale
    scale_switch = nodes.new('GeometryNodeSwitch')
    scale_switch.input_type = 'VECTOR'
    scale_switch.location = (500, -500)
    links.new(group_input.outputs['Uniform Scale'], scale_switch.inputs[0])  # Switch
    links.new(scale_final.outputs[0], scale_switch.inputs[1])  # False
    links.new(uniform_scale.outputs[0], scale_switch.inputs[2])  # True

    # Apply strength multiplier
    position_strength = nodes.new('ShaderNodeVectorMath')
    position_strength.operation = 'MULTIPLY'
    position_strength.location = (300, -100)
    links.new(position_range.outputs[0], position_strength.inputs[0])
    links.new(group_input.outputs['Strength'], position_strength.inputs[1])  # Scalar

    rotation_strength = nodes.new('ShaderNodeVectorMath')
    rotation_strength.operation = 'MULTIPLY'
    rotation_strength.location = (300, -300)
    links.new(rotation_range.outputs[0], rotation_strength.inputs[0])
    links.new(group_input.outputs['Strength'], rotation_strength.inputs[1])  # Scalar

    # Apply transforms to instances
    translate_instances = nodes.new('GeometryNodeTranslateInstances')
    translate_instances.location = (500, 200)
    links.new(group_input.outputs['Geometry'], translate_instances.inputs['Instances'])
    links.new(position_strength.outputs[0], translate_instances.inputs['Translation'])

    # Rotate instances
    rotate_instances = nodes.new('GeometryNodeRotateInstances')
    rotate_instances.location = (600, 100)
    links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
    links.new(rotation_strength.outputs[0], rotate_instances.inputs['Rotation'])

    # Scale instances
    scale_instances = nodes.new('GeometryNodeScaleInstances')
    scale_instances.location = (700, 0)
    links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
    links.new(scale_switch.outputs[0], scale_instances.inputs['Scale'])

    # Connect to the output
    links.new(scale_instances.outputs['Instances'], switch.inputs[2])  # False
    links.new(switch.outputs[0], group_output.inputs['Geometry'])

    return effector_group


# Compatibility function for easy integration
def create_noise_effector_logic_group(name_suffix=""):
    """Create Noise Effector logic group - compatibility function"""
    return create_noise_effector(name_suffix)