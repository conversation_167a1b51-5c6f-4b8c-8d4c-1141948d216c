"""
UI State Management для ClonerPro
Прямая копия системы expand/collapse из advanced_cloners БЕЗ абстракций
"""

import bpy


def get_expanded_states_key(obj_name, modifier_name):
    """
    Создает ключ для хранения состояния раскрытия модификатора
    Точная копия из advanced_cloners/ui/common/ui_utils.py
    """
    # Ограничиваем длину ключа до 60 символов (63 - максимум для IDProperty)
    if len(obj_name) + len(modifier_name) + 1 > 60:
        obj_hash = str(hash(obj_name) % 10000).zfill(4)
        mod_hash = str(hash(modifier_name) % 10000).zfill(4)
        return f"{obj_name[:25]}_{obj_hash}_{mod_hash}"
    else:
        return f"{obj_name}_{modifier_name}"


def is_element_expanded(context, obj_name, modifier_name, state_property="cloner_expanded_states"):
    """
    Проверяет, развернут ли элемент в UI
    Точная копия из advanced_cloners/ui/common/ui_utils.py
    """
    key = get_expanded_states_key(obj_name, modifier_name)
    expanded_states = context.scene.get(state_property, {})
    return expanded_states.get(key, False)


def set_element_expanded(context, obj_name, modifier_name, state, state_property="cloner_expanded_states"):
    """
    Устанавливает состояние раскрытия элемента в UI
    Точная копия из advanced_cloners/ui/common/ui_utils.py
    """
    key = get_expanded_states_key(obj_name, modifier_name)
    expanded_states = context.scene.get(state_property, {})
    expanded_states[key] = state
    context.scene[state_property] = expanded_states
    
    # Принудительное обновление UI
    for area in context.screen.areas:
        if area.type == 'VIEW_3D':
            area.tag_redraw()


# Convenience functions for specific components
def is_effector_expanded(context, obj_name, modifier_name):
    """Проверить состояние expand/collapse для эффектора"""
    return is_element_expanded(context, obj_name, modifier_name, "effector_expanded_states")


def set_effector_expanded(context, obj_name, modifier_name, state):
    """Установить состояние expand/collapse для эффектора"""
    set_element_expanded(context, obj_name, modifier_name, state, "effector_expanded_states")


def is_cloner_expanded(context, obj_name, modifier_name):
    """Проверить состояние expand/collapse для клонера"""
    return is_element_expanded(context, obj_name, modifier_name, "cloner_expanded_states")


def set_cloner_expanded(context, obj_name, modifier_name, state):
    """Установить состояние expand/collapse для клонера"""
    set_element_expanded(context, obj_name, modifier_name, state, "cloner_expanded_states")