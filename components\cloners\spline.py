"""
Spline Cloner - Class-based implementation
Объединяет логику и конфигурацию Spline Cloner в один класс
"""

import bpy
import bmesh
from mathutils import Vector
from ..base_cloner import BaseCloner


class SplineCloner(BaseCloner):
    """
    Spline Cloner - клонирует объекты вдоль сплайнов/кривых
    Аналогичен Object Cloner, но использует кривые как источник точек
    """
    
    bl_idname = "SPLINE"
    bl_label = "Spline Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Spline Cloner
        Заменяет старый spline_config.py
        """
        # Source Settings
        props_owner.spline_source_mode = bpy.props.EnumProperty(
            name="Source Mode",
            description="Source for cloning",
            items=[
                ('OBJECT', 'Object', 'Clone single object', 'OBJECT_DATA', 0),
                ('COLLECTION', 'Collection', 'Clone objects from collection', 'OUTLINER_COLLECTION', 1),
            ],
            default='OBJECT'
        )
        
        # Spline Distribution Settings
        props_owner.spline_instance_count = bpy.props.IntProperty(
            name="Instance Count",
            description="Number of instances along the spline",
            default=10,
            min=1,
            max=1000
        )
        
        props_owner.spline_curve_start = bpy.props.FloatProperty(
            name="Curve Start", 
            description="Start position along curve (0.0-1.0)",
            default=0.0,
            min=0.0,
            max=1.0
        )
        
        props_owner.spline_curve_end = bpy.props.FloatProperty(
            name="Curve End",
            description="End position along curve (0.0-1.0)", 
            default=1.0,
            min=0.0,
            max=1.0
        )
        
        props_owner.spline_curve_offset = bpy.props.FloatProperty(
            name="Curve Offset",
            description="Offset along curve",
            default=0.0
        )
        
        props_owner.spline_spacing_mode = bpy.props.EnumProperty(
            name="Spacing Mode",
            description="How to distribute instances",
            items=[
                ('COUNT', 'Count', 'Use instance count', 'MESH_GRID', 0),
                ('LENGTH', 'Length', 'Use spacing length', 'ARROW_LEFTRIGHT', 1),
            ],
            default='COUNT'
        )
        
        props_owner.spline_spacing_length = bpy.props.FloatProperty(
            name="Spacing Length",
            description="Distance between instances when using length mode",
            default=1.0,
            min=0.1
        )
        
        props_owner.spline_align_to_spline = bpy.props.BoolProperty(
            name="Align to Spline",
            description="Align instances to spline direction",
            default=True
        )
        
        # Gradient Settings (как в Linear Cloner)
        props_owner.spline_scale_start = bpy.props.FloatVectorProperty(
            name="Scale Start",
            description="Scale at the start of the spline",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.spline_scale_end = bpy.props.FloatVectorProperty(
            name="Scale End", 
            description="Scale at the end of the spline",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.spline_rotation_start = bpy.props.FloatVectorProperty(
            name="Rotation Start",
            description="Rotation at the start of the spline",
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )
        props_owner.spline_rotation_end = bpy.props.FloatVectorProperty(
            name="Rotation End",
            description="Rotation at the end of the spline", 
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )
        
        # Hide Original
        props_owner.spline_hide_original = bpy.props.BoolProperty(
            name="Hide Original",
            description="Hide the original spline curve",
            default=False
        )
        
        # Uniform Scale
        props_owner.spline_uniform_scale = bpy.props.BoolProperty(
            name="Uniform Scale",
            description="Use uniform scaling for instances",
            default=True
        )
        
        # Collection Settings (для collection mode)
        props_owner.spline_collection_pick_instance = bpy.props.BoolProperty(
            name="Pick Instance",
            description="Pick specific instance from collection",
            default=False
        )
        props_owner.spline_collection_instance_index = bpy.props.IntProperty(
            name="Instance Index",
            description="Index of object in collection to use",
            default=0,
            min=0
        )
        props_owner.spline_collection_random_seed = bpy.props.IntProperty(
            name="Collection Random Seed",
            description="Seed for random collection object selection",
            default=0
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Spline Cloner
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Source Mode", "NodeSocketInt", "INPUT", 0),  # 0=Object, 1=Collection
            ("Source Object", "NodeSocketObject", "INPUT", None),
            ("Source Collection", "NodeSocketCollection", "INPUT", None),
            ("Instance Count", "NodeSocketInt", "INPUT", 10),
            ("Hide Original", "NodeSocketBool", "INPUT", False),
            ("Curve Start", "NodeSocketFloat", "INPUT", 0.0),  # 0-100% как в backup
            ("Curve End", "NodeSocketFloat", "INPUT", 100.0),  # 0-100% как в backup
            ("Curve Offset", "NodeSocketFloat", "INPUT", 0.0),
            ("Spacing Mode", "NodeSocketInt", "INPUT", 0),  # 0=Count, 1=Length
            ("Spacing Length", "NodeSocketFloat", "INPUT", 1.0),
            ("Align to Spline", "NodeSocketBool", "INPUT", True),
            ("Uniform Scale", "NodeSocketBool", "INPUT", True),
            ("Scale Start", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Scale End", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Rotation Start", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Rotation End", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Collection Pick Instance", "NodeSocketBool", "INPUT", True),
            ("Collection Instance Index", "NodeSocketInt", "INPUT", 0),
            ("Collection Object Count", "NodeSocketInt", "INPUT", 3),
            ("Collection Random Seed", "NodeSocketInt", "INPUT", 2),
        ]
    
    def create_node_group(self, mode="OBJECT", target_obj=None):
        """
        Создание node group для Spline Cloner
        
        Args:
            mode: Режим создания ("OBJECT", "STACKED", "COLLECTION")
            target_obj: Целевой объект (для совместимости с mesh-based системой)
            
        Returns:
            tuple: (node_group, socket_mapping)
        """
        try:
            # Создаем уникальное имя группы
            import time
            base_name = f"SplineCloner3D_{mode}"
            unique_suffix = str(int(time.time() * 1000000) % 1000000)
            group_name = f"{base_name}_{unique_suffix}"
            
            # Проверяем уникальность имени
            counter = 1
            original_name = group_name
            while group_name in bpy.data.node_groups:
                group_name = f"{original_name}_{counter}"
                counter += 1
            
            node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')
            
            # Создаем интерфейс (базовые + специфичные сокеты)
            specific_sockets = self.get_specific_sockets()
            socket_mapping = self.create_cloner_interface(node_group, specific_sockets)
            
            # ИСПРАВЛЕНИЕ: Устанавливаем правильные ограничения для Curve Start/End как в backup
            self._fix_curve_start_end_sockets(node_group)
            
            # Создаем базовые ноды
            base_nodes = self.create_base_nodes(node_group, mode)
            
            # Реализуем логику Spline Cloner
            final_geometry = self._create_spline_logic(base_nodes, mode)
            
            # Применяем анти-рекурсию - ВСЕГДА (Switch управляется сокетом)
            anti_recursion_result = self.apply_anti_recursion(base_nodes, final_geometry, use_anti_recursion=True)
            base_nodes['links'].new(anti_recursion_result, base_nodes['group_output'].inputs['Geometry'])
            
            print(f"✅ Created Spline Cloner node group: {group_name}")
            return node_group, socket_mapping
            
        except Exception as e:
            print(f"[ERROR] Spline Cloner node group creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def _create_spline_logic(self, base_nodes, mode):
        """
        ТОЧНАЯ КОПИЯ из backup spline.py 
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # ТОЧНО как в backup - копируем _build_node_tree полностью
        
        # Join Geometry для подготовки кривой (получаем из входной геометрии)
        join_geometry = nodes.new('GeometryNodeJoinGeometry')
        join_geometry.location = (-600, -300)
        join_geometry.name = "Join Spline Geometry"
        
        # Curve Offset Math - конвертируем в factor для смещения Start/End
        curve_offset_to_factor = nodes.new('ShaderNodeMath')
        curve_offset_to_factor.location = (-550, -400)
        curve_offset_to_factor.name = "Curve Offset to Factor"
        curve_offset_to_factor.operation = 'DIVIDE'
        curve_offset_to_factor.inputs[1].default_value = 10.0
        
        # Trim Curve для Curve Start/End
        trim_curve = nodes.new('GeometryNodeTrimCurve')
        trim_curve.location = (-400, -300)
        trim_curve.name = "Trim Curve Start/End"
        trim_curve.mode = 'FACTOR'
        
        # Конвертация процентов в факторы
        math_normalize_start = nodes.new('ShaderNodeMath')
        math_normalize_start.location = (-700, -200)
        math_normalize_start.name = "Convert Start Percentage"
        math_normalize_start.operation = 'DIVIDE'
        math_normalize_start.inputs[1].default_value = 100.0
        math_normalize_start.use_clamp = True
        
        math_normalize_end = nodes.new('ShaderNodeMath')
        math_normalize_end.location = (-700, -250)
        math_normalize_end.name = "Convert End Percentage"
        math_normalize_end.operation = 'DIVIDE'
        math_normalize_end.inputs[1].default_value = 100.0
        math_normalize_end.use_clamp = True
        
        # Применяем Curve Offset
        math_offset_start = nodes.new('ShaderNodeMath')
        math_offset_start.location = (-500, -200)
        math_offset_start.name = "Apply Offset to Start"
        math_offset_start.operation = 'ADD'
        math_offset_start.use_clamp = True
        
        math_offset_end = nodes.new('ShaderNodeMath')
        math_offset_end.location = (-500, -250)
        math_offset_end.name = "Apply Offset to End"
        math_offset_end.operation = 'ADD'
        math_offset_end.use_clamp = True
        
        # Валидация Start <= End
        math_ensure_start_le_end = nodes.new('ShaderNodeMath')
        math_ensure_start_le_end.location = (-400, -200)
        math_ensure_start_le_end.name = "Ensure Start <= End"
        math_ensure_start_le_end.operation = 'MINIMUM'
        math_ensure_start_le_end.use_clamp = True
        
        math_ensure_end_ge_start = nodes.new('ShaderNodeMath')
        math_ensure_end_ge_start.location = (-400, -250)
        math_ensure_end_ge_start.name = "Ensure End >= Start"
        math_ensure_end_ge_start.operation = 'MAXIMUM'
        math_ensure_end_ge_start.use_clamp = True
        
        # Resample Curve
        resample_curve = nodes.new('GeometryNodeResampleCurve')
        resample_curve.location = (-450, -300)
        resample_curve.mode = 'COUNT'
        resample_curve.name = "Resample for Smooth Normals"
        resample_curve.inputs['Count'].default_value = 100
        
        # Curve to Points - COUNT
        curve_to_points_count = nodes.new('GeometryNodeCurveToPoints')
        curve_to_points_count.location = (-200, -250)
        curve_to_points_count.mode = 'COUNT'
        curve_to_points_count.name = "Curve to Points Count"
        
        # Curve to Points - LENGTH
        curve_to_points_length = nodes.new('GeometryNodeCurveToPoints')
        curve_to_points_length.location = (-200, -350)
        curve_to_points_length.mode = 'LENGTH'
        curve_to_points_length.name = "Curve to Points Length"
        
        # Switch для Spacing Mode
        spacing_mode_switch_points = nodes.new('GeometryNodeSwitch')
        spacing_mode_switch_points.location = (-50, -300)
        spacing_mode_switch_points.input_type = 'GEOMETRY'
        spacing_mode_switch_points.name = "Spacing Mode Points Switch"
        
        spacing_mode_switch_rotation = nodes.new('GeometryNodeSwitch')
        spacing_mode_switch_rotation.location = (-50, -400)
        spacing_mode_switch_rotation.input_type = 'VECTOR'
        spacing_mode_switch_rotation.name = "Spacing Mode Rotation Switch"
        
        spacing_mode_switch_normal = nodes.new('GeometryNodeSwitch')
        spacing_mode_switch_normal.location = (-250, -500)
        spacing_mode_switch_normal.input_type = 'VECTOR'
        spacing_mode_switch_normal.name = "Spacing Mode Normal Switch"
        
        spacing_mode_switch_tangent = nodes.new('GeometryNodeSwitch')
        spacing_mode_switch_tangent.location = (-250, -600)
        spacing_mode_switch_tangent.input_type = 'VECTOR'
        spacing_mode_switch_tangent.name = "Spacing Mode Tangent Switch"
        
        # Align Switch
        align_switch = nodes.new('GeometryNodeSwitch')
        align_switch.location = (-200, -200)
        align_switch.input_type = 'VECTOR'
        align_switch.name = "Align Switch"
        align_switch.inputs['False'].default_value = (0.0, 0.0, 0.0)
        
        # Source Object Info
        source_object_info = nodes.new('GeometryNodeObjectInfo')
        source_object_info.location = (-600, 100)
        source_object_info.name = "Source Object Info"
        source_object_info.transform_space = 'ORIGINAL'
        source_object_info.inputs['As Instance'].default_value = True
        
        # Collection Info
        collection_info = nodes.new('GeometryNodeCollectionInfo')
        collection_info.location = (-600, -50)
        collection_info.name = "Collection Info"
        collection_info.transform_space = 'ORIGINAL'
        collection_info.inputs['Separate Children'].default_value = True
        collection_info.inputs['Reset Children'].default_value = True
        
        # Source Mode Switch
        source_switch = nodes.new('GeometryNodeSwitch')
        source_switch.location = (-400, 50)
        source_switch.input_type = 'GEOMETRY'
        source_switch.name = "Source Mode Switch"
        
        # Instance on Points
        instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
        instance_on_points.location = (0, 0)
        instance_on_points.name = "Instance on Points"
        instance_on_points.inputs['Selection'].default_value = True
        
        # Gradient interpolation nodes
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (-100, -450)
        index_node.name = "Instance Index"
        
        domain_size = nodes.new('GeometryNodeAttributeDomainSize')
        domain_size.location = (-150, -500)
        domain_size.name = "Get Point Count"
        domain_size.component = 'POINTCLOUD'
        
        math_count_minus_one = nodes.new('ShaderNodeMath')
        math_count_minus_one.location = (-50, -500)
        math_count_minus_one.name = "Count Minus One"
        math_count_minus_one.operation = 'SUBTRACT'
        math_count_minus_one.inputs[1].default_value = 1.0
        
        math_interpolation_factor = nodes.new('ShaderNodeMath')
        math_interpolation_factor.location = (0, -450)
        math_interpolation_factor.name = "Interpolation Factor"
        math_interpolation_factor.operation = 'DIVIDE'
        math_interpolation_factor.use_clamp = True
        
        # Mix nodes for gradient
        mix_scale = nodes.new('ShaderNodeMix')
        mix_scale.location = (100, -400)
        mix_scale.name = "Mix Scale Start/End"
        mix_scale.data_type = 'VECTOR'
        mix_scale.clamp_factor = True
        
        mix_rotation = nodes.new('ShaderNodeMix')
        mix_rotation.location = (100, -500)
        mix_rotation.name = "Mix Rotation Start/End"
        mix_rotation.data_type = 'VECTOR'
        mix_rotation.clamp_factor = True
        
        # Transform nodes
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.location = (200, 0)
        rotate_instances.name = "Rotate Instances"
        rotate_instances.inputs['Selection'].default_value = True
        rotate_instances.inputs['Pivot Point'].default_value = (0.0, 0.0, 0.0)
        rotate_instances.inputs['Local Space'].default_value = True
        
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.location = (400, 0)
        scale_instances.name = "Scale Instances"
        scale_instances.inputs['Selection'].default_value = True
        
        # Store Named Attribute для цвета
        store_named_attribute = nodes.new('GeometryNodeStoreNamedAttribute')
        store_named_attribute.location = (600, 0)
        store_named_attribute.name = "Store Named Attribute"
        store_named_attribute.data_type = 'FLOAT_COLOR'
        store_named_attribute.domain = 'INSTANCE'
        store_named_attribute.inputs['Selection'].default_value = True
        store_named_attribute.inputs['Name'].default_value = "col"
        store_named_attribute.inputs['Value'].default_value = (1.0, 1.0, 1.0, 1.0)
        
        # Switch для Hide Original
        hide_original_switch = nodes.new('GeometryNodeSwitch')
        hide_original_switch.location = (700, -100)
        hide_original_switch.input_type = 'GEOMETRY'
        hide_original_switch.name = "Hide Original Switch"
        
        # Join Geometry для объединения оригинала с экземплярами
        final_join = nodes.new('GeometryNodeJoinGeometry')
        final_join.location = (750, 0)
        final_join.name = "Final Join Geometry"
        
        # Random Value для коллекций
        random_value = nodes.new('FunctionNodeRandomValue')
        random_value.location = (-100, -400)
        random_value.name = "Collection Random Value"
        random_value.data_type = 'INT'
        random_value.inputs['Min'].default_value = 0
        random_value.inputs['ID'].default_value = 0
        
        # Translate Instances для Random Position
        translate_instances = nodes.new('GeometryNodeTranslateInstances')
        translate_instances.location = (450, 100)
        translate_instances.name = "Translate Instances"
        translate_instances.inputs['Selection'].default_value = True
        translate_instances.inputs['Local Space'].default_value = True
        
        # Random Value для Random Position
        random_position = nodes.new('FunctionNodeRandomValue')
        random_position.location = (250, -400)
        random_position.name = "Random Position Value"
        random_position.data_type = 'FLOAT_VECTOR'
        random_position.inputs['Min'].default_value = (0.0, 0.0, 0.0)
        random_position.inputs['ID'].default_value = 0
        
        # Random Value для Random Rotation 
        random_rotation = nodes.new('FunctionNodeRandomValue')
        random_rotation.location = (100, -500)
        random_rotation.name = "Random Rotation Value"
        random_rotation.data_type = 'FLOAT_VECTOR'
        random_rotation.inputs['Min'].default_value = (0.0, 0.0, 0.0)
        random_rotation.inputs['ID'].default_value = 0
        
        # Random Value для Random Scale
        random_scale_value = nodes.new('FunctionNodeRandomValue')
        random_scale_value.location = (50, -600)
        random_scale_value.name = "Random Scale Value"
        random_scale_value.data_type = 'FLOAT'
        random_scale_value.inputs['Min'].default_value = 1.0
        random_scale_value.inputs['ID'].default_value = 0
        
        # Vector Math для комбинирования Instance Rotation + Random Rotation
        vector_add_rotation = nodes.new('ShaderNodeVectorMath')
        vector_add_rotation.location = (50, -300)
        vector_add_rotation.name = "Add Rotations"
        vector_add_rotation.operation = 'ADD'
        
        # Separate/Combine для Uniform Scale
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-200, 400)
        separate_xyz.name = "Separate Scale XYZ"
        
        combine_xyz = nodes.new('ShaderNodeCombineXYZ')
        combine_xyz.location = (0, 400)
        combine_xyz.name = "Combine Uniform Scale"
        
        # Switch для Uniform Scale
        uniform_scale_switch = nodes.new('GeometryNodeSwitch')
        uniform_scale_switch.location = (150, 400)
        uniform_scale_switch.input_type = 'VECTOR'
        uniform_scale_switch.name = "Uniform Scale Switch"
        
        # Random Value для Random Scale
        random_scale_factor = nodes.new('FunctionNodeRandomValue')
        random_scale_factor.location = (200, -700)
        random_scale_factor.name = "Random Scale Factor"
        random_scale_factor.data_type = 'FLOAT'
        random_scale_factor.inputs['Min'].default_value = 1.0
        
        # Vector Math для применения Random Scale
        vector_scale_random = nodes.new('ShaderNodeVectorMath')
        vector_scale_random.location = (350, 300)
        vector_scale_random.name = "Apply Random Scale"
        vector_scale_random.operation = 'MULTIPLY'
        
        # Vector Math для комбинирования Instance Rotation + Gradient Rotation
        vector_combine_instance_gradient = nodes.new('ShaderNodeVectorMath')
        vector_combine_instance_gradient.location = (200, -500)
        vector_combine_instance_gradient.name = "Combine Instance + Gradient Rotation"
        vector_combine_instance_gradient.operation = 'ADD'
        
        # ПОДКЛЮЧЕНИЯ как в backup
        
        # 1. Входная геометрия → Join → Resample → Trim → Curve to Points
        links.new(group_input.outputs['Geometry'], join_geometry.inputs['Geometry'])
        links.new(join_geometry.outputs['Geometry'], resample_curve.inputs['Curve'])
        links.new(resample_curve.outputs['Curve'], trim_curve.inputs['Curve'])
        
        # Curve offset logic
        links.new(group_input.outputs['Curve Offset'], curve_offset_to_factor.inputs[0])
        links.new(group_input.outputs['Curve Start'], math_normalize_start.inputs[0])
        links.new(group_input.outputs['Curve End'], math_normalize_end.inputs[0])
        
        links.new(math_normalize_start.outputs['Value'], math_offset_start.inputs[0])
        links.new(curve_offset_to_factor.outputs['Value'], math_offset_start.inputs[1])
        links.new(math_normalize_end.outputs['Value'], math_offset_end.inputs[0])
        links.new(curve_offset_to_factor.outputs['Value'], math_offset_end.inputs[1])
        
        links.new(math_offset_start.outputs['Value'], math_ensure_start_le_end.inputs[0])
        links.new(math_offset_end.outputs['Value'], math_ensure_start_le_end.inputs[1])
        links.new(math_offset_end.outputs['Value'], math_ensure_end_ge_start.inputs[0])
        links.new(math_ensure_start_le_end.outputs['Value'], math_ensure_end_ge_start.inputs[1])
        
        links.new(math_ensure_start_le_end.outputs['Value'], trim_curve.inputs['Start'])
        links.new(math_ensure_end_ge_start.outputs['Value'], trim_curve.inputs['End'])
        
        # Curve to Points
        links.new(trim_curve.outputs['Curve'], curve_to_points_count.inputs['Curve'])
        links.new(group_input.outputs['Instance Count'], curve_to_points_count.inputs['Count'])
        links.new(trim_curve.outputs['Curve'], curve_to_points_length.inputs['Curve'])
        links.new(group_input.outputs['Spacing Length'], curve_to_points_length.inputs['Length'])
        
        # Spacing mode switches
        links.new(group_input.outputs['Spacing Mode'], spacing_mode_switch_points.inputs['Switch'])
        links.new(curve_to_points_count.outputs['Points'], spacing_mode_switch_points.inputs['False'])
        links.new(curve_to_points_length.outputs['Points'], spacing_mode_switch_points.inputs['True'])
        
        links.new(group_input.outputs['Spacing Mode'], spacing_mode_switch_rotation.inputs['Switch'])
        links.new(curve_to_points_count.outputs['Rotation'], spacing_mode_switch_rotation.inputs['False'])
        links.new(curve_to_points_length.outputs['Rotation'], spacing_mode_switch_rotation.inputs['True'])
        
        links.new(group_input.outputs['Spacing Mode'], spacing_mode_switch_normal.inputs['Switch'])
        links.new(curve_to_points_count.outputs['Normal'], spacing_mode_switch_normal.inputs['False'])
        links.new(curve_to_points_length.outputs['Normal'], spacing_mode_switch_normal.inputs['True'])
        
        links.new(group_input.outputs['Spacing Mode'], spacing_mode_switch_tangent.inputs['Switch'])
        links.new(curve_to_points_count.outputs['Tangent'], spacing_mode_switch_tangent.inputs['False'])
        links.new(curve_to_points_length.outputs['Tangent'], spacing_mode_switch_tangent.inputs['True'])
        
        # Align switch
        links.new(group_input.outputs['Align to Spline'], align_switch.inputs['Switch'])
        links.new(spacing_mode_switch_rotation.outputs['Output'], align_switch.inputs['True'])
        
        # Source mode
        links.new(group_input.outputs['Source Object'], source_object_info.inputs['Object'])
        links.new(group_input.outputs['Source Collection'], collection_info.inputs['Collection'])
        links.new(source_object_info.outputs['Geometry'], source_switch.inputs['False'])
        links.new(collection_info.outputs['Instances'], source_switch.inputs['True'])
        links.new(group_input.outputs['Source Mode'], source_switch.inputs['Switch'])
        
        # Collection randomization
        links.new(group_input.outputs['Collection Object Count'], random_value.inputs['Max'])
        links.new(group_input.outputs['Collection Random Seed'], random_value.inputs['Seed'])
        
        # ПРАВИЛЬНОЕ ПОДКЛЮЧЕНИЕ UNIFORM SCALE К INSTANCE ON POINTS
        # Сначала подключаем Uniform Scale Switch к Instance on Points
        links.new(spacing_mode_switch_points.outputs['Output'], instance_on_points.inputs['Points'])
        links.new(source_switch.outputs['Output'], instance_on_points.inputs['Instance'])
        links.new(align_switch.outputs['Output'], instance_on_points.inputs['Rotation'])
        links.new(uniform_scale_switch.outputs['Output'], instance_on_points.inputs['Scale'])  # ← ИСПРАВЛЕНО
        
        # Collection Pick Instance logic
        links.new(group_input.outputs['Collection Pick Instance'], instance_on_points.inputs['Pick Instance'])
        links.new(random_value.outputs['Value'], instance_on_points.inputs['Instance Index'])
        
        # Instance Transforms + Random Position
        links.new(instance_on_points.outputs['Instances'], translate_instances.inputs['Instances'])
        
        # Random Position
        links.new(group_input.outputs['Random Position'], random_position.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_position.inputs['Seed'])
        
        # Gradient interpolation
        links.new(spacing_mode_switch_points.outputs['Output'], domain_size.inputs['Geometry'])
        links.new(domain_size.outputs['Point Count'], math_count_minus_one.inputs[0])
        
        links.new(index_node.outputs['Index'], math_interpolation_factor.inputs[0])
        links.new(math_count_minus_one.outputs['Value'], math_interpolation_factor.inputs[1])
        
        # Mix Scale: Start → End (gradient)
        links.new(group_input.outputs['Scale Start'], mix_scale.inputs['A'])
        links.new(group_input.outputs['Scale End'], mix_scale.inputs['B'])
        links.new(math_interpolation_factor.outputs['Value'], mix_scale.inputs['Factor'])
        
        # Mix Rotation: Start → End (gradient)
        links.new(group_input.outputs['Rotation Start'], mix_rotation.inputs['A'])
        links.new(group_input.outputs['Rotation End'], mix_rotation.inputs['B'])
        links.new(math_interpolation_factor.outputs['Value'], mix_rotation.inputs['Factor'])
        
        # КОМБИНИРУЕМ INSTANCE SCALE + GRADIENT SCALE как в backup
        # Instance Scale * Gradient Scale = финальный масштаб
        vector_combine_instance_gradient_scale = nodes.new('ShaderNodeVectorMath')
        vector_combine_instance_gradient_scale.location = (200, -400)
        vector_combine_instance_gradient_scale.name = "Combine Instance + Gradient Scale"
        vector_combine_instance_gradient_scale.operation = 'MULTIPLY'
        
        # Instance Scale * mix_scale (gradient) 
        links.new(group_input.outputs['Instance Scale'], vector_combine_instance_gradient_scale.inputs[0])
        links.new(mix_scale.outputs['Result'], vector_combine_instance_gradient_scale.inputs[1])
        
        # UNIFORM SCALE LOGIC - обрабатываем комбинированный scale через uniform/non-uniform switch
        # Combined scale → Separate XYZ → Uniform Logic → Switch
        links.new(vector_combine_instance_gradient_scale.outputs['Vector'], separate_xyz.inputs['Vector'])
        links.new(separate_xyz.outputs['X'], combine_xyz.inputs['X'])
        links.new(separate_xyz.outputs['X'], combine_xyz.inputs['Y'])  # Uniform - используем X для всех осей
        links.new(separate_xyz.outputs['X'], combine_xyz.inputs['Z'])
        
        # Switch между комбинированным scale (non-uniform) и uniform scale
        links.new(group_input.outputs['Uniform Scale'], uniform_scale_switch.inputs['Switch'])
        links.new(vector_combine_instance_gradient_scale.outputs['Vector'], uniform_scale_switch.inputs['False'])  # Обычный XYZ
        links.new(combine_xyz.outputs['Vector'], uniform_scale_switch.inputs['True'])  # Uniform
        
        # Random Position применяется как и раньше
        links.new(random_position.outputs['Value'], translate_instances.inputs['Translation'])
        
        # Random Rotation
        links.new(group_input.outputs['Random Rotation'], random_rotation.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_rotation.inputs['Seed'])
        
        # Комбинируем Instance Rotation + Gradient Rotation
        links.new(group_input.outputs['Instance Rotation'], vector_combine_instance_gradient.inputs[0])
        links.new(mix_rotation.outputs['Result'], vector_combine_instance_gradient.inputs[1])
        
        # Комбинируем (Instance + Gradient) + Random Rotation
        links.new(vector_combine_instance_gradient.outputs['Vector'], vector_add_rotation.inputs[0])
        links.new(random_rotation.outputs['Value'], vector_add_rotation.inputs[1])
        
        # Instance Rotation (комбинированный)
        links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(vector_add_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])
        
        # UNIFORM SCALE LOGIC уже подключен выше - удаляем дублирующие подключения
        
        # Random Scale - создаем диапазон 1.0 ± Random Scale
        math_subtract = nodes.new('ShaderNodeMath')
        math_subtract.location = (150, -700)
        math_subtract.name = "Random Scale Min"
        math_subtract.operation = 'SUBTRACT'
        math_subtract.inputs[0].default_value = 1.0
        
        math_add = nodes.new('ShaderNodeMath')
        math_add.location = (300, -700)
        math_add.name = "Random Scale Max"
        math_add.operation = 'ADD'
        math_add.inputs[0].default_value = 1.0
        
        # Random Scale range: 1.0 - Random Scale до 1.0 + Random Scale
        links.new(group_input.outputs['Random Scale'], math_subtract.inputs[1])
        links.new(group_input.outputs['Random Scale'], math_add.inputs[1])
        links.new(math_subtract.outputs['Value'], random_scale_factor.inputs['Min'])
        links.new(math_add.outputs['Value'], random_scale_factor.inputs['Max'])
        links.new(group_input.outputs['Random Seed'], random_scale_factor.inputs['Seed'])
        
        # Применяем Random Scale к базовому масштабу
        links.new(uniform_scale_switch.outputs['Output'], vector_scale_random.inputs[0])
        links.new(random_scale_factor.outputs['Value'], vector_scale_random.inputs[1])
        
        # Instance Scale с учетом всех модификаций
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(vector_scale_random.outputs['Vector'], scale_instances.inputs['Scale'])
        
        # GLOBAL TRANSFORMS - применяются в самом конце как в BaseCloner
        
        # Global Position
        global_translate = nodes.new('GeometryNodeTranslateInstances')
        global_translate.location = (650, 0)
        global_translate.name = "Global Position"
        global_translate.inputs['Selection'].default_value = True
        global_translate.inputs['Local Space'].default_value = False  # Global space
        
        links.new(scale_instances.outputs['Instances'], global_translate.inputs['Instances'])
        links.new(group_input.outputs['Global Position'], global_translate.inputs['Translation'])
        
        # Global Rotation  
        global_rotate = nodes.new('GeometryNodeRotateInstances')
        global_rotate.location = (800, 0)
        global_rotate.name = "Global Rotation"
        global_rotate.inputs['Selection'].default_value = True
        global_rotate.inputs['Local Space'].default_value = False  # Global space
        global_rotate.inputs['Pivot Point'].default_value = (0.0, 0.0, 0.0)
        
        links.new(global_translate.outputs['Instances'], global_rotate.inputs['Instances'])
        links.new(group_input.outputs['Global Rotation'], global_rotate.inputs['Rotation'])
        
        # Store Attribute
        links.new(global_rotate.outputs['Instances'], store_named_attribute.inputs['Geometry'])
        
        # Hide Original Logic
        links.new(group_input.outputs['Hide Original'], hide_original_switch.inputs['Switch'])
        links.new(group_input.outputs['Geometry'], hide_original_switch.inputs['False'])
        
        # Final Join - объединяем оригинальную геометрию с экземплярами
        links.new(hide_original_switch.outputs['Output'], final_join.inputs['Geometry'])
        links.new(store_named_attribute.outputs['Geometry'], final_join.inputs['Geometry'])
        
        # Final Output
        links.new(final_join.outputs['Geometry'], base_nodes['group_output'].inputs['Geometry'])
        
        return final_join.outputs['Geometry']
    
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Spline Cloner
        
        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        # Source Settings
        box = layout.box()
        box.label(text="Source Settings", icon='CURVE_DATA')
        
        col = box.column(align=True)
        col.prop(props_owner, "spline_source_mode", text="Mode")
        
        # Hide Original из модификатора (как в backup)
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == 'Hide Original':
                        col.separator()
                        col.prop(modifier, f'["{socket.identifier}"]', text="Hide Original Spline")
                        break
        
        # Spline Distribution
        box = layout.box()
        box.label(text="Spline Distribution", icon='CURVE_PATH')
        
        col = box.column(align=True)
        col.prop(props_owner, "spline_instance_count", text="Count")
        
        # Spacing Mode buttons (как в backup)
        row = col.row(align=True)
        row.label(text="Spacing:")
        spacing_row = col.row(align=True)
        spacing_row.prop_enum(props_owner, "spline_spacing_mode", 'COUNT', text="Count")
        spacing_row.prop_enum(props_owner, "spline_spacing_mode", 'LENGTH', text="Length")
        
        if props_owner.spline_spacing_mode == 'LENGTH':
            col.prop(props_owner, "spline_spacing_length", text="Length")
        
        col.separator()
        col.prop(props_owner, "spline_curve_start", text="Start")
        col.prop(props_owner, "spline_curve_end", text="End") 
        col.prop(props_owner, "spline_curve_offset", text="Offset")
        col.prop(props_owner, "spline_align_to_spline", text="Align to Spline")
        
        # Gradient Settings
        box = layout.box()
        box.label(text="Gradient", icon='IPO_EASE_IN_OUT')
        
        col = box.column(align=True)
        col.label(text="Scale:")
        col.prop(props_owner, "spline_scale_start", text="Start")
        col.prop(props_owner, "spline_scale_end", text="End")
        
        col.separator()
        col.label(text="Rotation:")
        col.prop(props_owner, "spline_rotation_start", text="Start")
        col.prop(props_owner, "spline_rotation_end", text="End")
        
        # Collection Settings (если в collection mode)
        if props_owner.spline_source_mode == 'COLLECTION':
            box = layout.box()
            box.label(text="Collection Settings", icon='OUTLINER_COLLECTION')
            
            col = box.column(align=True)
            col.prop(props_owner, "spline_collection_pick_instance", text="Pick Instance")
            if props_owner.spline_collection_pick_instance:
                col.prop(props_owner, "spline_collection_instance_index", text="Index")
            else:
                col.prop(props_owner, "spline_collection_random_seed", text="Random Seed")
        
        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, modifier)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        
        # Instance Transform
        box = layout.box()
        box.label(text="Instance Transform", icon='CON_TRANSFORM')
        
        col = box.column(align=True)
        
        # Uniform Scale из модификатора (как в backup)
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == 'Uniform Scale':
                        col.prop(modifier, f'["{socket.identifier}"]', text="Uniform Scale")
                        col.separator()
                        break
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name in ['Instance Scale', 'Instance Rotation']:
                        col.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Randomization
        box = layout.box()
        box.label(text="Randomization", icon='RNDCURVE')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Random') or socket.name == 'Random Seed':
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Global Transform
        box = layout.box()
        box.label(text="Global Transform", icon='OBJECT_ORIGIN')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Global'):
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Anti-recursion
        box = layout.box()
        box.label(text="Anti-Recursion", icon='CON_TRACKTO')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name == 'Realize Instances':
                        box.prop(modifier, f'["{socket.identifier}"]', text="Realize Instances (Anti-Recursion)")
                        break
    
    def get_default_parameters(self, mode="OBJECT"):
        """Параметры по умолчанию для Spline Cloner"""
        base_defaults = super().get_default_parameters()
        
        # Source Mode зависит от режима создания
        source_mode = 1 if mode == "COLLECTION" else 0
        
        spline_defaults = {
            "source_mode": source_mode,  # 0=Object, 1=Collection (зависит от mode)
            "instance_count": 10,
            "hide_original": False,
            "curve_start": 0.0,   # 0% как в backup
            "curve_end": 100.0,   # 100% как в backup
            "curve_offset": 0.0,
            "spacing_mode": 0,  # 0=Count, 1=Length (Int, не строка)
            "spacing_length": 1.0,
            "align_to_spline": True,
            "uniform_scale": True,  # Uniform Scale по умолчанию включен
            "scale_start": (1.0, 1.0, 1.0),
            "scale_end": (1.0, 1.0, 1.0),
            "rotation_start": (0.0, 0.0, 0.0),
            "rotation_end": (0.0, 0.0, 0.0),
            "collection_pick_instance": True,
            "collection_instance_index": 0,
            "collection_object_count": 3,
            "collection_random_seed": 2,
            # Global Transform параметры
            "global_position": (0.0, 0.0, 0.0),
            "global_rotation": (0.0, 0.0, 0.0),
        }
        return {**base_defaults, **spline_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        spline_groups = {
            "Source Settings": ["Source Mode", "Source Object", "Source Collection"],
            "Spline Distribution": ["Instance Count", "Curve Start", "Curve End", "Curve Offset", "Spacing Mode", "Spacing Length", "Align to Spline"],
            "Gradient": ["Scale Start", "Scale End", "Rotation Start", "Rotation End"],
            "Collection Settings": ["Collection Pick Instance", "Collection Instance Index", "Collection Random Seed"]
        }
        return {**spline_groups, **base_groups}
    
    def _fix_curve_start_end_sockets(self, node_group):
        """Устанавливает правильные ограничения для Curve Start/End как в backup"""
        for item in node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT':
                if item.name == 'Curve Start':
                    item.min_value = 0.0
                    item.max_value = 100.0
                    item.subtype = 'PERCENTAGE'
                    item.default_value = 0.0
                elif item.name == 'Curve End':
                    item.min_value = 0.0
                    item.max_value = 100.0
                    item.subtype = 'PERCENTAGE'
                    item.default_value = 100.0
    
    def setup_default_values(self, modifier, socket_mapping, mode="OBJECT"):
        """Устанавливает значения по умолчанию для модификатора"""
        defaults = self.get_default_parameters(mode)
        
        for param_name, default_value in defaults.items():
            # Конвертируем имя параметра в socket name
            socket_name = self._param_to_socket_name(param_name)
            
            # Ищем socket в mapping - сравниваем с ключом socket_mapping
            socket_key = socket_name.lower().replace(" ", "_")
            if socket_key in socket_mapping:
                socket_id = socket_mapping[socket_key]
                try:
                    modifier[socket_id] = default_value
                except Exception as e:
                    print(f"[ERROR] Failed to set {socket_name}: {e}")
    
    def _param_to_socket_name(self, param_name):
        """Конвертирует имя параметра в имя сокета"""
        # Маппинг параметров к именам сокетов
        param_mapping = {
            "source_mode": "Source Mode",
            "instance_count": "Instance Count", 
            "hide_original": "Hide Original",
            "curve_start": "Curve Start",
            "curve_end": "Curve End",
            "curve_offset": "Curve Offset",
            "spacing_mode": "Spacing Mode",
            "spacing_length": "Spacing Length",
            "align_to_spline": "Align to Spline",
            "uniform_scale": "Uniform Scale",
            "scale_start": "Scale Start",
            "scale_end": "Scale End", 
            "rotation_start": "Rotation Start",
            "rotation_end": "Rotation End",
            "instance_scale": "Instance Scale",
            "instance_rotation": "Instance Rotation",
            "random_position": "Random Position",
            "random_rotation": "Random Rotation",
            "random_scale": "Random Scale",
            "random_seed": "Random Seed",
            "collection_pick_instance": "Collection Pick Instance",
            "collection_instance_index": "Collection Instance Index", 
            "collection_object_count": "Collection Object Count",
            "collection_random_seed": "Collection Random Seed",
            "global_position": "Global Position",
            "global_rotation": "Global Rotation"
        }
        return param_mapping.get(param_name, param_name.title().replace('_', ' '))


# Экземпляр класса для использования в других модулях
spline_cloner = SplineCloner()


# ===== BRIDGE ФУНКЦИИ ДЛЯ MESH-BASED СИСТЕМЫ =====

def create_spline_cloner_stacked_mode_class(target_obj, **kwargs):
    """Bridge функция для STACKED режима - использует классовую архитектуру с mesh-based системой"""
    print(f"🔧 [BRIDGE] Creating Spline Cloner STACKED via class system")
    
    import bpy
    cloner_instance = spline_cloner
    
    # Создаем node group через классовый метод с target_obj
    node_group, socket_mapping = cloner_instance.create_node_group(mode="STACKED", target_obj=target_obj)
    if not node_group:
        print(f"❌ [BRIDGE] Failed to create Spline Cloner node group")
        return False
    
    # Добавляем модификатор прямо на target объект (как в STACKED mode)
    modifier = target_obj.modifiers.new(name="SplineCloner", type='NODES')
    modifier.node_group = node_group
    
    # Устанавливаем метаданные с UUID поддержкой
    from ...core.core import set_cloner_metadata
    set_cloner_metadata(
        modifier,
        "SPLINE",
        "STACKED",
        source_type="OBJECT",
        use_uuid=True
    )
    
    # Устанавливаем дефолтные параметры для STACKED режима
    cloner_instance.setup_default_values(modifier, socket_mapping, mode="STACKED")
    
    print(f"✅ [BRIDGE] Created Spline Cloner STACKED modifier: {modifier.name}")
    return True


def create_spline_cloner_collection_mode_class(collection_name, **kwargs):
    """Bridge функция для COLLECTION режима - использует классовую архитектуру с mesh-based системой"""
    print(f"🔧 [BRIDGE] Creating Spline Cloner COLLECTION via class system")
    
    import bpy
    cloner_instance = spline_cloner
    
    # Получаем активный объект как target (сплайн для клонирования)
    target_obj = bpy.context.active_object
    if not target_obj:
        print(f"⚠️ [BRIDGE] No active object for Spline Cloner COLLECTION mode")
        return False
    
    # Создаем node group через классовый метод с target_obj
    node_group, socket_mapping = cloner_instance.create_node_group(mode="COLLECTION", target_obj=target_obj)
    if not node_group:
        print(f"❌ [BRIDGE] Failed to create Spline Cloner node group")
        return False
    
    # Добавляем модификатор на target объект
    modifier = target_obj.modifiers.new(name="SplineCollectionCloner", type='NODES')
    modifier.node_group = node_group
    
    # Устанавливаем метаданные для COLLECTION режима с UUID поддержкой
    from ...core.core import set_cloner_metadata
    set_cloner_metadata(
        modifier,
        "SPLINE",
        "COLLECTION",
        source_type="COLLECTION",
        use_uuid=True
    )
    
    # Устанавливаем дефолтные параметры для COLLECTION режима
    cloner_instance.setup_default_values(modifier, socket_mapping, mode="COLLECTION")
    
    print(f"✅ [BRIDGE] Created Spline Cloner COLLECTION modifier: {modifier.name}")
    return True



# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Spline Cloner (в новой архитектуре не требуется)"""
    print("✅ Spline Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Spline Cloner (в новой архитектуре не требуется)"""
    print("✅ Spline Cloner: Using class-based architecture, no unregistration needed") 
    pass