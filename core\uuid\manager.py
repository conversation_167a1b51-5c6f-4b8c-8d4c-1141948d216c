"""
Blender UUID Manager for ClonerPro
Provides UUID-based identification for cloners using Blender Custom Properties
"""

import uuid
import time
import bpy
from typing import Dict, List, Optional, Tuple

class BlenderClonerUUIDManager:
    """UUID менеджер для Blender, использующий Custom Properties"""
    
    @staticmethod
    def generate_cloner_uuid() -> str:
        """Генерация UUID для клонера"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_chain_uuid() -> str:
        """Генерация UUID для цепочки клонеров"""
        return str(uuid.uuid4())
    
    @staticmethod
    def set_cloner_uuid_metadata(modifier, cloner_type: str, mode: str, **kwargs):
        """
        Установка UUID метаданных в Custom Properties модификатора
        
        Args:
            modifier: Blender modifier object
            cloner_type: Тип клонера (GRID, LINEAR, etc.)
            mode: Режим клонера (OBJECT, STACKED, COLLECTION)
            **kwargs: Дополнительные параметры
        """
        
        # Генерируем UUID если его нет
        if not modifier.get("cloner_uuid"):
            modifier["cloner_uuid"] = BlenderClonerUUIDManager.generate_cloner_uuid()
            print(f"✓ Generated new cloner UUID: {modifier['cloner_uuid']}")
        
        # Устанавливаем chain_uuid
        chain_uuid = kwargs.get("chain_uuid")
        if not chain_uuid:
            if kwargs.get("is_chain_start", False):
                chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
                print(f"✓ Generated new chain UUID: {chain_uuid}")
            elif kwargs.get("inherit_chain_from"):
                chain_uuid = kwargs["inherit_chain_from"]
                print(f"✓ Inherited chain UUID: {chain_uuid}")
        
        if chain_uuid:
            modifier["chain_uuid"] = chain_uuid
        
        # Основные метаданные
        modifier["cloner_type"] = cloner_type
        modifier["cloner_mode"] = mode
        modifier["creation_timestamp"] = time.time()
        
        # Legacy compatibility fields (необходимы для create_cloner_collection)
        modifier["original_object"] = kwargs.get("original_object", "")
        modifier["target_object"] = kwargs.get("target_object", "")
        modifier["cloner_collection"] = kwargs.get("cloner_collection", "")
        modifier["source_type"] = kwargs.get("source_type", "OBJECT")
        
        # UUID связи для цепочек
        modifier["previous_cloner_uuid"] = kwargs.get("previous_cloner_uuid", "")
        modifier["next_cloner_uuids"] = kwargs.get("next_cloner_uuids", "")
        
        # Пользовательские метаданные
        modifier["display_name"] = kwargs.get("display_name", f"{cloner_type} Cloner")
        modifier["user_notes"] = kwargs.get("user_notes", "")
        
        # Blender-specific данные для дополнительной верификации
        modifier["blender_session_uid"] = getattr(modifier, 'session_uid', 0)
        
        # Persistent UID если доступен (Blender 4.2+)
        if hasattr(modifier, 'persistent_uid') and modifier.persistent_uid:
            modifier["blender_persistent_uid"] = modifier.persistent_uid
        
        # Fingerprint для дополнительной идентификации
        modifier["object_fingerprint"] = BlenderClonerUUIDManager._generate_blender_fingerprint(modifier)
        
        print(f"✓ UUID Metadata set for {cloner_type} cloner: {modifier['cloner_uuid']}")
    
    @staticmethod
    def _generate_blender_fingerprint(modifier) -> str:
        """Генерация fingerprint для Blender объекта"""
        try:
            # Получаем объект-владелец модификатора
            owner_obj = None
            for obj in bpy.data.objects:
                if modifier in obj.modifiers:
                    owner_obj = obj
                    break
            
            if not owner_obj:
                return "unknown"
            
            # Создаем fingerprint на основе Blender-specific свойств
            fingerprint_data = [
                str(owner_obj.data.name if owner_obj.data else ""),
                str(modifier.node_group.name if hasattr(modifier, 'node_group') and modifier.node_group else ""),
                str(len(owner_obj.modifiers)),
                str(getattr(modifier, 'session_uid', 0))
            ]
            
            return str(hash(tuple(fingerprint_data)))
            
        except Exception as e:
            print(f"Warning: Could not generate Blender fingerprint: {e}")
            return "error"
    
    @staticmethod
    def find_cloner_by_uuid(cloner_uuid: str):
        """Найти клонер по UUID в Blender сцене"""
        for obj in bpy.data.objects:
            for modifier in obj.modifiers:
                if (modifier.type == 'NODES' and 
                    hasattr(modifier, 'node_group') and
                    modifier.node_group and
                    modifier.get("cloner_uuid") == cloner_uuid):
                    return obj, modifier
        return None, None
    
    @staticmethod
    def find_cloners_in_chain_by_uuid(chain_uuid: str) -> List[Tuple]:
        """Найти всех клонеров в цепочке по chain_uuid"""
        chain_cloners = []
        
        for obj in bpy.data.objects:
            for modifier in obj.modifiers:
                if (modifier.type == 'NODES' and 
                    hasattr(modifier, 'node_group') and
                    modifier.node_group and
                    modifier.get("chain_uuid") == chain_uuid):
                    chain_cloners.append((obj, modifier))
        
        # Сортируем по chain_sequence
        chain_cloners.sort(key=lambda x: x[1].get("chain_sequence", 0))
        
        return chain_cloners
    
    @staticmethod
    def migrate_legacy_cloner(obj, modifier):
        """Миграция старого клонера на UUID систему"""
        print(f"🔄 Migrating legacy Blender cloner: {obj.name}")
        
        # Сохраняем старые метаданные
        old_metadata = {
            "cloner_type": modifier.get("cloner_type", "UNKNOWN"),
            "cloner_mode": modifier.get("cloner_mode", "OBJECT"),
            "original_object": modifier.get("original_object", ""),
            "previous_cloner": modifier.get("previous_cloner_object", ""),
            "chain_source_object": modifier.get("chain_source_object", "")
        }
        
        # Генерируем UUID
        cloner_uuid = BlenderClonerUUIDManager.generate_cloner_uuid()
        modifier["cloner_uuid"] = cloner_uuid
        
        # Определяем chain_uuid
        chain_uuid = None
        if old_metadata["chain_source_object"]:
            # Пытаемся найти chain_uuid от других клонеров с тем же источником
            for other_obj in bpy.data.objects:
                for other_modifier in other_obj.modifiers:
                    if (other_modifier.type == 'NODES' and
                        other_modifier.get("chain_source_object") == old_metadata["chain_source_object"] and
                        other_modifier.get("chain_uuid")):
                        chain_uuid = other_modifier["chain_uuid"]
                        break
                if chain_uuid:
                    break
        
        if not chain_uuid:
            chain_uuid = BlenderClonerUUIDManager.generate_chain_uuid()
        
        modifier["chain_uuid"] = chain_uuid
        
        # Устанавливаем остальные UUID метаданные
        BlenderClonerUUIDManager.set_cloner_uuid_metadata(
            modifier,
            old_metadata["cloner_type"],
            old_metadata["cloner_mode"],
            chain_uuid=chain_uuid,
            display_name=f"Migrated {old_metadata['cloner_type']} Cloner"
        )
        
        print(f"✅ Blender migration complete: {cloner_uuid}")
        return cloner_uuid
    
    @staticmethod
    def scan_all_cloners_with_uuid() -> Dict:
        """Сканирование всех клонеров в Blender сцене с UUID поддержкой"""
        result = {
            "cloners_by_uuid": {},
            "chains_by_uuid": {},
            "legacy_cloners": [],
            "session_uid_cloners": {},  # Blender-specific
            "orphaned_cloners": []
        }
        
        print("🔍 Blender UUID Scanner: Starting enhanced scan...")
        
        # Сканируем все объекты в bpy.data.objects
        for obj in bpy.data.objects:
            for modifier in obj.modifiers:
                # Проверяем, что это геометрические ноды
                if not (modifier.type == 'NODES' and 
                        hasattr(modifier, 'node_group') and
                        modifier.node_group):
                    continue
                
                # Проверяем, что это клонер
                if not BlenderClonerUUIDManager._is_cloner_modifier(modifier):
                    continue
                
                cloner_uuid = modifier.get("cloner_uuid")
                chain_uuid = modifier.get("chain_uuid")
                session_uid = getattr(modifier, 'session_uid', None)
                
                if cloner_uuid:
                    # UUID клонер
                    result["cloners_by_uuid"][cloner_uuid] = (obj, modifier)
                    
                    if chain_uuid:
                        if chain_uuid not in result["chains_by_uuid"]:
                            result["chains_by_uuid"][chain_uuid] = []
                        result["chains_by_uuid"][chain_uuid].append((obj, modifier))
                    
                    # Проверяем целостность связей
                    if BlenderClonerUUIDManager._check_uuid_links_broken(modifier):
                        result["orphaned_cloners"].append((obj, modifier))
                        
                else:
                    # Legacy клонер без UUID
                    result["legacy_cloners"].append((obj, modifier))
                
                # Blender session_uid tracking
                if session_uid:
                    result["session_uid_cloners"][session_uid] = (obj, modifier)
        
        # Сортируем цепочки по sequence
        for chain_uuid in result["chains_by_uuid"]:
            result["chains_by_uuid"][chain_uuid].sort(
                key=lambda x: x[1].get("chain_sequence", 0)
            )
        
        print(f"🔍 Blender UUID Scanner complete:")
        print(f"  - UUID cloners: {len(result['cloners_by_uuid'])}")
        print(f"  - Chains: {len(result['chains_by_uuid'])}")
        print(f"  - Legacy cloners: {len(result['legacy_cloners'])}")
        print(f"  - Session UID cloners: {len(result['session_uid_cloners'])}")
        print(f"  - Orphaned cloners: {len(result['orphaned_cloners'])}")
        
        return result
    
    @staticmethod
    def _is_cloner_modifier(modifier) -> bool:
        """Проверить, является ли модификатор клонером (Blender-specific)"""
        try:
            # Проверяем метаданные
            if modifier.get("cloner_type"):
                return True
            
            # Проверяем имя node group
            if not modifier.node_group:
                return False
            
            node_group_name = modifier.node_group.name
            cloner_patterns = [
                "GridCloner", "LinearCloner", "CircleCloner", "SpiralCloner",
                "ObjectCloner", "CurvesCloner", "VolumeCloner", "SplineCloner"
            ]
            
            for pattern in cloner_patterns:
                if pattern in node_group_name:
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error checking cloner modifier: {e}")
            return False
    
    @staticmethod
    def _check_uuid_links_broken(modifier) -> bool:
        """Проверить, есть ли битые UUID связи"""
        previous_uuid = modifier.get("previous_cloner_uuid", "")
        next_uuids = modifier.get("next_cloner_uuids", "").split(",")
        next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
        
        # Проверяем previous
        if previous_uuid:
            prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
            if not prev_obj:
                return True
        
        # Проверяем next
        for next_uuid in next_uuids:
            next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
            if not next_obj:
                return True
        
        return False