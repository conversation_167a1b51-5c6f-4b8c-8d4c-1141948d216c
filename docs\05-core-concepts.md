# Основные концепции ClonerPro

## Введение в концепции

ClonerPro построен вокруг нескольких ключевых концепций, которые определяют его архитектуру и поведение. Понимание этих концепций критически важно для эффективной работы с аддоном.

## 1. Единая классовая архитектура с автоматической маршрутизацией

### Концепция единого API

ClonerPro построен вокруг **единой точки входа** с автоматическим выбором оптимальной системы исполнения:

```python
# Единый API для ВСЕХ клонеров
from core.templates.cloner_creation import create_cloner_unified

# Один API, автоматическая маршрутизация
any_cloner = create_cloner_unified(cloner_type, mode, source)

# Система автоматически определяет unified/mesh
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE"}  
MESH_CLONERS = {"OBJECT", "SPLINE"}

def get_cloner_system(cloner_type: str) -> str:
    """Автоматическое определение системы"""
    if cloner_type in UNIFIED_CLONERS:
        return "unified"
    elif cloner_type in MESH_CLONERS:  
        return "mesh"
    else:
        return "unknown"
```

### BaseCloner - единая классовая база

**Философия**: Все клонеры наследуются от единого базового класса для максимальной унификации

```python
class BaseCloner(BaseComponent):
    """Базовый класс для ВСЕХ клонеров ClonerPro"""
    
    def create_node_group(self, mode="OBJECT"):
        """Единый метод создания с анти-рекурсией для всех клонеров"""
        
    def _create_cloner_logic(self, base_nodes, mode):
        """Переопределяется в каждом дочернем классе"""
        raise NotImplementedError
```

### Unified клонеры (математически основанные)

**Характеристики**:
- Математические алгоритмы распределения (сетки, линии, окружности)
- BaseCloner архитектура с общими трансформациями
- Встроенная анти-рекурсия через Switch ноды
- Поддержка всех трёх режимов (Object/Stacked/Collection)

**Реализованные клонеры**: 
- **GridCloner** - 2D/3D сеточные распределения
- **LinearCloner** - линейные массивы
- **CircleCloner** - круговые паттерны

```python
# Пример создания unified клонера через единый API
grid_cloner = create_cloner_unified(
    cloner_type="GRID",    # Система автоматически выберет unified
    mode="OBJECT",         # Object/Stacked/Collection
    source=source_object   # Исходный объект
)
```

### Mesh клонеры (геометрически основанные)

**Характеристики**:
- Распределение по геометрии (вертексы, рёбра, фейсы, кривые)
- BaseCloner архитектура с mesh-специфичной логикой
- Прямая работа с геометрическими данными
- Оптимизация для сложных поверхностей

**Реализованные клонеры**:
- **ObjectCloner** - клонирование по поверхностям объектов
- **SplineCloner** - клонирование по кривым и путям

```python
# Пример создания mesh клонера через единый API
object_cloner = create_cloner_unified(
    cloner_type="OBJECT",     # Система автоматически выберет mesh
    mode="STACKED",           # Обычно Stacked для mesh клонеров
    source=surface_obj        # Поверхность для клонирования
)
```

## 2. Три режима создания

### Object Mode - Создание объекта клонера

**Концепция**: Создание отдельного объекта клонера в специальной коллекции

**Когда использовать**:
- Нужен независимый объект клонера
- Исходный объект должен остаться неизменным
- Требуется навигация по сложным сценам
- Планируется создание цепочек клонеров

**Архитектура**:
```
Исходный объект (остаётся неизменным)
    ↓
Копия в коллекции ClonerTo
    ↓  
Объект клонера в коллекции CLONERS_OriginalObjectName
    ↓
Geometry Nodes модификатор → Результат клонирования
```

**Пример использования**:
```python
# Создаём куб
bpy.ops.mesh.primitive_cube_add()
original_cube = bpy.context.active_object

# Создаём Grid клонер в Object режиме через единый API
grid_cloner = create_cloner_unified("GRID", "OBJECT", original_cube)

# Результат:
# - original_cube остался неизменным
# - Создался grid_cloner в коллекции CLONERS_Cube  
# - Создалась копия Cube_Copy в коллекции ClonerTo
```

### Stacked Mode - Модификатор на объекте

**Концепция**: Добавление модификатора клонера прямо на исходный объект

**Когда использовать**:
- Нужно клонировать сам объект
- Требуется минимальная сложность сцены
- Модификатор должен быть частью object stack
- Простые одноразовые клонирования

**Архитектура**:
```
Исходный объект
    ↓
Добавление Geometry Nodes модификатора
    ↓
Результат клонирования прямо на объекте
```

**Пример использования**:
```python
# Создаём куб
bpy.ops.mesh.primitive_cube_add()
cube = bpy.context.active_object

# Добавляем Linear клонер как модификатор через единый API
linear_modifier = create_cloner_unified("LINEAR", "STACKED", cube)

# Результат:
# - На cube добавился модификатор LinearCloner
# - Клонирование применяется к самому кубу
```

### Collection Mode - Клонирование коллекций

**Концепция**: Клонирование целых коллекций объектов

**Когда использовать**:
- Нужно клонировать группы объектов
- Сложные сборки должны дублироваться целиком
- Архитектурные элементы с множественными компонентами
- Создание сложных паттернов из готовых блоков

**Архитектура**:
```
Исходная коллекция (группа объектов)
    ↓
Копия коллекции в ClonerTo (если нужно)
    ↓
Объект клонера в коллекции CLONERS_
    ↓
Geometry Nodes с Collection Input → Результат клонирования коллекций
```

**Пример использования**:
```python
# Создаём коллекцию с несколькими объектами
furniture_collection = bpy.data.collections.new("Furniture")
# ... добавляем стол, стулья, лампу в коллекцию

# Создаём Circle клонер для коллекции через единый API
circle_cloner = create_cloner_unified("CIRCLE", "COLLECTION", "Furniture")

# Результат:
# - Создался клонер, который размещает всю коллекцию по кругу
# - Каждый инстанс содержит стол, стулья и лампу
```

## 3. UUID система и идентификация

### Концепция уникальной идентификации

**Проблема**: Blender объекты могут переименовываться, удаляться, дублироваться, что нарушает связи между клонерами.

**Решение**: Каждый клонер получает уникальный UUID, который сохраняется в custom properties и не зависит от имён объектов.

```python
# Структура UUID метаданных
{
    "cloner_uuid": "12345678-1234-5678-9abc-123456789def",      # Уникальный ID клонера
    "chain_uuid": "*************-8765-cba9-987654321fed",       # ID цепочки клонеров
    "chain_source_uuid": "abcdef12-3456-7890-abcd-ef1234567890", # ID источника цепочки
    "previous_cloner_uuid": "...",                               # UUID предыдущего в цепи
    "next_cloner_uuids": "uuid1,uuid2,uuid3",                   # UUID следующих в цепи
    "creation_timestamp": 1702023456.789,                       # Время создания
    "display_name": "Grid Cloner",                              # Человекочитаемое имя
    "user_notes": ""                                             # Пользовательские заметки
}
```

### UUID Manager

```python
from core.uuid.manager import BlenderClonerUUIDManager

# Поиск клонера по UUID
obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(cloner_uuid)

# Поиск всех клонеров в цепочке
chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)

# Сканирование всех клонеров с UUID
result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
```

## 4. Chain Management (Управление цепочками)

### Концепция цепочек клонеров

**Определение**: Цепочка клонеров - это последовательность связанных клонеров, где каждый следующий клонирует результат предыдущего.

**Пример цепочки**:
```
Исходный куб → Grid клонер → Object клонер → Linear клонер
```

### Автоматическое обнаружение цепочек

ClonerPro автоматически обнаруживает когда создаётся клонер от другого клонера:

```python
# При создании клонера система проверяет:
# 1. Является ли source объектом клонером?
# 2. Есть ли у source клонера chain_uuid?
# 3. Нужно ли создавать новую цепочку или присоединиться к существующей?

def setup_cloner_chain_links_only(context, new_cloner, source_cloner, source_collection):
    """Автоматическое связывание клонеров в цепочки"""
    
    # Определение источника для UUID
    source_uuid = _get_or_create_source_uuid(source_object_or_collection)
    
    # Поиск существующих клонеров с тем же источником  
    related_cloners = _find_cloners_by_source_uuid(source_uuid)
    
    # Установка связей
    if related_cloners:
        _link_to_existing_chain(new_cloner, related_cloners)
    else:
        _create_new_chain(new_cloner, source_uuid)
```

### Метаданные цепочек

```python
# Каждый клонер в цепочке хранит информацию о связях
{
    "chain_uuid": "*************-8765-cba9-987654321fed",    # Общий для всей цепочки
    "chain_source_uuid": "abcdef12-3456-7890-abcd",          # UUID источника цепочки
    "chain_sequence": 2,                                      # Позиция в цепочке (0, 1, 2...)
    "previous_cloner_uuid": "uuid_of_previous",               # Связь с предыдущим
    "next_cloner_uuids": "uuid_of_next1,uuid_of_next2",      # Связи со следующими
}
```

### Smart Recovery (Умное восстановление)

После перезагрузки файла система восстанавливает все связи:

```python
# При загрузке файла вызывается
def restore_cloner_chains_on_file_load():
    # 1. Сканирование всех клонеров в сцене
    # 2. Группировка по chain_uuid
    # 3. Восстановление связей между клонерами
    # 4. Валидация целостности цепочек
```

## 5. Встроенная Anti-Recursion система

### Концепция встроенной защиты

**Проблема**: При создании сложных цепочек клонеров возможны циклические зависимости, которые приводят к бесконечным циклам и зависанию Blender.

**Решение**: Встроенная защита на уровне BaseCloner через Switch-based архитектуру.

```python
class BaseCloner(BaseComponent):
    def apply_anti_recursion(self, base_nodes, final_geometry_output, use_anti_recursion=True):
        """Встроенная анти-рекурсия через Switch ноды - АВТОМАТИЧЕСКИ для всех клонеров"""
        
        # Final Realize Instances (анти-рекурсия)
        final_realize = nodes.new('GeometryNodeRealizeInstances')
        
        # Final Switch (переключение анти-рекурсии)
        final_switch = nodes.new('GeometryNodeSwitch') 
        final_switch.input_type = 'GEOMETRY'
        
        # True = instances (анти-рекурсия ВЫКЛЮЧЕНА)
        # False = realized (анти-рекурсия ВКЛЮЧЕНА)
        links.new(final_geometry_output, final_switch.inputs['True'])
        links.new(final_realize.outputs['Geometry'], final_switch.inputs['False'])
        
        # Управление через сокет "Realize Instances"
        links.new(group_input.outputs['Realize Instances'], final_switch.inputs['Switch'])
        
        return final_switch.outputs['Output']
```

### Принципы работы встроенной anti-recursion

1. **Switch-based логика** - Переключение между instances и realized геометрией
2. **Встроенность в BaseCloner** - Автоматически применяется для ВСЕХ клонеров
3. **Управляемость** - Контролируется через сокет "Realize Instances"
4. **Универсальность** - Работает одинаково для unified и mesh клонеров

## 6. Эффекторы и модификация

### Концепция эффекторов

**Определение**: Эффекторы - это компоненты, которые модифицируют поведение клонеров, изменяя трансформации, видимость или другие параметры клонированных объектов.

**Типы эффекторов**:
- **Random** - Случайные трансформации
- **Noise** - Шумовые изменения на основе координат
- **Plain** - Линейные градиенты изменений (планируется)
- **Step** - Ступенчатые изменения (планируется)
- **Delay** - Анимационные задержки (планируется)
- **Target** - Направление на целевой объект (планируется)

### Архитектура эффекторов

```python
# Создание эффектора
def create_random_effector_logic_group(name_suffix=""):
    """Создание geometry nodes группы для Random эффектора"""
    
    # Создание node group с рандомизацией
    node_group = bpy.data.node_groups.new(name=f"RandomEffector{name_suffix}", type='GeometryNodeTree')
    
    # Настройка нодов для случайных трансформаций
    setup_random_transformation_nodes(node_group)
    
    return node_group

# Связывание с клонером через geometry nodes
def link_effector_to_cloner(cloner_obj, effector_type):
    """Связывание эффектора с клонером"""
    # Поиск клонера и добавление эффектора в его node tree
```

### Применение эффекторов

```python
# Пример использования Random эффектора
from core.managers.effector_creation import create_effector

# Создание клонера через единый API
grid_cloner = create_cloner_unified("GRID", "OBJECT", source_obj)

# Добавление Random эффектора
random_effector = create_effector(
    effector_type="RANDOM",
    target_cloner=grid_cloner,
    strength=0.5,
    seed=42
)

# ПРИМЕЧАНИЕ: Random эффектор уже встроен в BaseCloner через сокеты:
# - Random Position, Random Rotation, Random Scale, Random Seed
```

## 7. Geometry Nodes интеграция

### Концепция node-based архитектуры

ClonerPro полностью построен на Geometry Nodes, что обеспечивает:

- **Производительность** - Нативная производительность Blender
- **Гибкость** - Возможность ручного редактирования node trees
- **Совместимость** - Полная совместимость с экосистемой Blender
- **Анимируемость** - Все параметры можно анимировать

### Единая структура node groups (BaseCloner)

#### Все клонеры используют единую архитектуру
```
BaseCloner Node Group
├── Group Input (базовые + специфичные сокеты)
├── Geometry Input Logic (Object/Stacked/Collection)
├── Специфичная логика клонера (_create_cloner_logic)
│   ├── Distribution Logic (Grid/Linear/Circle/Object/Spline)
│   ├── Instance Transforms (Scale, Rotation)
│   └── Random Transforms (Position, Rotation, Scale)
├── Global Transforms (Position, Rotation)
├── Anti-Recursion (Final Switch + Realize Instances)
└── Group Output (Geometry)
```

#### Преимущества единой структуры
- **Стандартизация**: Все клонеры имеют одинаковые базовые сокеты
- **Предсказуемость**: Пользователи знают что ожидать от любого клонера
- **Совместимость**: Легко заменять один тип клонера на другой
- **Расширяемость**: Новые клонеры автоматически получают всю базовую функциональность

### Параметры и интерфейс

```python
# BaseCloner автоматически создает полный интерфейс
class GridCloner(BaseCloner):
    def get_specific_sockets(self):
        """Специфичные сокеты Grid клонера (добавляются к базовым)"""
        return [
            ("Count X", "NodeSocketInt", "INPUT", 3),
            ("Count Y", "NodeSocketInt", "INPUT", 3), 
            ("Count Z", "NodeSocketInt", "INPUT", 1),
            ("Spacing", "NodeSocketFloat", "INPUT", 2.0),
        ]

# Базовые сокеты создаются автоматически BaseCloner:
# - Geometry, Instance Source, Collection (входные)
# - Instance Scale, Instance Rotation (трансформации)
# - Random Position, Random Rotation, Random Scale, Random Seed (рандомизация)
# - Global Position, Global Rotation (глобальные трансформации)
# - Realize Instances (анти-рекурсия)
# - Geometry (выходной)
```

## 8. Производительность и оптимизация

### Концепция эффективности

ClonerPro разработан с учётом производительности:

**Ленивая загрузка**:
```python
# Компоненты загружаются только при необходимости
def _get_cloner_logic_function(cloner_type, mode):
    if cloner_type == "GRID":
        from ...components.cloners.grid import create_grid_cloner_logic_group
        return create_grid_cloner_logic_group
```

**Кэширование** (временно отключено):
```python
# Планируется возврат с fake_user подходом
NODE_GROUP_CACHE = {}
```

**Batch операции**:
```python
# Групповое создание клонеров
def create_multiple_cloners(cloner_configs):
    """Эффективное создание множественных клонеров"""
    # Оптимизированная обработка списка конфигураций
```

### Пределы производительности

```python
# Системные ограничения в core/core.py
SAFETY_LIMITS = {
    'max_recursion_depth': 32,
    'max_cloner_chain_length': 10,
    'max_effectors_per_cloner': 20
}

SYSTEM_DEFAULTS = {
    'max_instances': 10000  # Максимум инстансов на клонер
}
```

Эти концепции формируют основу для понимания и эффективного использования ClonerPro. Каждая концепция тесно связана с другими, создавая целостную и мощную систему клонирования для Blender.