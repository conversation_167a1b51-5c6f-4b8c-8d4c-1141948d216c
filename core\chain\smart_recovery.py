"""
Smart Chain Recovery - Умное восстановление цепей клонеров
Анализ геометрии для поиска лучших замен при утере источников
"""

import bpy
from mathutils import Vector
from ..core import get_cloner_modifier, get_cloner_info

class SmartChainRecovery:
    """Умная система восстановления цепей"""
    
    @staticmethod
    def analyze_object_signature(obj):
        """Создать простую сигнатуру объекта для сравнения"""
        if not obj or not obj.data:
            return None
        
        try:
            signature = {
                'type': obj.type,
                'vertex_count': 0,
                'face_count': 0,
                'dimensions': obj.dimensions.copy(),
                'material_count': len(obj.material_slots)
            }
            
            if obj.type == 'MESH' and hasattr(obj.data, 'vertices'):
                signature['vertex_count'] = len(obj.data.vertices)
                signature['face_count'] = len(obj.data.polygons)
            
            return signature
            
        except:
            return None
    
    @staticmethod
    def calculate_similarity(sig1, sig2):
        """Рассчитать похожесть двух объектов"""
        if not sig1 or not sig2:
            return 0.0
        
        score = 0.0
        
        # Тип объекта (важно)
        if sig1['type'] == sig2['type']:
            score += 40.0
        
        # Количество вершин (для mesh)
        if sig1['type'] == 'MESH' and sig2['type'] == 'MESH':
            v1, v2 = sig1['vertex_count'], sig2['vertex_count']
            if v1 > 0 and v2 > 0:
                vertex_sim = min(v1, v2) / max(v1, v2)
                score += vertex_sim * 30.0
        
        # Размеры
        dim_diff = (sig1['dimensions'] - sig2['dimensions']).length
        max_dim = max(sig1['dimensions'].length, sig2['dimensions'].length)
        if max_dim > 0:
            size_sim = max(0, 1 - (dim_diff / max_dim))
            score += size_sim * 20.0
        
        # Материалы
        if sig1['material_count'] == sig2['material_count']:
            score += 10.0
        
        return min(score, 100.0)
    
    @staticmethod
    def find_best_replacement_for_missing_source(missing_name, context_objects=None):
        """Найти лучшую замену для утерянного источника"""
        if not context_objects:
            context_objects = bpy.context.scene.objects
        
        # Ищем объекты с похожими именами
        name_candidates = []
        for obj in context_objects:
            if obj.name != missing_name:
                # Простое сравнение имен
                name_similarity = 0
                if missing_name.lower() in obj.name.lower():
                    name_similarity = len(missing_name) / len(obj.name)
                elif obj.name.lower() in missing_name.lower():
                    name_similarity = len(obj.name) / len(missing_name)
                
                if name_similarity > 0.3:  # Минимальная схожесть имен
                    name_candidates.append((obj, name_similarity))
        
        # Сортируем по схожести имен
        name_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Возвращаем лучший кандидат если есть
        if name_candidates:
            best_candidate = name_candidates[0][0]
            similarity = name_candidates[0][1] * 100
            
            if similarity > 50:  # Достаточно высокая схожесть
                return best_candidate, similarity
        
        return None, 0
    
    @staticmethod
    def smart_repair_broken_source(cloner_obj, cloner_modifier):
        """Умное восстановление сломанного источника"""
        cloner_info = get_cloner_info(cloner_modifier)
        
        # Ищем утерянный источник
        missing_source = cloner_info.get("chain_source_object", "")
        if not missing_source or missing_source in bpy.data.objects:
            return False  # Источник не утерян
        
        # Пытаемся найти замену
        replacement, similarity = SmartChainRecovery.find_best_replacement_for_missing_source(missing_source)
        
        if replacement and similarity > 60:  # Высокая уверенность
            try:
                # Переподключаем к найденной замене
                from .reconnection import reconnect_to_source
                success = reconnect_to_source(cloner_obj, cloner_modifier, replacement)
                
                if success:
                    # Обновляем метаданные
                    cloner_modifier["chain_source_object"] = replacement.name
                    print(f"🔧 [SMART_RECOVERY] Auto-repaired {cloner_obj.name}: {missing_source} → {replacement.name} ({similarity:.1f}%)")
                    return True
                    
            except Exception as e:
                pass
        
        return False