"""
ClonerPro Component Registries

Центральная система регистрации всех компонентов ClonerPro.
Использует паттерн Registry для упрощения добавления новых компонентов.

Для добавления нового компонента достаточно добавить одну строчку в соответствующий словарь.
"""

import bpy
from typing import Dict, Callable, Optional, List

# Импортируем все функции создания клонеров
# Grid клонер теперь использует классовую архитектуру - импорты не нужны
# Linear и Circle клонеры мигрированы на классовую архитектуру - импорты не нужны

# Импортируем эффекторы
from ...components.effectors.random import create_random_effector
from ...components.effectors.noise import create_noise_effector

# Импортируем поля (fields) - пока оставляем заглушку
# from ...components.fields.sphere import create_sphere_field


# ======================================================================
# CLONER REGISTRY - Регистрация всех клонеров
# ======================================================================

# Unified клонеры (GRID, LINEAR, CIRCLE, HONEYCOMB) - используют unified creation систему
UNIFIED_CLONERS_REGISTRY = {
    "GRID": "GridCloner",
    "LINEAR": "LinearCloner",
    "CIRCLE": "CircleCloner",
    "HONEYCOMB": "HoneycombCloner"
}

# Mesh клонеры (OBJECT, SPLINE) - используют mesh creation систему  
MESH_CLONERS_REGISTRY = {
    "OBJECT": "ObjectCloner",
    "SPLINE": "SplineCloner"
}

# Импортируем единый реестр клонеров
from .cloner_registry import CLONER_REGISTRY


# ======================================================================
# EFFECTOR REGISTRY - Регистрация всех эффекторов
# ======================================================================

EFFECTOR_REGISTRY = {
    'RANDOM': {
        'creator': create_random_effector,
        'display_name': 'Random Effector',
        'description': 'Applies random transformations to clones',
        'affects': ['position', 'rotation', 'scale']
    },
    'NOISE': {
        'creator': create_noise_effector,
        'display_name': 'Noise Effector',
        'description': 'Applies noise-based transformations to clones',
        'affects': ['position', 'rotation', 'scale']
    }
    # Будущие эффекторы добавлять здесь:
    # 'STEP': {
    #     'creator': create_step_effector,
    #     'display_name': 'Step Effector',
    #     'description': 'Applies stepped transformations'
    # }
}


# ======================================================================
# FIELD REGISTRY - Регистрация всех полей
# ======================================================================

FIELD_REGISTRY = {
    # Пока пусто - поля будут добавлены позже
    # 'SPHERE': {
    #     'creator': create_sphere_field,
    #     'display_name': 'Sphere Field',
    #     'description': 'Creates spherical influence field',
    #     'shape': 'sphere'
    # }
}


# ======================================================================
# HELPER FUNCTIONS - Вспомогательные функции для работы с реестрами
# ======================================================================

def get_cloner_creator(cloner_type: str, mode: str = 'OBJECT') -> Optional[Callable]:
    """
    Получить класс клонера по типу (классовая архитектура)
    
    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE, OBJECT, SPLINE)
        mode: Режим создания (OBJECT, STACKED, COLLECTION)
    
    Returns:
        Класс клонера или None если не найден
    """
    cloner_class = CLONER_REGISTRY.get(cloner_type)
    if not cloner_class:
        print(f"⚠️ [REGISTRY] Cloner type '{cloner_type}' not found in registry")
        return None
    
    return cloner_class


def is_unified_cloner(cloner_type: str) -> bool:
    """Проверить, является ли клонер unified типом (GRID, LINEAR, CIRCLE)"""
    return cloner_type in UNIFIED_CLONERS_REGISTRY


def is_mesh_cloner(cloner_type: str) -> bool:
    """Проверить, является ли клонер mesh типом (OBJECT, SPLINE)"""
    return cloner_type in MESH_CLONERS_REGISTRY


def get_effector_creator(effector_type: str) -> Optional[Callable]:
    """
    Получить функцию создания эффектора по типу
    
    Args:
        effector_type: Тип эффектора (RANDOM, NOISE)
    
    Returns:
        Функция создания эффектора или None если не найдена
    """
    effector_info = EFFECTOR_REGISTRY.get(effector_type)
    if not effector_info:
        print(f"⚠️ [REGISTRY] Effector type '{effector_type}' not found in registry")
        return None
    
    return effector_info.get('creator')


def get_field_creator(field_type: str) -> Optional[Callable]:
    """
    Получить функцию создания поля по типу
    
    Args:
        field_type: Тип поля (SPHERE, BOX)
    
    Returns:
        Функция создания поля или None если не найдена
    """
    field_info = FIELD_REGISTRY.get(field_type)
    if not field_info:
        print(f"⚠️ [REGISTRY] Field type '{field_type}' not found in registry")
        return None
    
    return field_info.get('creator')


def list_available_cloners() -> List[str]:
    """Получить список всех доступных типов клонеров"""
    return list(CLONER_REGISTRY.keys())


def list_available_effectors() -> List[str]:
    """Получить список всех доступных типов эффекторов"""
    return list(EFFECTOR_REGISTRY.keys())


def list_available_fields() -> List[str]:
    """Получить список всех доступных типов полей"""
    return list(FIELD_REGISTRY.keys())


def get_component_info(component_type: str, registry_type: str = 'cloner') -> Optional[Dict]:
    """
    Получить полную информацию о компоненте
    
    Args:
        component_type: Тип компонента
        registry_type: Тип реестра ('cloner', 'effector', 'field')
    
    Returns:
        Словарь с информацией о компоненте или None
    """
    if registry_type == 'cloner':
        return CLONER_REGISTRY.get(component_type)
    elif registry_type == 'effector':
        return EFFECTOR_REGISTRY.get(component_type)
    elif registry_type == 'field':
        return FIELD_REGISTRY.get(component_type)
    else:
        print(f"⚠️ [REGISTRY] Unknown registry type: {registry_type}")
        return None


def validate_component_registration():
    """Проверить корректность регистрации всех компонентов"""
    print("🔍 [REGISTRY] Validating component registration...")
    
    # Проверяем клонеры (все теперь в классовой архитектуре)
    for cloner_type, cloner_class in CLONER_REGISTRY.items():
        if hasattr(cloner_class, '__name__') and hasattr(cloner_class, '__bases__'):
            print(f"✅ [REGISTRY] Class-based cloner '{cloner_type}' -> {cloner_class.__name__}")
            
            # Проверяем тип системы
            if cloner_type in UNIFIED_CLONERS_REGISTRY:
                print(f"  🔄 [REGISTRY] {cloner_type} uses UNIFIED system")
            elif cloner_type in MESH_CLONERS_REGISTRY:
                print(f"  🎯 [REGISTRY] {cloner_type} uses MESH system")
        else:
            print(f"⚠️ [REGISTRY] Invalid cloner registration: '{cloner_type}' -> {cloner_class}")
    
    # Проверяем эффекторы
    for effector_type, effector_info in EFFECTOR_REGISTRY.items():
        if 'creator' not in effector_info:
            print(f"⚠️ [REGISTRY] Missing creator for effector '{effector_type}'")
        else:
            print(f"✅ [REGISTRY] Effector '{effector_type}' registered")
    
    # Проверяем поля
    for field_type, field_info in FIELD_REGISTRY.items():
        if 'creator' not in field_info:
            print(f"⚠️ [REGISTRY] Missing creator for field '{field_type}'")
        else:
            print(f"✅ [REGISTRY] Field '{field_type}' registered")
    
    print("✅ [REGISTRY] Component registration validation complete")


# Автоматическая валидация при импорте модуля
if __name__ != "__main__":
    validate_component_registration()