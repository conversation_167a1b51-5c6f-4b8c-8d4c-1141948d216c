"""
Components Package

Содержит все реализации клонеров, эффекторов и филдов.
"""

from . import cloners
from . import effectors  
from . import fields

__all__ = ['cloners', 'effectors', 'fields']

def register():
    """Register component implementations"""
    if hasattr(cloners, 'register'):
        cloners.register()

def unregister():
    """Unregister component implementations"""
    if hasattr(cloners, 'unregister'):
        cloners.unregister()