"""
Linear Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
from ..base_cloner import BaseCloner


class LinearCloner(BaseCloner):
    """
    Linear Cloner - создает линейное распределение клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "LINEAR"
    bl_label = "Linear Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Linear Cloner
        Заменяет старый linear_config.py
        """
        # Basic Linear Settings
        props_owner.linear_count = bpy.props.IntProperty(
            name="Count",
            description="Number of instances along the line",
            default=5,
            min=1,
            max=1000
        )
        props_owner.linear_offset = bpy.props.FloatVectorProperty(
            name="Offset",
            description="Offset between instances",
            default=(3.0, 0.0, 0.0),
            size=3
        )
        
        # Gradient Settings
        props_owner.linear_scale_start = bpy.props.FloatVectorProperty(
            name="Scale Start",
            description="Scale at the start of the line",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.linear_scale_end = bpy.props.FloatVectorProperty(
            name="Scale End", 
            description="Scale at the end of the line",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.linear_rotation_start = bpy.props.FloatVectorProperty(
            name="Rotation Start",
            description="Rotation at the start of the line",
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )
        props_owner.linear_rotation_end = bpy.props.FloatVectorProperty(
            name="Rotation End",
            description="Rotation at the end of the line", 
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Linear Cloner
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Count", "NodeSocketInt", "INPUT", 5),
            ("Offset", "NodeSocketVector", "INPUT", (3.0, 0.0, 0.0)),
            ("Scale Start", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Scale End", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Rotation Start", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Rotation End", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Linear Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем линейное распределение точек
        linear_points = self._create_linear_distribution(base_nodes)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Line"
        instance_node.location = (400, 0)
        links.new(linear_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])
        
        # Получаем индекс для градиента и рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем градиентные трансформации
        instances_with_gradient = self._apply_gradient_transforms(base_nodes, instance_node.outputs['Instances'], index_node.outputs['Index'])
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instances_with_gradient)
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_linear_distribution(self, base_nodes):
        """
        Создание линейного распределения точек
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход с линейными точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Создаем линию точек
        mesh_line = nodes.new('GeometryNodeMeshLine')
        mesh_line.name = "Linear Points"
        mesh_line.mode = 'OFFSET'
        mesh_line.count_mode = 'TOTAL'
        mesh_line.location = (-200, 200)
        links.new(group_input.outputs['Count'], mesh_line.inputs['Count'])
        links.new(group_input.outputs['Offset'], mesh_line.inputs['Offset'])
        
        return mesh_line.outputs['Mesh']
    
    def _apply_gradient_transforms(self, base_nodes, instances_input, index_input):
        """
        Применение градиентных трансформаций (Scale/Rotation Start -> End)
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами
            index_input: Входной сокет с индексом
            
        Returns:
            NodeSocket: Выход с градиентными трансформациями
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Нормализуем индекс для интерполяции (0.0 - 1.0)
        # Получаем максимальный индекс (Count - 1)
        count_minus_one = nodes.new('ShaderNodeMath')
        count_minus_one.operation = 'SUBTRACT'
        count_minus_one.inputs[1].default_value = 1.0
        count_minus_one.location = (0, -300)
        links.new(group_input.outputs['Count'], count_minus_one.inputs[0])
        
        # Нормализуем индекс
        normalize_index = nodes.new('ShaderNodeMath')
        normalize_index.operation = 'DIVIDE'
        normalize_index.location = (150, -300)
        links.new(index_input, normalize_index.inputs[0])
        links.new(count_minus_one.outputs['Value'], normalize_index.inputs[1])
        
        # Интерполяция масштаба
        mix_scale = nodes.new('ShaderNodeMix')
        mix_scale.data_type = 'VECTOR'
        mix_scale.blend_type = 'MIX'
        mix_scale.location = (300, -200)
        links.new(normalize_index.outputs['Value'], mix_scale.inputs['Factor'])
        links.new(group_input.outputs['Scale Start'], mix_scale.inputs['A'])
        links.new(group_input.outputs['Scale End'], mix_scale.inputs['B'])
        
        # Интерполяция поворота
        mix_rotation = nodes.new('ShaderNodeMix')
        mix_rotation.data_type = 'VECTOR'
        mix_rotation.blend_type = 'MIX'
        mix_rotation.location = (300, -400)
        links.new(normalize_index.outputs['Value'], mix_rotation.inputs['Factor'])
        links.new(group_input.outputs['Rotation Start'], mix_rotation.inputs['A'])
        links.new(group_input.outputs['Rotation End'], mix_rotation.inputs['B'])
        
        # Применяем градиентное вращение
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.name = "Apply Gradient Rotation"
        rotate_instances.location = (500, 0)
        links.new(instances_input, rotate_instances.inputs['Instances'])
        links.new(mix_rotation.outputs['Result'], rotate_instances.inputs['Rotation'])
        
        # Применяем градиентный масштаб
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.name = "Apply Gradient Scale"
        scale_instances.location = (650, 0)
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(mix_scale.outputs['Result'], scale_instances.inputs['Scale'])
        
        return scale_instances.outputs['Instances']
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Linear Cloner
        
        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        # Linear Settings
        box = layout.box()
        box.label(text="Linear Settings", icon='IPO_LINEAR')
        
        row = box.row(align=True)
        row.prop(props_owner, "linear_count", text="Count")
        box.prop(props_owner, "linear_offset", text="Offset")
        
        # Gradient Settings
        box = layout.box()
        box.label(text="Gradient", icon='IPO_EASE_IN_OUT')
        
        col = box.column(align=True)
        col.label(text="Scale:")
        col.prop(props_owner, "linear_scale_start", text="Start")
        col.prop(props_owner, "linear_scale_end", text="End")
        
        col.separator()
        col.label(text="Rotation:")
        col.prop(props_owner, "linear_rotation_start", text="Start")
        col.prop(props_owner, "linear_rotation_end", text="End")
        
        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, modifier)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        
        # Instance Transform
        box = layout.box()
        box.label(text="Instance Transform", icon='CON_TRANSFORM')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name in ['Instance Scale', 'Instance Rotation']:
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Randomization
        box = layout.box()
        box.label(text="Randomization", icon='RNDCURVE')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Random') or socket.name == 'Random Seed':
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Global Transform
        box = layout.box()
        box.label(text="Global Transform", icon='OBJECT_ORIGIN')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Global'):
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Linear Cloner"""
        base_defaults = super().get_default_parameters()
        linear_defaults = {
            "count": 5,
            "offset": (3.0, 0.0, 0.0),
            "scale_start": (1.0, 1.0, 1.0),
            "scale_end": (1.0, 1.0, 1.0),
            "rotation_start": (0.0, 0.0, 0.0),
            "rotation_end": (0.0, 0.0, 0.0)
        }
        return {**base_defaults, **linear_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        linear_groups = {
            "Linear Settings": ["Count", "Offset"],
            "Gradient": ["Scale Start", "Scale End", "Rotation Start", "Rotation End"]
        }
        return {**linear_groups, **base_groups}


# Экземпляр класса для использования в других модулях
linear_cloner = LinearCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Linear Cloner (в новой архитектуре не требуется)"""
    print("✅ Linear Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Linear Cloner (в новой архитектуре не требуется)"""
    print("✅ Linear Cloner: Using class-based architecture, no unregistration needed") 
    pass