"""
Circle Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
import bmesh
import mathutils
from ..base_cloner import BaseCloner


class CircleCloner(BaseCloner):
    """
    Circle Cloner - создает радиальное распределение клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "CIRCLE"
    bl_label = "Circle Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Circle Cloner
        Заменяет старый circle_config.py
        """
        # Circle Settings
        props_owner.circle_count = bpy.props.IntProperty(
            name="Count",
            description="Number of instances around the circle",
            default=8,
            min=3,
            max=1000
        )
        props_owner.circle_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Radius of the circle",
            default=2.0,
            min=0.0
        )
        props_owner.circle_height = bpy.props.FloatProperty(
            name="Height",
            description="Height offset for the circle",
            default=0.0
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Circle Cloner
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Count", "NodeSocketInt", "INPUT", 8),
            ("Radius", "NodeSocketFloat", "INPUT", 2.0),
            ("Height", "NodeSocketFloat", "INPUT", 0.0),
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Circle Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем круговое распределение точек
        circle_points = self._create_circular_distribution(base_nodes)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Circle"
        instance_node.location = (400, 0)
        links.new(circle_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])
        
        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем face-to-center поворот (специфично для Circle Cloner)
        instances_with_face_rotation = self._apply_face_to_center_rotation(base_nodes, instance_node.outputs['Instances'])
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instances_with_face_rotation)
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_circular_distribution(self, base_nodes):
        """
        Создание кругового распределения точек
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход с круговыми точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Создаем простую окружность
        mesh_circle = nodes.new('GeometryNodeMeshCircle')
        mesh_circle.name = "Circle Points"
        mesh_circle.fill_type = 'NONE'  # Только вершины окружности
        mesh_circle.location = (-400, 200)
        links.new(group_input.outputs['Count'], mesh_circle.inputs['Vertices'])
        links.new(group_input.outputs['Radius'], mesh_circle.inputs['Radius'])
        
        # Преобразуем mesh в точки
        mesh_to_points = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points.name = "Mesh to Points"
        mesh_to_points.mode = 'VERTICES'
        mesh_to_points.location = (-200, 200)
        links.new(mesh_circle.outputs['Mesh'], mesh_to_points.inputs['Mesh'])
        
        # Применяем height offset если нужно
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Apply Height Offset"
        set_position.location = (-100, 200)
        links.new(mesh_to_points.outputs['Points'], set_position.inputs['Geometry'])
        
        # Создаем вектор высоты
        combine_height = nodes.new('ShaderNodeCombineXYZ')
        combine_height.location = (-300, 100)
        combine_height.inputs['X'].default_value = 0.0
        combine_height.inputs['Y'].default_value = 0.0
        links.new(group_input.outputs['Height'], combine_height.inputs['Z'])
        
        links.new(combine_height.outputs['Vector'], set_position.inputs['Offset'])
        
        return set_position.outputs['Geometry']
    
    def _apply_face_to_center_rotation(self, base_nodes, instances_input):
        """
        Применение поворота "лицом к центру" (специфично для Circle Cloner)
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами
            
        Returns:
            NodeSocket: Выход с поворотом лицом к центру
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        
        # Применяем поворот лицом к центру (простой поворот на 90 градусов)
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.name = "Face To Center Rotation"
        rotate_instances.location = (500, 0)
        links.new(instances_input, rotate_instances.inputs['Instances'])
        
        # Создаем поворот на 90 градусов по Z-оси (1.5708 радиан)
        combine_face_rotation = nodes.new('ShaderNodeCombineXYZ')
        combine_face_rotation.location = (300, -100)
        combine_face_rotation.inputs[0].default_value = 0.0  # X
        combine_face_rotation.inputs[1].default_value = 0.0  # Y  
        combine_face_rotation.inputs[2].default_value = 1.5708  # Z = 90 degrees in radians
        
        links.new(combine_face_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])
        
        return rotate_instances.outputs['Instances']
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Circle Cloner
        
        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        # Circle Settings
        box = layout.box()
        box.label(text="Circle Settings", icon='MESH_CIRCLE')
        
        col = box.column(align=True)
        col.prop(props_owner, "circle_count", text="Count")
        col.prop(props_owner, "circle_radius", text="Radius")
        col.prop(props_owner, "circle_height", text="Height")
        
        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, modifier)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        
        # Instance Transform
        box = layout.box()
        box.label(text="Instance Transform", icon='CON_TRANSFORM')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name in ['Instance Scale', 'Instance Rotation']:
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Randomization
        box = layout.box()
        box.label(text="Randomization", icon='RNDCURVE')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Random') or socket.name == 'Random Seed':
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Global Transform
        box = layout.box()
        box.label(text="Global Transform", icon='OBJECT_ORIGIN')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Global'):
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Circle Cloner"""
        base_defaults = super().get_default_parameters()
        circle_defaults = {
            "count": 8,
            "radius": 2.0,
            "height": 0.0
        }
        return {**base_defaults, **circle_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        circle_groups = {
            "Circle Settings": ["Count", "Radius", "Height"]
        }
        return {**circle_groups, **base_groups}


# Экземпляр класса для использования в других модулях
circle_cloner = CircleCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Circle Cloner (в новой архитектуре не требуется)"""
    print("✅ Circle Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Circle Cloner (в новой архитектуре не требуется)"""
    print("✅ Circle Cloner: Using class-based architecture, no unregistration needed") 
    pass