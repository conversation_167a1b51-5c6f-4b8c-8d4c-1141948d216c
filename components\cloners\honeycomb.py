"""
Honeycomb Cloner - Class-based implementation
Создает гексагональные (сотовые) паттерны клонов
"""

import bpy
import math
from ..base_cloner import BaseCloner


class HoneycombCloner(BaseCloner):
    """
    Honeycomb Cloner - создает гексагональные (сотовые) паттерны клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "HONEYCOMB"
    bl_label = "Honeycomb Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Honeycomb Cloner
        Заменяет старый honeycomb_config.py
        """
        # Basic Honeycomb Settings
        props_owner.honeycomb_rings = bpy.props.IntProperty(
            name="Rings",
            description="Number of hexagonal rings around the center",
            default=3,
            min=1,
            max=20
        )
        props_owner.honeycomb_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Distance from center to each hexagon vertex",
            default=2.0,
            min=0.1,
            max=100.0
        )
        props_owner.honeycomb_spacing = bpy.props.FloatProperty(
            name="Spacing",
            description="Additional spacing between hexagons",
            default=0.1,
            min=0.0,
            max=10.0
        )
        
        # Pattern Settings
        props_owner.honeycomb_pattern_type = bpy.props.EnumProperty(
            name="Pattern Type",
            description="Type of honeycomb pattern",
            items=[
                ('FULL', 'Full', 'Complete honeycomb pattern'),
                ('RING_ONLY', 'Ring Only', 'Only outer ring'),
                ('ALTERNATING', 'Alternating', 'Alternating hexagons'),
            ],
            default='FULL'
        )
        props_owner.honeycomb_rotation = bpy.props.FloatProperty(
            name="Pattern Rotation",
            description="Rotation of the entire honeycomb pattern",
            default=0.0,
            min=0.0,
            max=math.pi * 2,
            subtype='ANGLE'
        )
        
        # Instance Transform (базовые свойства уже определены в BaseCloner)
        # Randomization (базовые свойства уже определены в BaseCloner)
        # Global Transform (базовые свойства уже определены в BaseCloner)
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Honeycomb Cloner
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            ("Rings", "NodeSocketInt", "INPUT", 3),
            ("Radius", "NodeSocketFloat", "INPUT", 2.0),
            ("Spacing", "NodeSocketFloat", "INPUT", 0.1),
            ("Pattern Rotation", "NodeSocketFloat", "INPUT", 0.0),
        ]
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Honeycomb Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем гексагональную сетку точек
        honeycomb_points = self._create_honeycomb_points(base_nodes)
        
        # Применяем поворот паттерна
        rotated_points = self._apply_pattern_rotation(base_nodes, honeycomb_points)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Honeycomb"
        instance_node.location = (400, 0)
        links.new(rotated_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])
        
        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instance_node.outputs['Instances'])
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry

    def _create_honeycomb_points(self, base_nodes):
        """
        Создание гексагональной сетки точек

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с гексагональными точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Создаем центральную точку (0,0,0)
        center_point = nodes.new('GeometryNodeMeshLine')
        center_point.name = "Center Point"
        center_point.location = (-600, 200)
        center_point.mode = 'END_POINTS'
        center_point.inputs['Count'].default_value = 1
        center_point.inputs['Start Location'].default_value = (0.0, 0.0, 0.0)
        center_point.inputs['End Location'].default_value = (0.0, 0.0, 0.0)

        # Преобразуем в точки
        center_to_points = nodes.new('GeometryNodeMeshToPoints')
        center_to_points.name = "Center to Points"
        center_to_points.location = (-400, 200)
        center_to_points.mode = 'VERTICES'
        links.new(center_point.outputs['Mesh'], center_to_points.inputs['Mesh'])

        # Создаем кольца гексагонов
        rings_geometry = self._create_hexagon_rings(base_nodes)

        # Объединяем центр и кольца
        join_geometry = nodes.new('GeometryNodeJoinGeometry')
        join_geometry.name = "Join Center and Rings"
        join_geometry.location = (-100, 200)
        links.new(center_to_points.outputs['Points'], join_geometry.inputs['Geometry'])
        links.new(rings_geometry, join_geometry.inputs['Geometry'])

        return join_geometry.outputs['Geometry']

    def _create_hexagon_rings(self, base_nodes):
        """
        Создание колец гексагонов вокруг центра
        Упрощенная версия - создаем точки математически

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с кольцами точек
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Создаем Grid точек и преобразуем их в гексагональный паттерн
        grid_points = nodes.new('GeometryNodeMeshGrid')
        grid_points.name = "Grid Base"
        grid_points.location = (-600, 0)

        # Размер сетки зависит от количества колец
        # Для rings=3 нужна сетка примерно 7x7
        rings_to_size = nodes.new('ShaderNodeMath')
        rings_to_size.name = "Rings to Grid Size"
        rings_to_size.location = (-800, 0)
        rings_to_size.operation = 'MULTIPLY'
        rings_to_size.inputs[1].default_value = 2.0
        links.new(group_input.outputs['Rings'], rings_to_size.inputs[0])

        add_one = nodes.new('ShaderNodeMath')
        add_one.name = "Add One"
        add_one.location = (-800, -50)
        add_one.operation = 'ADD'
        add_one.inputs[1].default_value = 3.0
        links.new(rings_to_size.outputs['Value'], add_one.inputs[0])

        links.new(add_one.outputs['Value'], grid_points.inputs['Vertices X'])
        links.new(add_one.outputs['Value'], grid_points.inputs['Vertices Y'])

        # Масштабируем сетку по радиусу
        links.new(group_input.outputs['Radius'], grid_points.inputs['Size X'])
        links.new(group_input.outputs['Radius'], grid_points.inputs['Size Y'])

        # Преобразуем в точки
        grid_to_points = nodes.new('GeometryNodeMeshToPoints')
        grid_to_points.name = "Grid to Points"
        grid_to_points.location = (-400, 0)
        grid_to_points.mode = 'VERTICES'
        links.new(grid_points.outputs['Mesh'], grid_to_points.inputs['Mesh'])

        # Применяем гексагональное смещение к четным рядам
        hex_offset = self._apply_hexagonal_offset(base_nodes, grid_to_points.outputs['Points'])

        # Фильтруем точки по расстоянию от центра (создаем кольца)
        filtered_points = self._filter_points_by_rings(base_nodes, hex_offset)

        return filtered_points

    def _apply_hexagonal_offset(self, base_nodes, grid_points):
        """
        Применение гексагонального смещения к сетке точек
        Четные ряды смещаются на половину шага

        Args:
            base_nodes: Словарь с базовыми нодами
            grid_points: Точки сетки

        Returns:
            NodeSocket: Точки с гексагональным смещением
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем позицию каждой точки
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.name = "Point Position"
        position_node.location = (-200, -100)

        # Извлекаем Y координату
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.name = "Separate Y"
        separate_xyz.location = (0, -100)
        links.new(position_node.outputs['Position'], separate_xyz.inputs['Vector'])

        # Определяем четность ряда (Y координата)
        y_to_row = nodes.new('ShaderNodeMath')
        y_to_row.name = "Y to Row"
        y_to_row.location = (200, -100)
        y_to_row.operation = 'DIVIDE'
        y_to_row.inputs[1].default_value = 1.0  # Шаг сетки
        links.new(separate_xyz.outputs['Y'], y_to_row.inputs[0])

        # Модуло 2 для определения четности
        modulo_two = nodes.new('ShaderNodeMath')
        modulo_two.name = "Modulo 2"
        modulo_two.location = (400, -100)
        modulo_two.operation = 'MODULO'
        modulo_two.inputs[1].default_value = 2.0
        links.new(y_to_row.outputs['Value'], modulo_two.inputs[0])

        # Смещение для четных рядов
        hex_offset_x = nodes.new('ShaderNodeMath')
        hex_offset_x.name = "Hex Offset X"
        hex_offset_x.location = (600, -100)
        hex_offset_x.operation = 'MULTIPLY'
        hex_offset_x.inputs[1].default_value = 0.5  # Половина шага
        links.new(modulo_two.outputs['Value'], hex_offset_x.inputs[0])

        # Применяем смещение
        offset_vector = nodes.new('ShaderNodeCombineXYZ')
        offset_vector.name = "Offset Vector"
        offset_vector.location = (800, -100)
        links.new(hex_offset_x.outputs['Value'], offset_vector.inputs['X'])
        offset_vector.inputs['Y'].default_value = 0.0
        offset_vector.inputs['Z'].default_value = 0.0

        # Применяем смещение к точкам
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Apply Hex Offset"
        set_position.location = (1000, 0)
        links.new(grid_points, set_position.inputs['Geometry'])
        links.new(offset_vector.outputs['Vector'], set_position.inputs['Offset'])

        return set_position.outputs['Geometry']

    def _filter_points_by_rings(self, base_nodes, hex_points):
        """
        Фильтрация точек по расстоянию от центра для создания колец

        Args:
            base_nodes: Словарь с базовыми нодами
            hex_points: Точки с гексагональным смещением

        Returns:
            NodeSocket: Отфильтрованные точки в виде колец
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем позицию каждой точки
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.name = "Point Position for Filter"
        position_node.location = (1200, -100)

        # Вычисляем расстояние от центра
        vector_length = nodes.new('ShaderNodeVectorMath')
        vector_length.name = "Distance from Center"
        vector_length.location = (1400, -100)
        vector_length.operation = 'LENGTH'
        links.new(position_node.outputs['Position'], vector_length.inputs['Vector'])

        # Максимальное расстояние на основе количества колец
        max_distance = nodes.new('ShaderNodeMath')
        max_distance.name = "Max Distance"
        max_distance.location = (1600, -100)
        max_distance.operation = 'MULTIPLY'
        max_distance.inputs[1].default_value = 1.5  # Коэффициент для радиуса
        links.new(group_input.outputs['Rings'], max_distance.inputs[0])

        # Сравниваем расстояние с максимальным
        compare_distance = nodes.new('ShaderNodeMath')
        compare_distance.name = "Compare Distance"
        compare_distance.location = (1800, -100)
        compare_distance.operation = 'LESS_THAN'
        links.new(vector_length.outputs['Value'], compare_distance.inputs[0])
        links.new(max_distance.outputs['Value'], compare_distance.inputs[1])

        # Удаляем точки за пределами колец
        delete_geometry = nodes.new('GeometryNodeDeleteGeometry')
        delete_geometry.name = "Delete Outer Points"
        delete_geometry.location = (2000, 0)
        delete_geometry.domain = 'POINT'
        delete_geometry.mode = 'ALL'
        links.new(hex_points, delete_geometry.inputs['Geometry'])
        links.new(compare_distance.outputs['Value'], delete_geometry.inputs['Selection'])

        return delete_geometry.outputs['Geometry']

    def _apply_pattern_rotation(self, base_nodes, honeycomb_points):
        """
        Применение поворота ко всему паттерну гексагонов

        Args:
            base_nodes: Словарь с базовыми нодами
            honeycomb_points: Точки гексагонального паттерна

        Returns:
            NodeSocket: Повернутые точки
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Поворачиваем весь паттерн
        rotate_pattern = nodes.new('GeometryNodeRotateInstances')
        rotate_pattern.name = "Rotate Honeycomb Pattern"
        rotate_pattern.location = (200, 200)
        links.new(honeycomb_points, rotate_pattern.inputs['Instances'])

        # Создаем вектор поворота из input угла
        combine_rotation = nodes.new('ShaderNodeCombineXYZ')
        combine_rotation.name = "Pattern Rotation Vector"
        combine_rotation.location = (0, 300)
        combine_rotation.inputs['X'].default_value = 0.0
        combine_rotation.inputs['Y'].default_value = 0.0
        links.new(group_input.outputs['Pattern Rotation'], combine_rotation.inputs['Z'])
        links.new(combine_rotation.outputs['Vector'], rotate_pattern.inputs['Rotation'])

        return rotate_pattern.outputs['Instances']


# Экземпляр класса для использования в других модулях
honeycomb_cloner = HoneycombCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Honeycomb Cloner (в новой архитектуре не требуется)"""
    print("✅ Honeycomb Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Honeycomb Cloner (в новой архитектуре не требуется)"""
    print("✅ Honeycomb Cloner: Using class-based architecture, no unregistration needed")
    pass
