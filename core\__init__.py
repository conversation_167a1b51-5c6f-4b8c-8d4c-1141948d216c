"""
ClonerPro Core Module
Центральная система управления компонентами
"""

from . import templates
from . import managers  
from . import services
from . import system
from . import cleanup
from . import core
from . import chain
from . import uuid

__all__ = ['uuid', 'chain', 'templates', 'managers', 'services', 'system', 'cleanup', 'core']

def register():
    """Register core components"""
    system.settings.register()

def unregister():
    """Unregister core components"""
    system.settings.unregister()