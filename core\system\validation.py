"""
Система валидации для ClonerPro
Портировано из advanced_cloners/core/utils/systems/validation_system.py

КРИТИЧНО: Эта система предотвращает краши при некорректных данных
"""

import bpy
from typing import Optional, List, Dict, Any
import time


class ValidationError(Exception):
    """Исключение для ошибок валидации"""
    pass


class ClonerValidator:
    """Валидатор для компонентов клонера"""
    
    @staticmethod
    def validate_object(obj: bpy.types.Object) -> bool:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Валидация объекта Blender
        
        Args:
            obj: Объект для валидации
            
        Returns:
            bool: True если объект валиден
            
        Raises:
            ValidationError: Если объект не валиден
        """
        if not obj:
            raise ValidationError("Объект не может быть None")
        
        if not hasattr(obj, 'type'):
            raise ValidationError("Объект должен иметь атрибут type")
        
        # Проверяем, что объект еще существует в сцене
        try:
            # Пытаемся получить доступ к имени объекта
            _ = obj.name
        except ReferenceError:
            raise ValidationError("Объект больше не существует в памяти Blender")
        
        return True
    
    @staticmethod
    def validate_modifier(modifier: bpy.types.Modifier) -> bool:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Валидация модификатора
        
        Args:
            modifier: Модификатор для валидации
            
        Returns:
            bool: True если модификатор валиден
            
        Raises:
            ValidationError: Если модификатор не валиден
        """
        if not modifier:
            raise ValidationError("Модификатор не может быть None")
        
        if modifier.type != 'NODES':
            raise ValidationError("Модификатор должен быть типа NODES")
        
        if not modifier.node_group:
            raise ValidationError("Модификатор должен иметь node_group")
        
        # Проверяем, что модификатор еще существует
        try:
            _ = modifier.name
        except ReferenceError:
            raise ValidationError("Модификатор больше не существует в памяти Blender")
        
        return True
    
    @staticmethod
    def validate_node_group(node_group: bpy.types.NodeGroup) -> bool:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Валидация группы узлов
        
        Args:
            node_group: Группа узлов для валидации
            
        Returns:
            bool: True если группа узлов валидна
            
        Raises:
            ValidationError: Если группа узлов не валидна
        """
        if not node_group:
            raise ValidationError("Группа узлов не может быть None")
        
        if node_group.type != 'GeometryNodeTree':
            raise ValidationError("Группа узлов должна быть типа GeometryNodeTree")
        
        # Проверяем, что node_group еще существует
        try:
            _ = node_group.name
        except ReferenceError:
            raise ValidationError("Группа узлов больше не существует в памяти Blender")
        
        return True
    
    @staticmethod
    def validate_cloner_parameters(params: Dict[str, Any]) -> bool:
        """
        Валидация параметров клонера
        
        Args:
            params: Словарь параметров для валидации
            
        Returns:
            bool: True если параметры валидны
            
        Raises:
            ValidationError: Если параметры не валидны
        """
        if not isinstance(params, dict):
            raise ValidationError("Параметры должны быть словарем")
        
        # Базовые обязательные параметры для всех клонеров
        required_params = ['Count']
        
        for param in required_params:
            if param not in params:
                raise ValidationError(f"Отсутствует обязательный параметр: {param}")
        
        # Валидация Count параметра
        count = params.get('Count')
        if count is not None:
            if not isinstance(count, (int, float)):
                raise ValidationError("Параметр 'Count' должен быть числом")
            if count < 0:
                raise ValidationError("Параметр 'Count' не может быть отрицательным")
            if count > 10000:  # Разумный лимит для предотвращения зависания
                raise ValidationError("Параметр 'Count' слишком большой (максимум 10000)")
        
        return True
    
    @staticmethod
    def validate_effector_parameters(params: Dict[str, Any]) -> bool:
        """
        Валидация параметров эффектора
        
        Args:
            params: Словарь параметров для валидации
            
        Returns:
            bool: True если параметры валидны
            
        Raises:
            ValidationError: Если параметры не валидны
        """
        if not isinstance(params, dict):
            raise ValidationError("Параметры эффектора должны быть словарем")
        
        # Валидация Strength параметра если присутствует
        strength = params.get('Strength')
        if strength is not None:
            if not isinstance(strength, (int, float)):
                raise ValidationError("Параметр 'Strength' должен быть числом")
            if strength < 0:
                raise ValidationError("Параметр 'Strength' не может быть отрицательным")
        
        return True
    
    @staticmethod
    def validate_scene_context() -> bool:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Валидация контекста сцены Blender
        
        Returns:
            bool: True если контекст валиден
            
        Raises:
            ValidationError: Если контекст не валиден
        """
        if not bpy.context:
            raise ValidationError("Контекст Blender недоступен")
        
        if not bpy.context.scene:
            raise ValidationError("Активная сцена недоступна")
        
        if not bpy.context.view_layer:
            raise ValidationError("Активный view layer недоступен")
        
        # Проверяем доступность dependency graph
        try:
            depsgraph = bpy.context.evaluated_depsgraph_get()
            if not depsgraph:
                raise ValidationError("Dependency graph недоступен")
        except:
            raise ValidationError("Ошибка получения dependency graph")
        
        return True
    
    @staticmethod
    def validate_modifier_on_object(obj: bpy.types.Object, modifier_name: str) -> bool:
        """
        Валидация существования модификатора на объекте
        
        Args:
            obj: Объект для проверки
            modifier_name: Имя модификатора
            
        Returns:
            bool: True если модификатор существует
            
        Raises:
            ValidationError: Если модификатор не найден
        """
        ClonerValidator.validate_object(obj)
        
        if not hasattr(obj, 'modifiers'):
            raise ValidationError(f"Объект {obj.name} не поддерживает модификаторы")
        
        if modifier_name not in obj.modifiers:
            raise ValidationError(f"Модификатор '{modifier_name}' не найден на объекте {obj.name}")
        
        modifier = obj.modifiers[modifier_name]
        ClonerValidator.validate_modifier(modifier)
        
        return True
    
    @staticmethod
    def validate_effector_cloner_compatibility(effector_mod: bpy.types.Modifier, 
                                              cloner_mod: bpy.types.Modifier) -> bool:
        """
        Валидация совместимости эффектора с клонером
        
        Args:
            effector_mod: Модификатор эффектора
            cloner_mod: Модификатор клонера
            
        Returns:
            bool: True если совместимы
            
        Raises:
            ValidationError: Если не совместимы
        """
        ClonerValidator.validate_modifier(effector_mod)
        ClonerValidator.validate_modifier(cloner_mod)
        
        # Проверяем, что это действительно эффектор и клонер
        from .dependency_safety import is_effector_modifier, is_cloner_modifier
        
        if not is_effector_modifier(effector_mod):
            raise ValidationError(f"Модификатор '{effector_mod.name}' не является эффектором")
        
        if not is_cloner_modifier(cloner_mod):
            raise ValidationError(f"Модификатор '{cloner_mod.name}' не является клонером")
        
        # Проверяем, что клонер поддерживает эффекторы
        if not cloner_mod.node_group.get("supports_effector_chains", False):
            raise ValidationError(f"Клонер '{cloner_mod.name}' не поддерживает эффекторы")
        
        return True


class PerformanceMonitor:
    """Монитор производительности для операций ClonerPro"""
    
    def __init__(self):
        self.operation_times = {}
        self._operation_history = []
        self._max_history = 100
    
    def start_operation(self, operation_name: str):
        """
        Начинает отслеживание операции
        
        Args:
            operation_name: Имя операции для отслеживания
        """
        self.operation_times[operation_name] = time.time()
    
    def end_operation(self, operation_name: str) -> Optional[float]:
        """
        Завершает отслеживание операции
        
        Args:
            operation_name: Имя операции
            
        Returns:
            float: Длительность операции в секундах или None
        """
        if operation_name in self.operation_times:
            duration = time.time() - self.operation_times[operation_name]
            
            # Записываем в историю
            operation_record = {
                'name': operation_name,
                'duration': duration,
                'timestamp': time.time()
            }
            self._operation_history.append(operation_record)
            
            # Оставляем только недавние записи
            if len(self._operation_history) > self._max_history:
                self._operation_history = self._operation_history[-self._max_history:]
            
            del self.operation_times[operation_name]
            
            # Предупреждаем о медленных операциях
            if duration > 1.0:  # Больше 1 секунды
                print(f"[WARNING] Медленная операция '{operation_name}' заняла {duration:.3f} секунд")
            else:
                print(f"[DEBUG] Операция '{operation_name}' заняла {duration:.3f} секунд")
            
            return duration
        
        return None
    
    def get_operation_statistics(self) -> Dict[str, Any]:
        """
        Получает статистику производительности
        
        Returns:
            Dict: Статистика операций
        """
        if not self._operation_history:
            return {
                'total_operations': 0,
                'average_duration': 0.0,
                'slowest_operation': None,
                'fastest_operation': None
            }
        
        durations = [op['duration'] for op in self._operation_history]
        
        slowest = max(self._operation_history, key=lambda x: x['duration'])
        fastest = min(self._operation_history, key=lambda x: x['duration'])
        
        return {
            'total_operations': len(self._operation_history),
            'average_duration': sum(durations) / len(durations),
            'slowest_operation': {
                'name': slowest['name'],
                'duration': slowest['duration']
            },
            'fastest_operation': {
                'name': fastest['name'],
                'duration': fastest['duration']
            },
            'recent_operations': self._operation_history[-10:]  # Последние 10 операций
        }
    
    def clear_history(self):
        """Очищает историю операций"""
        self._operation_history.clear()
        print("[DEBUG] Performance history cleared")


class SystemHealthChecker:
    """Проверяет общее здоровье системы ClonerPro"""
    
    @staticmethod
    def check_system_health() -> Dict[str, Any]:
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет общее здоровье системы
        
        Returns:
            Dict: Отчет о здоровье системы
        """
        health_report = {
            'overall_status': 'healthy',
            'issues': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # Проверяем контекст Blender
            try:
                ClonerValidator.validate_scene_context()
            except ValidationError as e:
                health_report['issues'].append(f"Scene context issue: {str(e)}")
                health_report['overall_status'] = 'critical'
            
            # Проверяем количество клонеров и эффекторов
            cloner_count = 0
            effector_count = 0
            broken_modifiers = []
            
            for obj in bpy.data.objects:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    if mod.type == 'NODES' and mod.node_group:
                        try:
                            from .dependency_safety import is_cloner_modifier, is_effector_modifier
                            
                            if is_cloner_modifier(mod):
                                cloner_count += 1
                                # Проверяем целостность клонера
                                try:
                                    ClonerValidator.validate_modifier(mod)
                                    ClonerValidator.validate_node_group(mod.node_group)
                                except ValidationError as e:
                                    broken_modifiers.append(f"Broken cloner {mod.name}: {str(e)}")
                            
                            elif is_effector_modifier(mod):
                                effector_count += 1
                                # Проверяем целостность эффектора
                                try:
                                    ClonerValidator.validate_modifier(mod)
                                    ClonerValidator.validate_node_group(mod.node_group)
                                except ValidationError as e:
                                    broken_modifiers.append(f"Broken effector {mod.name}: {str(e)}")
                        
                        except Exception as e:
                            broken_modifiers.append(f"Error checking modifier {mod.name}: {str(e)}")
            
            # Записываем статистику
            health_report['statistics'] = {
                'cloner_count': cloner_count,
                'effector_count': effector_count,
                'broken_modifiers': len(broken_modifiers)
            }
            
            # Добавляем проблемы в отчет
            health_report['issues'].extend(broken_modifiers)
            
            # Проверяем предупреждения
            if cloner_count > 20:
                health_report['warnings'].append(f"Много клонеров в сцене ({cloner_count}), может влиять на производительность")
            
            if effector_count > 50:
                health_report['warnings'].append(f"Много эффекторов в сцене ({effector_count}), может влиять на производительность")
            
            # Определяем общий статус
            if health_report['issues']:
                health_report['overall_status'] = 'critical' if any('broken' in issue.lower() for issue in health_report['issues']) else 'warning'
            elif health_report['warnings']:
                health_report['overall_status'] = 'warning'
            
        except Exception as e:
            health_report['overall_status'] = 'critical'
            health_report['issues'].append(f"System health check failed: {str(e)}")
        
        return health_report
    
    @staticmethod
    def diagnose_performance_issues() -> List[str]:
        """
        Диагностирует потенциальные проблемы производительности
        
        Returns:
            List[str]: Список обнаруженных проблем
        """
        issues = []
        
        try:
            # Проверяем количество объектов в сцене
            object_count = len(bpy.data.objects)
            if object_count > 1000:
                issues.append(f"Много объектов в сцене ({object_count})")
            
            # Проверяем сложность node groups
            complex_groups = []
            for group in bpy.data.node_groups:
                if group.type == 'GeometryNodeTree' and len(group.nodes) > 50:
                    complex_groups.append(group.name)
            
            if complex_groups:
                issues.append(f"Сложные node groups обнаружены: {', '.join(complex_groups[:5])}")
            
            # Проверяем количество модификаторов на объектах
            heavy_objects = []
            for obj in bpy.data.objects:
                if hasattr(obj, 'modifiers') and len(obj.modifiers) > 10:
                    heavy_objects.append(obj.name)
            
            if heavy_objects:
                issues.append(f"Объекты с множественными модификаторами: {', '.join(heavy_objects[:5])}")
            
        except Exception as e:
            issues.append(f"Ошибка диагностики производительности: {str(e)}")
        
        return issues


# Глобальные экземпляры
performance_monitor = PerformanceMonitor()
validator = ClonerValidator()
health_checker = SystemHealthChecker()


# Удобные функции для быстрого использования
def validate_operation_context():
    """Быстрая валидация контекста для операций"""
    return validator.validate_scene_context()


def validate_component(component_type: str, component) -> bool:
    """
    Валидирует компонент по типу
    
    Args:
        component_type: Тип компонента ('object', 'modifier', 'node_group')
        component: Компонент для валидации
        
    Returns:
        bool: True если компонент валиден
    """
    if component_type == 'object':
        return validator.validate_object(component)
    elif component_type == 'modifier':
        return validator.validate_modifier(component)
    elif component_type == 'node_group':
        return validator.validate_node_group(component)
    else:
        raise ValidationError(f"Неизвестный тип компонента: {component_type}")


def timed_operation(operation_name: str):
    """
    Декоратор для измерения времени операций
    
    Args:
        operation_name: Имя операции
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            performance_monitor.start_operation(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                performance_monitor.end_operation(operation_name)
        return wrapper
    return decorator


def quick_health_check() -> str:
    """
    Быстрая проверка здоровья системы
    
    Returns:
        str: Статус системы
    """
    health = health_checker.check_system_health()
    return health['overall_status']


# Публичный API
__all__ = [
    'ValidationError',
    'ClonerValidator',
    'PerformanceMonitor',
    'SystemHealthChecker',
    'performance_monitor',
    'validator',
    'health_checker',
    'validate_operation_context',
    'validate_component',
    'timed_operation',
    'quick_health_check'
]