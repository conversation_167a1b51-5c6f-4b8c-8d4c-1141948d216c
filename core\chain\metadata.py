"""
Chain Metadata - Управление метаданными цепей
"""

# Legacy функции удалены, используем UUID систему
from .utils import chain_debug_print


def prepare_uuid_chain_info(cloner_obj, cloner_modifier, cloner_info):
    """Подготовить chain info для UUID системы"""
    try:
        from ..uuid.manager import BlenderClonerUUIDManager
        
        cloner_uuid = cloner_info.get("cloner_uuid", "")
        chain_uuid = cloner_info.get("chain_uuid", "")
        previous_uuid = cloner_info.get("previous_cloner_uuid", "")
        next_uuids = cloner_info.get("next_cloner_uuids", "").split(",")
        next_uuids = [uuid.strip() for uuid in next_uuids if uuid.strip()]
        
        # Находим объекты по UUID
        previous_cloner_obj = None
        if previous_uuid:
            prev_obj, prev_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(previous_uuid)
            previous_cloner_obj = prev_obj
        
        next_cloners_objs = []
        for next_uuid in next_uuids:
            next_obj, next_modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(next_uuid)
            if next_obj:
                next_cloners_objs.append(next_obj)
        
        # Получаем всю цепь
        all_chain_cloners = []
        if chain_uuid:
            all_chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
        
        # Определяем, является ли частью цепи (любой клонер с chain_uuid или связями)
        is_part_of_chain = bool(chain_uuid or previous_uuid or next_uuids)
        
        # НОВОЕ: Получаем текущий источник клонера ДО удаления
        current_cloner_source = None
        try:
            from .source_discovery import get_current_cloner_source
            current_cloner_source = get_current_cloner_source(cloner_obj, cloner_modifier)
            if current_cloner_source:
                print(f"✅ [CHAIN] Captured current source before deletion: {current_cloner_source.name}")
            else:
                print(f"⚠️ [CHAIN] No current source found before deletion")
        except Exception as e:
            print(f"⚠️ [CHAIN] Error capturing current source: {e}")

        chain_info = {
            "cloner_obj": cloner_obj,  # ДОБАВЛЕНО: сам объект клонера
            "cloner_modifier": cloner_modifier,  # ДОБАВЛЕНО: модификатор клонера
            "current_source": current_cloner_source,  # НОВОЕ: текущий источник ДО удаления
            "deleted_cloner_name": cloner_obj.name,
            "deleted_cloner_uuid": cloner_uuid,
            "deleted_cloner_index": cloner_info.get("chain_sequence", 0),
            "is_chained": is_part_of_chain,  # UUID: цепь определяется наличием связей
            "has_uuid": True,
            "chain_uuid": chain_uuid,
            "previous_cloner_uuid": previous_uuid,
            "next_cloner_uuids": next_uuids,
            "previous_cloner_name": previous_cloner_obj.name if previous_cloner_obj else "",
            "next_cloners_names": [obj.name for obj in next_cloners_objs],
            "chain_source_object": cloner_info.get("chain_source_object", ""),
            "chain_source_collection": cloner_info.get("chain_source_collection", ""),
            "chain_source_uuid": cloner_info.get("chain_source_uuid", ""),  # НОВОЕ: UUID источника
            "all_chain_cloners_names": [obj.name for obj, mod in all_chain_cloners]
        }
        
        chain_debug_print(2, f"🔗 [CHAIN] UUID chain info prepared:")
        print(f"    UUID: {cloner_uuid}")
        print(f"    Chain UUID: {chain_uuid}")
        print(f"    Previous: {previous_uuid} ({chain_info['previous_cloner_name'] or 'None'})")
        print(f"    Next: {len(next_uuids)} ({len(chain_info['next_cloners_names'])} found)")
        print(f"    Is chained: {is_part_of_chain}")
        print(f"    Source UUID: {chain_info['chain_source_uuid'] or 'None'}")
        print(f"    Raw cloner_info chain_source_uuid: {cloner_info.get('chain_source_uuid', 'NOT_FOUND')}")
        if current_cloner_source:
            print(f"    Current source: {current_cloner_source.name}")
            current_source_uuid = current_cloner_source.get("source_uuid", "NO_UUID") if hasattr(current_cloner_source, 'get') else "NO_GET_METHOD"
            print(f"    Current source UUID: {current_source_uuid}")
        
        return chain_info
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available, falling back to legacy")
        return None




def update_chain_metadata_after_deletion(cloner_obj, new_previous_cloner, chain_info):
    """Обновить метаданные цепи после удаления клонера"""
    from ...core.core import set_cloner_metadata, get_cloner_modifier, get_cloner_info
    
    try:
        print(f"🔗 [CHAIN] Updating chain metadata for {cloner_obj.name}")
        
        # Получаем модификатор следующего клонера
        next_modifier = get_cloner_modifier(cloner_obj)
        if not next_modifier:
            print(f"⚠️ [CHAIN] No modifier found for {cloner_obj.name}")
            return False
        
        # Получаем информацию о следующем клонере
        next_cloner_info = get_cloner_info(next_modifier)
        if not next_cloner_info:
            print(f"⚠️ [CHAIN] No cloner info found for {cloner_obj.name}")
            return False
        
        # Обновляем индекс цепи (сдвигаем на один назад)
        old_index = next_cloner_info.get("cloner_index", 0)
        new_index = max(0, old_index - 1)
        
        # Обновляем источник цепи на новый предыдущий клонер
        new_chain_source = ""
        if new_previous_cloner:
            new_chain_source = new_previous_cloner.name
        else:
            # Если нет предыдущего клонера, используем исходный источник из chain_info
            new_chain_source = chain_info.get("chain_source_object", "")
        
        # Устанавливаем новые метаданные
        metadata_updates = {
            "cloner_index": new_index,
            "chain_source_object": new_chain_source,
            "chain_source_collection": chain_info.get("chain_source_collection", "")
        }
        
        set_cloner_metadata(next_modifier, **metadata_updates)
        
        print(f"✅ [CHAIN] Updated chain metadata for {cloner_obj.name}:")
        print(f"    New index: {new_index} (was {old_index})")
        print(f"    New source: {new_chain_source}")
        
        return True
        
    except Exception as e:
        print(f"❌ [CHAIN] Error updating chain metadata: {e}")
        return False


def ensure_chain_source_uuid_propagation(start_uuid, source_obj, chain_info):
    """Обеспечить распространение UUID источника по всей цепи"""
    try:
        from ..uuid.manager import BlenderClonerUUIDManager
        from .source_discovery import get_or_create_source_uuid
        
        chain_uuid = chain_info.get("chain_uuid", "")
        if not chain_uuid:
            print("⚠️ [CHAIN] No chain UUID for source propagation")
            return
        
        # Получаем или создаем UUID для источника
        source_uuid = get_or_create_source_uuid(source_obj)
        if not source_uuid:
            print("⚠️ [CHAIN] Could not get/create source UUID")
            return
        
        print(f"🔗 [CHAIN] Propagating source UUID {source_uuid} through chain {chain_uuid}")
        
        # Находим всех клонеров в цепи
        chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
        
        # Обновляем chain_source_uuid у всех клонеров в цепи
        updated_count = 0
        for cloner_obj, cloner_modifier in chain_cloners:
            try:
                cloner_modifier["chain_source_uuid"] = source_uuid
                print(f"✓ [CHAIN] Updated source UUID for {cloner_obj.name}")
                updated_count += 1
            except Exception as e:
                print(f"⚠️ [CHAIN] Error updating source UUID for {cloner_obj.name}: {e}")
        
        print(f"✅ [CHAIN] Source UUID propagation complete: {updated_count}/{len(chain_cloners)} cloners updated")
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available for source propagation")
    except Exception as e:
        print(f"❌ [CHAIN] Error in source UUID propagation: {e}")