"""
Effector Creation Manager для ClonerPro
Прямая логика создания модификаторов эффекторов БЕЗ фабрик и абстракций
"""

import bpy
from ...components.effectors.random import create_random_effector
from ...components.effectors.noise import create_noise_effector
from ...config.effectors.random_config import get_random_effector_parameters
from ...config.effectors.noise_config import get_noise_effector_parameters
from ..system.error_handling import safe_execute_operation, component_creation_safe
from ..system.validation import validate_operation_context, validate_component
from ..system.dependency_safety import safe_execute_effector_operation


@component_creation_safe
def create_random_effector_modifier(context, obj, effector_type="RANDOM"):
    """
    🛡️ БЕЗОПАСНОЕ создание Random Effector модификатора на объекте
    Прямая реализация с интегрированными системами безопасности
    """
    # 🛡️ КРИТИЧЕСКАЯ ВАЛИДАЦИЯ КОНТЕКСТА
    validate_operation_context()
    validate_component('object', obj)
    
    # 🛡️ БЕЗОПАСНОЕ ВЫПОЛНЕНИЕ ОПЕРАЦИИ С ЭФФЕКТОРОМ
    success, result = safe_execute_effector_operation(_create_random_effector_modifier_internal, context, obj, effector_type)
    
    if not success:
        print(f"[ERROR] Failed to create random effector: {result}")
        return None, result
    
    return result


def _create_random_effector_modifier_internal(context, obj, effector_type="RANDOM"):
    """
    Внутренняя функция создания Random Effector модификатора
    Выполняется внутри безопасного контекста
    """
    try:
        # 1. Создаем node group для Random Effector
        node_group = create_random_effector()
        
        if not node_group:
            return None, "Could not create Random Effector node group"
        
        # Removed debug log
        
        # 2. Создаем уникальное имя модификатора
        base_mod_name = "Random Effector"
        mod_name = base_mod_name
        counter = 1
        while mod_name in obj.modifiers:
            mod_name = f"{base_mod_name}.{counter:03d}"
            counter += 1
        
        # Removed debug log
        
        # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем ВСЕ старые handlers для этого имени
        # Это предотвращает краши при пересоздании эффектора с тем же именем
        _force_cleanup_effector_handlers(mod_name)
        
        # 3. Создаем модификатор
        modifier = obj.modifiers.new(name=mod_name, type='NODES')
        modifier.node_group = node_group
        
        # 4. Добавляем метаданные
        modifier["architecture_type"] = "standard"
        modifier["effector_type"] = effector_type
        
        # 5. Временно отключаем эффектор и устанавливаем нулевые параметры
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                try:
                    if socket.name == "Enable":
                        modifier[socket.identifier] = False
                    elif socket.name == "Position" or socket.name == "Rotation" or socket.name == "Scale":
                        # Устанавливаем нулевые значения для трансформаций
                        modifier[socket.identifier] = (0.0, 0.0, 0.0)
                    elif socket.name == "Strength":
                        modifier[socket.identifier] = 1.0
                    elif socket.name == "Uniform Scale":
                        modifier[socket.identifier] = True
                    elif socket.name == "Seed":
                        modifier[socket.identifier] = 0
                except Exception as e:
                    print(f"Warning: Could not set parameter {socket.name}: {e}")
        
        # 6. Отключаем модификатор в viewport до настройки
        modifier.show_viewport = False
        
        # 7. Устанавливаем параметры из конфигурации
        set_effector_default_parameters(modifier, effector_type)
        
        print(f"✅ Created {effector_type} effector modifier: {mod_name}")
        return modifier, None
        
    except Exception as e:
        error_msg = f"Error creating Random Effector: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg


@component_creation_safe
def create_noise_effector_modifier(context, obj, effector_type="NOISE"):
    """
    🛡️ БЕЗОПАСНОЕ создание Noise Effector модификатора на объекте
    Прямая реализация с интегрированными системами безопасности
    """
    # 🛡️ КРИТИЧЕСКАЯ ВАЛИДАЦИЯ КОНТЕКСТА
    validate_operation_context()
    validate_component('object', obj)
    
    # 🛡️ БЕЗОПАСНОЕ ВЫПОЛНЕНИЕ ОПЕРАЦИИ С ЭФФЕКТОРОМ
    success, result = safe_execute_effector_operation(_create_noise_effector_modifier_internal, context, obj, effector_type)
    
    if not success:
        print(f"[ERROR] Failed to create noise effector: {result}")
        return None, result
    
    return result


def _create_noise_effector_modifier_internal(context, obj, effector_type="NOISE"):
    """
    Внутренняя функция создания Noise Effector модификатора
    Выполняется внутри безопасного контекста
    """
    try:
        # 1. Создаем node group для Noise Effector
        node_group = create_noise_effector()
        
        if not node_group:
            return None, "Could not create Noise Effector node group"
        
        # Removed debug log
        
        # 2. Создаем уникальное имя модификатора
        base_mod_name = "Noise Effector"
        mod_name = base_mod_name
        counter = 1
        while mod_name in obj.modifiers:
            mod_name = f"{base_mod_name}.{counter:03d}"
            counter += 1
        
        # Removed debug log
        
        # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем ВСЕ старые handlers для этого имени
        # Это предотвращает краши при пересоздании эффектора с тем же именем
        _force_cleanup_effector_handlers(mod_name)
        
        # 3. Создаем модификатор
        modifier = obj.modifiers.new(name=mod_name, type='NODES')
        modifier.node_group = node_group
        
        # 4. Добавляем метаданные
        modifier["architecture_type"] = "standard"
        modifier["effector_type"] = effector_type
        
        # 5. Временно отключаем эффектор и устанавливаем нулевые параметры
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                try:
                    if socket.name == "Enable":
                        modifier[socket.identifier] = False
                    elif socket.name == "Position" or socket.name == "Rotation" or socket.name == "Scale":
                        # Устанавливаем нулевые значения для трансформаций
                        modifier[socket.identifier] = (0.0, 0.0, 0.0)
                    elif socket.name == "Strength":
                        modifier[socket.identifier] = 1.0
                    elif socket.name == "Uniform Scale":
                        modifier[socket.identifier] = True
                    elif socket.name == "Symmetric Translation":
                        modifier[socket.identifier] = False
                    elif socket.name == "Symmetric Rotation":
                        modifier[socket.identifier] = False
                    elif socket.name == "Noise Scale":
                        modifier[socket.identifier] = 0.5
                    elif socket.name == "Noise Detail":
                        modifier[socket.identifier] = 2.0
                    elif socket.name == "Noise Roughness":
                        modifier[socket.identifier] = 0.5
                    elif socket.name == "Noise Lacunarity":
                        modifier[socket.identifier] = 2.0
                    elif socket.name == "Noise Distortion":
                        modifier[socket.identifier] = 0.0
                    elif socket.name == "Noise Position":
                        modifier[socket.identifier] = (0.0, 0.0, 0.0)
                    elif socket.name == "Noise XYZ Scale":
                        modifier[socket.identifier] = (1.0, 1.0, 1.0)
                    elif socket.name == "Speed":
                        modifier[socket.identifier] = 0.0
                    elif socket.name == "Seed":
                        modifier[socket.identifier] = 0
                except Exception as e:
                    print(f"Warning: Could not set parameter {socket.name}: {e}")
        
        # 6. Отключаем модификатор в viewport до настройки
        modifier.show_viewport = False
        
        # 7. Устанавливаем параметры из конфигурации
        set_effector_default_parameters(modifier, effector_type)
        
        print(f"✅ Created {effector_type} effector modifier: {mod_name}")
        return modifier, None
        
    except Exception as e:
        error_msg = f"Error creating Noise Effector: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg


def _force_cleanup_effector_handlers(effector_name):
    """
    КРИТИЧЕСКИ ВАЖНАЯ функция: Принудительная очистка ВСЕХ handlers для эффектора.
    
    Предотвращает краши rna_NodeTree_refine при пересоздании эффектора с тем же именем.
    """
    try:
        print(f"[FORCE CLEANUP] Очищаем ВСЕ handlers для {effector_name}")
        
        # 1. Очищаем depsgraph_update_post handlers
        handlers_removed = 0
        handlers_to_remove = []
        
        for handler in bpy.app.handlers.depsgraph_update_post:
            try:
                # Проверяем по имени эффектора
                if (hasattr(handler, 'effector_name') and 
                    handler.effector_name == effector_name):
                    handlers_to_remove.append(handler)
                # Проверяем по содержанию в __name__ или __repr__
                elif (hasattr(handler, '__name__') and 
                      effector_name in str(handler.__name__)):
                    handlers_to_remove.append(handler)
                # Проверяем в строковом представлении handler
                elif effector_name in str(handler):
                    handlers_to_remove.append(handler)
            except (AttributeError, ReferenceError, TypeError):
                # Handler поврежден или содержит невалидные ссылки - удаляем
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.depsgraph_update_post.remove(handler)
                handlers_removed += 1
                print(f"[FORCE CLEANUP] Удален depsgraph handler #{handlers_removed}")
            except (ValueError, ReferenceError):
                pass
        
        # 2. Очищаем frame_change_post handlers
        handlers_to_remove = []
        
        for handler in bpy.app.handlers.frame_change_post:
            try:
                if (hasattr(handler, 'effector_name') and 
                    handler.effector_name == effector_name):
                    handlers_to_remove.append(handler)
                elif effector_name in str(handler):
                    handlers_to_remove.append(handler)
            except (AttributeError, ReferenceError, TypeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.frame_change_post.remove(handler)
                handlers_removed += 1
                print(f"[FORCE CLEANUP] Удален frame_change handler #{handlers_removed}")
            except (ValueError, ReferenceError):
                pass
        
        # 3. КРИТИЧНО: Дополнительно очищаем все поврежденные handlers
        _cleanup_corrupted_handlers()
        
        print(f"[FORCE CLEANUP] ✅ Удалено {handlers_removed} handlers для {effector_name}")
        
    except Exception as e:
        print(f"[FORCE CLEANUP] Ошибка принудительной очистки handlers: {e}")
        # При ошибке выполняем экстренную очистку
        try:
            from ..system.safe_operations import emergency_cleanup_invalid_references
            emergency_cleanup_invalid_references()
        except:
            pass


def _cleanup_corrupted_handlers():
    """Дополнительная очистка поврежденных handlers"""
    try:
        # Очищаем depsgraph handlers с ReferenceError
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            try:
                # Пытаемся получить доступ к handler - если он поврежден, будет ошибка
                _ = str(handler)
                if hasattr(handler, 'effector_name'):
                    _ = handler.effector_name  # Может вызвать ReferenceError
            except (ReferenceError, AttributeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.depsgraph_update_post.remove(handler)
                print(f"[FORCE CLEANUP] Удален поврежденный depsgraph handler")
            except:
                pass
        
        # Очищаем frame_change handlers с ReferenceError
        handlers_to_remove = []
        for handler in bpy.app.handlers.frame_change_post:
            try:
                _ = str(handler)
                if hasattr(handler, 'effector_name'):
                    _ = handler.effector_name
            except (ReferenceError, AttributeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.frame_change_post.remove(handler)
                print(f"[FORCE CLEANUP] Удален поврежденный frame_change handler")
            except:
                pass
                
    except Exception as e:
        print(f"[FORCE CLEANUP] Ошибка очистки поврежденных handlers: {e}")


def set_effector_default_parameters(modifier, effector_type):
    """
    Установить параметры эффектора по умолчанию из конфигурации
    """
    try:
        if effector_type == "RANDOM":
            params = get_random_effector_parameters()
        elif effector_type == "NOISE":
            params = get_noise_effector_parameters()
        else:
            print(f"Warning: Unknown effector type {effector_type}")
            return
            
        # Устанавливаем значения по умолчанию из конфигурации
        for group_name, parameters in params.items():
            for param in parameters:
                socket_name = param["socket_name"]
                default_value = param["default"]
                
                # Находим сокет в node group
                if modifier.node_group:
                    for socket in modifier.node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == socket_name:
                            try:
                                modifier[socket.identifier] = default_value
                                print(f"Set {socket_name} = {default_value}")
                            except Exception as e:
                                print(f"Warning: Could not set {socket_name}: {e}")
                            break
        
        print(f"✅ Установлены параметры по умолчанию для {effector_type} эффектора")
            
    except Exception as e:
        print(f"Warning: Could not set default parameters: {e}")


def check_object_has_cloners(obj):
    """
    Проверить, есть ли клонеры на объекте
    """
    has_cloner = False
    
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            # Проверяем по имени node group
            node_group_name = mod.node_group.name
            cloner_patterns = [
                "GridCloner3D_Advanced",
                "GridClonerStacked", 
                "GridClonerCollection"
            ]
            
            for pattern in cloner_patterns:
                if pattern in node_group_name:
                    has_cloner = True
                    break
            
            if has_cloner:
                break
    
    return has_cloner


def create_effector_for_object(context, effector_type="RANDOM"):
    """
    Создать эффектор для активного объекта - главная функция
    """
    obj = context.active_object
    
    if not obj:
        return None, "No active object selected"
    
    # Проверяем наличие клонеров
    has_cloner = check_object_has_cloners(obj)
    warning_msg = None
    
    if not has_cloner:
        warning_msg = "This object has no cloners. Effectors only work with cloners."
    
    # Создаем эффектор в зависимости от типа
    if effector_type == "RANDOM":
        modifier, error = create_random_effector_modifier(context, obj, effector_type)
    elif effector_type == "NOISE":
        modifier, error = create_noise_effector_modifier(context, obj, effector_type)
    else:
        return None, f"Effector type {effector_type} not implemented yet"
    
    if error:
        return None, error
    
    if not has_cloner and modifier:
        # Предупреждение, но не ошибка
        return modifier, warning_msg
    
    return modifier, None