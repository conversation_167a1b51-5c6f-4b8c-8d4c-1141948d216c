"""
Fields Panel для ClonerPro
Точная копия интерфейса филдов из Advanced Cloners
"""

import bpy
from bpy.types import Panel


class FIELD_PT_main_panel(Panel):
    """Панель филдов - точная копия advanced_cloners"""
    bl_label = "Fields"
    bl_idname = "FIELD_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 2
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout
        
        # 2x3 сетка кнопок создания филдов
        self.draw_field_creation_grid(layout)
        
        # Список существующих филдов (если есть)
        self.draw_field_list(layout, context)

    def draw_field_creation_grid(self, layout):
        """2x3 сетка кнопок создания филдов"""
        
        # Первый ряд: Sphere, Box
        row1 = layout.row(align=True)
        row1.scale_y = 1.2
        
        sphere_op = row1.operator("clonerpro.create_field", text="Sphere", icon="MESH_UVSPHERE")
        sphere_op.component_type = "SPHERE"
        
        box_op = row1.operator("clonerpro.create_field", text="Box", icon="MESH_CUBE")
        box_op.component_type = "BOX"
        
        # Второй ряд: Ring, Object
        row2 = layout.row(align=True)
        row2.scale_y = 1.2
        
        ring_op = row2.operator("clonerpro.create_field", text="Ring", icon="MESH_TORUS")
        ring_op.component_type = "RING"
        
        # В оригинале есть Object field, но в параметрах его нет, заменяем на заглушку
        object_op = row2.operator("clonerpro.create_field", text="Object", icon="OBJECT_DATA")
        object_op.component_type = "OBJECT"
        
        # Третий ряд: Noise, Random
        row3 = layout.row(align=True)
        row3.scale_y = 1.2
        
        noise_op = row3.operator("clonerpro.create_field", text="Noise", icon="FORCE_TURBULENCE")
        noise_op.component_type = "NOISE"
        
        random_op = row3.operator("clonerpro.create_field", text="Random", icon="FORCE_HARMONIC")
        random_op.component_type = "RANDOM"

    def draw_field_list(self, layout, context):
        """Список существующих филдов"""
        fields = self.get_scene_fields(context)
        
        if not fields:
            return
        
        # Заголовок списка
        layout.separator()
        list_header = layout.row()
        list_header.label(text=f"Fields: {len(fields)}", icon="OUTLINER_DATA_LIGHTPROBE")
        
        # Отрисовка каждого филда
        for field in fields:
            self.draw_individual_field(layout, field)

    def draw_individual_field(self, layout, field):
        """Отрисовка индивидуального филда"""
        field_box = layout.box()
        
        # Заголовок филда
        header_row = field_box.row(align=True)
        
        # Expand/collapse треугольник
        header_row.label(text="", icon='TRIA_DOWN')  # TODO: состояние развернут/свернут
        
        # Название филда
        header_row.label(text=f"{field['type']} Field")
        
        # Видимость
        vis_icon = "HIDE_OFF" if field.get('visible', True) else "HIDE_ON"
        header_row.operator("clonerpro.toggle_component_visibility", text="", icon=vis_icon, emboss=False)
        
        # Control buttons
        controls = header_row.row(align=True)
        controls.scale_x = 0.8
        controls.operator("clonerpro.move_field_up", text="", icon="TRIA_UP", emboss=False)
        controls.operator("clonerpro.move_field_down", text="", icon="TRIA_DOWN", emboss=False)
        controls.operator("clonerpro.delete_component", text="", icon="X", emboss=False)
        
        # TODO: Параметры филда (когда развернут)

    def get_scene_fields(self, context):
        """Получить все филды из сцены"""
        fields = []
        
        # Поиск объектов с филдами
        for obj in context.scene.objects:
            for modifier in obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    field_type = self.detect_field_type(modifier.node_group.name)
                    if field_type:
                        fields.append({
                            'name': f"{field_type} Field",
                            'type': field_type,
                            'object': obj.name,
                            'modifier': modifier.name,
                            'visible': modifier.show_viewport
                        })
        
        return fields

    def detect_field_type(self, node_group_name):
        """Определить тип филда по имени node group"""
        name_lower = node_group_name.lower()
        
        if 'sphere' in name_lower and 'field' in name_lower:
            return 'Sphere'
        elif 'box' in name_lower and 'field' in name_lower:
            return 'Box'
        elif 'ring' in name_lower and 'field' in name_lower:
            return 'Ring'
        elif 'noise' in name_lower and 'field' in name_lower:
            return 'Noise'
        elif 'random' in name_lower and 'field' in name_lower:
            return 'Random'
        elif 'object' in name_lower and 'field' in name_lower:
            return 'Object'
        
        return None


# Операторы для управления филдами
class CLONERPRO_OT_move_field_up(bpy.types.Operator):
    """Переместить филд вверх"""
    bl_idname = "clonerpro.move_field_up"
    bl_label = "Move Field Up"
    bl_description = "Move field up in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved field up")
        return {'FINISHED'}


class CLONERPRO_OT_move_field_down(bpy.types.Operator):
    """Переместить филд вниз"""
    bl_idname = "clonerpro.move_field_down"
    bl_label = "Move Field Down"
    bl_description = "Move field down in list"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        self.report({'INFO'}, "Moved field down")
        return {'FINISHED'}


def register():
    """Регистрация панели филдов"""
    bpy.utils.register_class(CLONERPRO_OT_move_field_up)
    bpy.utils.register_class(CLONERPRO_OT_move_field_down)
    bpy.utils.register_class(FIELD_PT_main_panel)


def unregister():
    """Отмена регистрации панели филдов"""
    bpy.utils.unregister_class(FIELD_PT_main_panel)
    bpy.utils.unregister_class(CLONERPRO_OT_move_field_down)
    bpy.utils.unregister_class(CLONERPRO_OT_move_field_up)