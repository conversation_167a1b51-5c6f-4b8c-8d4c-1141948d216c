"""
Cloner UI Operators для ClonerPro - ТОЧНЫЕ паттерны из advanced_cloners
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty
from ...ui.utils.ui_helpers import is_element_expanded, set_element_expanded


class CLONERPRO_OT_toggle_cloner_expanded(Operator):
    """Toggle expanded state of a cloner - ТОЧНАЯ КОПИЯ"""
    bl_idname = "clonerpro.toggle_cloner_expanded"
    bl_label = "Toggle Cloner Expanded"
    bl_description = "Toggle expanded state of cloner parameters"
    bl_options = {'REGISTER'}

    obj_name: StringProperty()
    modifier_name: StringProperty()

    def execute(self, context):
        current = is_element_expanded(context, self.obj_name, self.modifier_name, "cloner_expanded_states")
        set_element_expanded(context, self.obj_name, self.modifier_name, not current, "cloner_expanded_states")
        return {'FINISHED'}


class CLONERPRO_OT_select_cloner(Operator):
    """Select specific cloner - ТОЧНАЯ КОПИЯ"""
    bl_idname = "clonerpro.select_cloner" 
    bl_label = "Select Cloner"
    bl_description = "Select and activate specific cloner"
    bl_options = {'REGISTER'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        if self.object_name in bpy.data.objects:
            obj = bpy.data.objects[self.object_name]
            
            # Проверяем, что объект доступен в текущем view layer
            if obj.name not in context.view_layer.objects:
                self.report({'ERROR'}, f"Object {self.object_name} is not in current view layer")
                return {'CANCELLED'}
            
            # Clear selection
            bpy.ops.object.select_all(action='DESELECT')
            
            # Select object
            obj.select_set(True)
            context.view_layer.objects.active = obj
            
            self.report({'INFO'}, f"Selected {self.object_name}")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}


class CLONERPRO_OT_toggle_component_visibility(Operator):
    """Toggle component visibility"""
    bl_idname = "clonerpro.toggle_component_visibility"
    bl_label = "Toggle Visibility"
    bl_description = "Toggle component visibility"
    bl_options = {'REGISTER'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")

    def execute(self, context):
        if self.object_name in bpy.data.objects:
            obj = bpy.data.objects[self.object_name]
            if self.modifier_name in obj.modifiers:
                modifier = obj.modifiers[self.modifier_name]
                modifier.show_viewport = not modifier.show_viewport
                self.report({'INFO'}, f"Toggled visibility for {self.modifier_name}")
                return {'FINISHED'}
        
        self.report({'ERROR'}, "Component not found")
        return {'CANCELLED'}


class CLONERPRO_OT_delete_component(Operator):
    """Delete component"""
    bl_idname = "clonerpro.delete_component"
    bl_label = "Delete Component"
    bl_description = "Delete component"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")

    def execute(self, context):
        if self.object_name in bpy.data.objects:
            obj = bpy.data.objects[self.object_name]
            if self.modifier_name in obj.modifiers:
                modifier = obj.modifiers[self.modifier_name]
                obj.modifiers.remove(modifier)
                self.report({'INFO'}, f"Deleted {self.modifier_name}")
                return {'FINISHED'}
        
        self.report({'ERROR'}, "Component not found")
        return {'CANCELLED'}


class CLONERPRO_OT_refresh_browser(Operator):
    """Refresh browser"""
    bl_idname = "clonerpro.refresh_browser"
    bl_label = "Refresh Browser"
    bl_description = "Refresh cloner browser"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # Принудительное обновление UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        self.report({'INFO'}, "Browser refreshed")
        return {'FINISHED'}


class CLONERPRO_OT_expand_all_groups(Operator):
    """Expand all groups"""
    bl_idname = "clonerpro.expand_all_groups"
    bl_label = "Expand All Groups"
    bl_description = "Expand all cloner groups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # TODO: Implement expand all functionality
        self.report({'INFO'}, "Expanded all groups")
        return {'FINISHED'}


class CLONERPRO_OT_collapse_all_groups(Operator):
    """Collapse all groups"""
    bl_idname = "clonerpro.collapse_all_groups"
    bl_label = "Collapse All Groups"
    bl_description = "Collapse all cloner groups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # TODO: Implement collapse all functionality
        self.report({'INFO'}, "Collapsed all groups")
        return {'FINISHED'}


class CLONERPRO_OT_set_socket_value(Operator):
    """Set socket value for modifier"""
    bl_idname = "clonerpro.set_socket_value"
    bl_label = "Set Socket Value"
    bl_description = "Set specific socket value for modifier"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    socket_name: StringProperty(name="Socket Name")
    socket_value: StringProperty(name="Socket Value")

    def execute(self, context):
        if self.object_name in bpy.data.objects:
            obj = bpy.data.objects[self.object_name]
            if self.modifier_name in obj.modifiers:
                modifier = obj.modifiers[self.modifier_name]
                
                # Найти socket по имени
                if modifier.node_group:
                    socket_id = self._find_socket_id(modifier.node_group, self.socket_name)
                    if socket_id:
                        try:
                            # Преобразуем значение в нужный тип
                            value = self._convert_value(self.socket_value)
                            modifier[socket_id] = value
                            
                            # ВАЖНО: Принудительное обновление UI (как в оригинальном аддоне)
                            self._force_ui_update(context)
                            
                            self.report({'INFO'}, f"Set {self.socket_name} to {value}")
                            return {'FINISHED'}
                        except Exception as e:
                            self.report({'ERROR'}, f"Failed to set value: {e}")
                            return {'CANCELLED'}
        
        self.report({'ERROR'}, "Socket not found")
        return {'CANCELLED'}
    
    def _find_socket_id(self, node_group, socket_name):
        """Найти ID сокета по имени"""
        for item in node_group.interface.items_tree:
            if (item.item_type == 'SOCKET' and 
                item.in_out == 'INPUT' and 
                item.name == socket_name):
                return item.identifier
        return None
    
    def _convert_value(self, value_str):
        """Преобразовать строковое значение в нужный тип"""
        try:
            # Пытаемся преобразовать в int
            return int(value_str)
        except ValueError:
            try:
                # Пытаемся преобразовать в float
                return float(value_str)
            except ValueError:
                # Возвращаем как строку
                return value_str
    
    def _force_ui_update(self, context):
        """Принудительное обновление UI - ТОЧНАЯ КОПИЯ из ObjectCloner_Standalone"""
        try:
            # Найти модификатор для агрессивного обновления
            if context.active_object:
                cloner_modifier = None
                for modifier in context.active_object.modifiers:
                    if modifier.type == 'NODES' and modifier.node_group:
                        if ("ObjectCloner" in modifier.node_group.name or 
                            "SplineCloner" in modifier.node_group.name):
                            cloner_modifier = modifier
                            break
                
                if cloner_modifier:
                    self._force_geometry_nodes_update(context, cloner_modifier)
                else:
                    self._force_basic_ui_update(context)
            
        except Exception as e:
            print(f"[DEBUG] UI update error (non-critical): {e}")
    
    def _force_geometry_nodes_update(self, context, modifier):
        """ТОЧНАЯ КОПИЯ force_geometry_nodes_update из ObjectCloner_Standalone"""
        try:
            # 1. Обновляем объект
            if context.object:
                context.object.update_tag()
            
            # 2. Принудительно обновляем модификатор
            if modifier and modifier.node_group:
                # Временно отключаем и включаем модификатор для принудительного обновления
                show_viewport = modifier.show_viewport
                modifier.show_viewport = False
                context.view_layer.update()
                modifier.show_viewport = show_viewport
            
            # 3. Обновляем view layer
            context.view_layer.update()
            
            # 4. Обновляем все области
            for area in context.screen.areas:
                area.tag_redraw()
                
            # 5. Дополнительное обновление сцены
            bpy.context.scene.frame_set(bpy.context.scene.frame_current)
            
        except Exception as e:
            print(f"[ERROR] Ошибка при обновлении Geometry Nodes: {e}")
            # Fallback к обычному обновлению
            self._force_basic_ui_update(context)
    
    def _force_basic_ui_update(self, context):
        """Базовое обновление UI"""
        try:
            # Обновляем view layer
            context.view_layer.update()
            
            # Обновляем все 3D viewport области
            for area in context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
        except:
            pass


def register():
    """Регистрация операторов UI клонеров"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_cloner_expanded)
    bpy.utils.register_class(CLONERPRO_OT_select_cloner)
    bpy.utils.register_class(CLONERPRO_OT_toggle_component_visibility)
    bpy.utils.register_class(CLONERPRO_OT_delete_component)
    bpy.utils.register_class(CLONERPRO_OT_refresh_browser)
    bpy.utils.register_class(CLONERPRO_OT_expand_all_groups)
    bpy.utils.register_class(CLONERPRO_OT_collapse_all_groups)
    bpy.utils.register_class(CLONERPRO_OT_set_socket_value)


def unregister():
    """Отмена регистрации операторов UI клонеров"""
    bpy.utils.unregister_class(CLONERPRO_OT_set_socket_value)
    bpy.utils.unregister_class(CLONERPRO_OT_collapse_all_groups)
    bpy.utils.unregister_class(CLONERPRO_OT_expand_all_groups)
    bpy.utils.unregister_class(CLONERPRO_OT_refresh_browser)
    bpy.utils.unregister_class(CLONERPRO_OT_delete_component)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_component_visibility)
    bpy.utils.unregister_class(CLONERPRO_OT_select_cloner)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_cloner_expanded)