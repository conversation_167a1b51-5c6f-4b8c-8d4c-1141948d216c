"""
Cache Manager для ClonerPro
Управляет кэшем эффекторов для предотвращения крашей в rna_NodeTree_refine
"""

import bpy
import time
from typing import Dict, List, Set, Optional, Tuple, Any


class EffectorCacheManager:
    """
    Менеджер кэша эффекторов для безопасной работы с NodeTree.
    
    КРИТИЧНО для предотвращения краша в rna_NodeTree_refine при повторном создании эффекторов.
    """
    
    _cache: Dict[str, Dict] = {}
    _linked_cloners_cache: Dict[str, List[Tuple]] = {}
    _last_cleanup_time: float = 0.0
    _cleanup_interval: float = 30.0  # 30 секунд между очистками
    
    @classmethod
    def invalidate_cache(cls):
        """Полностью очищает кэш эффекторов."""
        print("[CACHE] Invalidating effector cache")
        cls._cache.clear()
        cls._linked_cloners_cache.clear()
        cls._last_cleanup_time = time.time()
    
    @classmethod
    def clear_effector_from_cache(cls, effector_name: str):
        """
        КРИТИЧЕСКАЯ ФУНКЦИЯ: Очищает конкретный эффектор из кэша.
        
        ОБЯЗАТЕЛЬНО вызывать при удалении эффектора для предотвращения крашей!
        """
        try:
            print(f"[CACHE] Очищаем эффектор {effector_name} из кэша")
            
            # Очищаем основной кэш
            if effector_name in cls._cache:
                del cls._cache[effector_name]
                print(f"[CACHE] ✅ {effector_name} удален из основного кэша")
            
            # Очищаем кэш связанных клонеров
            if effector_name in cls._linked_cloners_cache:
                del cls._linked_cloners_cache[effector_name]
                print(f"[CACHE] ✅ {effector_name} удален из кэша связанных клонеров")
            
            # Очищаем эффектор из кэша других эффекторов
            keys_to_clean = []
            for key, cache_data in cls._cache.items():
                if 'linked_effectors' in cache_data:
                    linked_effectors = cache_data['linked_effectors']
                    if effector_name in linked_effectors:
                        linked_effectors.remove(effector_name)
                        keys_to_clean.append(key)
            
            for key in keys_to_clean:
                print(f"[CACHE] ✅ Очищен {effector_name} из кэша {key}")
            
        except Exception as e:
            print(f"[CACHE] ❌ Ошибка очистки кэша для {effector_name}: {e}")
    
    @classmethod
    def get_linked_cloners(cls, effector_name: str) -> List[Tuple]:
        """
        Получает список связанных клонеров для эффектора из кэша.
        
        Returns:
            List[Tuple]: Список кортежей (object, modifier)
        """
        try:
            # Проверяем кэш
            if effector_name in cls._linked_cloners_cache:
                cached_data = cls._linked_cloners_cache[effector_name]
                
                # Проверяем актуальность кэша
                if cls._is_cache_valid(cached_data):
                    return cached_data
            
            # Обновляем кэш
            linked_cloners = cls._update_linked_cloners_cache(effector_name)
            return linked_cloners
            
        except Exception as e:
            print(f"[CACHE] Ошибка получения связанных клонеров для {effector_name}: {e}")
            return []
    
    @classmethod
    def _update_linked_cloners_cache(cls, effector_name: str) -> List[Tuple]:
        """Обновляет кэш связанных клонеров для эффектора."""
        try:
            linked_cloners = []
            
            for obj in bpy.data.objects:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    try:
                        if (mod.type == 'NODES' and mod.node_group and
                            hasattr(mod.node_group, 'get')):
                            
                            linked_effectors = mod.node_group.get("linked_effectors", [])
                            if effector_name in linked_effectors:
                                linked_cloners.append((obj, mod))
                                
                    except (ReferenceError, AttributeError):
                        # Модификатор поврежден, пропускаем
                        continue
            
            # Обновляем кэш
            cls._linked_cloners_cache[effector_name] = linked_cloners
            return linked_cloners
            
        except Exception as e:
            print(f"[CACHE] Ошибка обновления кэша связанных клонеров: {e}")
            return []
    
    @classmethod
    def _is_cache_valid(cls, cached_data: List[Tuple]) -> bool:
        """Проверяет актуальность кэша."""
        try:
            # Проверяем валидность всех объектов и модификаторов в кэше
            for obj, mod in cached_data:
                try:
                    # Пытаемся обратиться к объектам
                    _ = obj.name
                    _ = mod.name
                except ReferenceError:
                    # Один из объектов удален, кэш неактуален
                    return False
            
            return True
            
        except Exception:
            return False
    
    @classmethod
    def get_effector_object(cls, effector_mod) -> Optional[bpy.types.Object]:
        """Получает объект эффектора (для совместимости с advanced_cloners API)."""
        try:
            if not effector_mod:
                return None
            
            # Ищем объект, содержащий этот модификатор
            for obj in bpy.data.objects:
                if effector_mod in obj.modifiers.values():
                    return obj
            
            return None
            
        except Exception as e:
            print(f"[CACHE] Ошибка получения объекта эффектора: {e}")
            return None
    
    @classmethod
    def cleanup_invalid_entries(cls):
        """Очищает невалидные записи из кэша."""
        try:
            current_time = time.time()
            
            # Ограничиваем частоту очистки
            if current_time - cls._last_cleanup_time < cls._cleanup_interval:
                return
            
            cls._last_cleanup_time = current_time
            
            print("[CACHE] Очистка невалидных записей кэша")
            
            # Очищаем основной кэш
            invalid_keys = []
            for key, cache_data in cls._cache.items():
                try:
                    # Проверяем валидность данных
                    if not cls._is_cache_data_valid(cache_data):
                        invalid_keys.append(key)
                except Exception:
                    invalid_keys.append(key)
            
            for key in invalid_keys:
                del cls._cache[key]
                print(f"[CACHE] Удалена невалидная запись: {key}")
            
            # Очищаем кэш связанных клонеров
            invalid_effectors = []
            for effector_name, linked_cloners in cls._linked_cloners_cache.items():
                if not cls._is_cache_valid(linked_cloners):
                    invalid_effectors.append(effector_name)
            
            for effector_name in invalid_effectors:
                del cls._linked_cloners_cache[effector_name]
                print(f"[CACHE] Удален невалидный кэш связанных клонеров: {effector_name}")
            
        except Exception as e:
            print(f"[CACHE] Ошибка очистки невалидных записей: {e}")
    
    @classmethod
    def _is_cache_data_valid(cls, cache_data: Dict) -> bool:
        """Проверяет валидность данных кэша."""
        try:
            # Базовая проверка структуры
            if not isinstance(cache_data, dict):
                return False
            
            # Дополнительные проверки можно добавить здесь
            return True
            
        except Exception:
            return False
    
    @classmethod
    def _rebuild_cache(cls):
        """Перестраивает весь кэш с нуля."""
        try:
            print("[CACHE] Перестройка кэша эффекторов")
            
            cls.invalidate_cache()
            
            # Сканируем все эффекторы в сцене
            effector_names = set()
            
            for obj in bpy.data.objects:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    try:
                        if (mod.type == 'NODES' and mod.node_group):
                            # Проверяем, является ли это эффектором
                            component_type = mod.node_group.get("component_type")
                            if component_type == 'EFFECTOR':
                                effector_names.add(mod.name)
                            elif any(keyword in mod.node_group.name.lower() 
                                   for keyword in ['effector', 'random', 'noise']):
                                effector_names.add(mod.name)
                    except (ReferenceError, AttributeError):
                        continue
            
            # Перестраиваем кэш для каждого эффектора
            for effector_name in effector_names:
                cls._update_linked_cloners_cache(effector_name)
            
            print(f"[CACHE] ✅ Кэш перестроен для {len(effector_names)} эффекторов")
            
        except Exception as e:
            print(f"[CACHE] ❌ Ошибка перестройки кэша: {e}")


class ModuleCache:
    """
    Кэш для модулей и импортов (портировано из advanced_cloners).
    Ускоряет повторные импорты часто используемых модулей.
    """
    
    _cache: Dict[str, any] = {}
    
    @classmethod
    def get_cached_function(cls, module_path: str, function_name: str):
        """Получает закэшированную функцию из модуля."""
        cache_key = f"{module_path}.{function_name}"
        
        if cache_key in cls._cache:
            return cls._cache[cache_key]
        
        try:
            # Пытаемся импортировать функцию
            if module_path.startswith('managers.'):
                # Локальный импорт из ClonerPro
                if module_path == 'managers.effector_creation':
                    from ..managers import effector_creation
                    func = getattr(effector_creation, function_name, None)
                elif module_path == 'managers.object_creation':
                    from ..managers import object_creation
                    func = getattr(object_creation, function_name, None)
                else:
                    func = None
            else:
                func = None
            
            if func:
                cls._cache[cache_key] = func
                return func
                
        except ImportError as e:
            print(f"[MODULE CACHE] Не удалось импортировать {cache_key}: {e}")
        
        return None
    
    @classmethod
    def clear_cache(cls):
        """Очищает кэш модулей."""
        cls._cache.clear()
        print("[MODULE CACHE] Кэш модулей очищен")


class ComponentRegistryCache:
    """
    Кэш реестра компонентов (упрощенная версия из advanced_cloners).
    Отслеживает все активные клонеры и эффекторы в сцене.
    """
    
    _cloner_registry: Set[str] = set()
    _effector_registry: Set[str] = set()
    _last_scan_time: float = 0.0
    _scan_interval: float = 5.0  # 5 секунд между сканированиями
    
    @classmethod
    def register_cloner(cls, object_name: str, modifier_name: str):
        """Регистрирует клонер в реестре."""
        registry_key = f"{object_name}:{modifier_name}"
        cls._cloner_registry.add(registry_key)
        print(f"[REGISTRY] Зарегистрирован клонер: {registry_key}")
    
    @classmethod
    def register_effector(cls, object_name: str, modifier_name: str):
        """Регистрирует эффектор в реестре."""
        registry_key = f"{object_name}:{modifier_name}"
        cls._effector_registry.add(registry_key)
        print(f"[REGISTRY] Зарегистрирован эффектор: {registry_key}")
    
    @classmethod
    def unregister_cloner(cls, object_name: str, modifier_name: str):
        """Убирает клонер из реестра."""
        registry_key = f"{object_name}:{modifier_name}"
        cls._cloner_registry.discard(registry_key)
        print(f"[REGISTRY] Отменена регистрация клонера: {registry_key}")
    
    @classmethod
    def unregister_effector(cls, object_name: str, modifier_name: str):
        """Убирает эффектор из реестра."""
        registry_key = f"{object_name}:{modifier_name}"
        cls._effector_registry.discard(registry_key)
        print(f"[REGISTRY] Отменена регистрация эффектора: {registry_key}")
    
    @classmethod
    def get_all_cloners(cls) -> List[Tuple[str, str]]:
        """Получает все зарегистрированные клонеры."""
        return [tuple(key.split(':', 1)) for key in cls._cloner_registry]
    
    @classmethod
    def get_all_effectors(cls) -> List[Tuple[str, str]]:
        """Получает все зарегистрированные эффекторы."""
        return [tuple(key.split(':', 1)) for key in cls._effector_registry]
    
    @classmethod
    def scan_scene_components(cls, force_scan: bool = False):
        """Сканирует сцену и обновляет реестр компонентов."""
        current_time = time.time()
        
        if not force_scan and (current_time - cls._last_scan_time) < cls._scan_interval:
            return
        
        cls._last_scan_time = current_time
        
        print("[REGISTRY] Сканирование компонентов сцены")
        
        # Очищаем реестры
        cls._cloner_registry.clear()
        cls._effector_registry.clear()
        
        # Сканируем объекты
        for obj in bpy.data.objects:
            try:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    try:
                        if mod.type == 'NODES' and mod.node_group:
                            component_type = mod.node_group.get("component_type")
                            
                            if component_type == 'CLONER':
                                cls.register_cloner(obj.name, mod.name)
                            elif component_type == 'EFFECTOR':
                                cls.register_effector(obj.name, mod.name)
                            else:
                                # Fallback проверка по именам
                                node_group_name = mod.node_group.name.lower()
                                if any(keyword in node_group_name for keyword in ['cloner', 'grid', 'linear']):
                                    cls.register_cloner(obj.name, mod.name)
                                elif any(keyword in node_group_name for keyword in ['effector', 'random', 'noise']):
                                    cls.register_effector(obj.name, mod.name)
                    
                    except (ReferenceError, AttributeError):
                        continue
            
            except (ReferenceError, AttributeError):
                continue
        
        print(f"[REGISTRY] Найдено клонеров: {len(cls._cloner_registry)}, эффекторов: {len(cls._effector_registry)}")
    
    @classmethod
    def clear_registry(cls):
        """Очищает реестр компонентов."""
        cls._cloner_registry.clear()
        cls._effector_registry.clear()
        print("[REGISTRY] Реестр компонентов очищен")


def clear_all_caches():
    """Очищает все кэши системы."""
    try:
        print("[CACHE] Очистка всех кэшей системы")
        
        # Очищаем кэш эффекторов
        EffectorCacheManager.invalidate_cache()
        
        # Очищаем кэш модулей
        ModuleCache.clear_cache()
        
        # Очищаем реестр компонентов
        ComponentRegistryCache.clear_registry()
        
        # Дополнительная очистка системных кэшей Blender
        try:
            bpy.context.view_layer.update()
        except:
            pass
        
        print("[CACHE] ✅ Все кэши очищены")
        
    except Exception as e:
        print(f"[CACHE] ❌ Ошибка очистки кэшей: {e}")


def get_cache_statistics() -> Dict[str, Any]:
    """Получает статистику всех кэшей системы."""
    return {
        'effector_cache_size': len(EffectorCacheManager._cache),
        'linked_cloners_cache_size': len(EffectorCacheManager._linked_cloners_cache),
        'module_cache_size': len(ModuleCache._cache),
        'registered_cloners': len(ComponentRegistryCache._cloner_registry),
        'registered_effectors': len(ComponentRegistryCache._effector_registry),
        'last_cleanup_time': EffectorCacheManager._last_cleanup_time,
        'last_scan_time': ComponentRegistryCache._last_scan_time
    }


def force_rebuild_all_caches():
    """Принудительно перестраивает все кэши."""
    print("[CACHE] Принудительная перестройка всех кэшей")
    
    # Очищаем все кэши
    clear_all_caches()
    
    # Перестраиваем кэш эффекторов
    EffectorCacheManager._rebuild_cache()
    
    # Сканируем компоненты
    ComponentRegistryCache.scan_scene_components(force_scan=True)
    
    print("[CACHE] ✅ Все кэши перестроены")


# ======================================================================
# ПУБЛИЧНЫЙ API
# ======================================================================

__all__ = [
    'EffectorCacheManager',
    'ModuleCache',
    'ComponentRegistryCache',
    'clear_all_caches',
    'get_cache_statistics',
    'force_rebuild_all_caches'
]