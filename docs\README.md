# Документация ClonerPro

**Профессиональные инструменты клонирования для Blender с унифицированной архитектурой**

## Обзор

ClonerPro — это мощный аддон клонирования для Blender, построенный на базе единой классовой архитектуры с интеллектуальным разделением на два типа клонеров:

- **Unified клонеры** (Grid, Linear, Circle) - Геометрические клонеры с математическими распределениями
- **Mesh клонеры** (Object, Spline) - Клонеры, работающие с поверхностями и кривыми

## Навигация по документации

### 📋 Начало работы
- [Установка и настройка](03-setup-installation.md) - Установка и первые шаги
- [Основные концепции](05-core-concepts.md) - Понимание двухсистемной архитектуры
- [Краткий обзор](01-executive-summary.md) - Обзор проекта и целей

### 🏗️ Архитектура и разработка
- [Архитектура проекта](02-project-architecture.md) - Полный обзор архитектуры
- [Организация кода](04-code-organization.md) - Структура модулей и ответственности
- [Рабочий процесс разработки](06-development-workflow.md) - Как вносить вклад и расширять

### 🤖 Интеграция с ИИ-ассистентами
- **[Руководство для ИИ-разработчиков](00-ai-developer-guide.md)** - **ОБЯЗАТЕЛЬНОЕ ЧТЕНИЕ** для ИИ-ассистентов, работающих с ClonerPro

### 📖 Справочник и примеры
- [Справочник API](07-api-reference.md) - Полная документация API
- [Общие задачи](08-common-tasks.md) - Практические примеры и паттерны
- [Руководство по системам клонеров](09-cloner-systems-guide.md) - Глубокое погружение в обе системы клонеров

## Ключевые особенности

### Единая классовая архитектура
ClonerPro построен на унифицированной системе создания клонеров с автоматическим выбором подходящего метода:

**Unified клонеры (геометрические)**
- Grid, Linear, Circle клонеры
- Математические алгоритмы распределения
- Три режима создания: Object, Stacked, Collection
- Единая базовая архитектура BaseCloner

**Mesh клонеры (поверхностные)**  
- Object, Spline клонеры
- Распределение по вертексам, рёбрам, фейсам, кривым
- Поддержка mesh_stacked режима для прямого создания
- Интеграция с unified системой через единый API

### Продвинутое управление цепочками
- **UUID система** - Уникальная идентификация всех клонеров
- **Chain Detection** - Автоматическое обнаружение и связывание цепочек клонеров
- **Smart Recovery** - Восстановление связей после перезагрузки файла
- **Chain Metadata** - Полное отслеживание отношений

### Три режима создания
Каждый тип клонера поддерживает несколько режимов создания:

1. **Object Mode** - Создаёт новые объекты клонеров в коллекциях CLONERS_
2. **Stacked Mode** - Добавляет модификатор клонера на исходный объект
3. **Collection Mode** - Клонирует целые коллекции объектов

### Система безопасности
- **Anti-recursion** - Защита от рекурсивных зависимостей
- **Dependency Safety** - Обработчики безопасных зависимостей
- **Event Handling** - Мониторинг и обработка событий
- **Error Recovery** - Восстановление после ошибок

## Текущее состояние реализации

### ✅ Полностью реализованы
- **Grid Cloner** - 2D/3D сеточное распределение с автоцентрированием
- **Linear Cloner** - Линейное распределение с градиентными трансформациями
- **Circle Cloner** - Радиальное распределение по окружности
- **Object Cloner** - Клонирование по вертексам/рёбрам/фейсам объектов
- **Spline Cloner** - Клонирование вдоль кривых и сплайнов
- **Anti-Recursion система** - Полная защита от рекурсивных зависимостей
- **UUID система** - Система уникальных идентификаторов
- **Chain detection** - Обнаружение и управление цепочками клонеров
- **Браузер клонеров** - Навигация и управление клонерами

### 🔄 В разработке
- **Noise Effector** - Реализован, требует интеграции в UI
- **Random Effector** - Расширенная функциональность
- **Volume, Curves клонеры** - Планируется расширение mesh системы

### ❌ Планируется
- **Effector система** - Plain, Step, Delay, Target эффекторы
- **Fields система** - Сферические, коробочные и другие поля

## Быстрый старт

```python
# Единый API для всех типов клонеров
from core.templates.cloner_creation import create_cloner_unified

# Создание Grid клонера в Object режиме
grid_cloner = create_cloner_unified(
    cloner_type="GRID",
    mode="OBJECT", 
    source=source_object
)

# Создание Object клонера в Stacked режиме (mesh-based)
object_cloner = create_cloner_unified(
    cloner_type="OBJECT",
    mode="STACKED",
    source=target_object
)

# Создание Spline клонера с mesh stacking
spline_cloner = create_cloner_unified(
    cloner_type="SPLINE",
    mode="STACKED",
    source=curve_object,
    mesh_stacked=True
)
```

## Поддержка и сообщество

Для получения помощи и обсуждений:
- Изучите [Руководство для ИИ-разработчиков](00-ai-developer-guide.md) для правильного взаимодействия с архитектурой
- Просмотрите [Общие задачи](08-common-tasks.md) для практических примеров
- Обратитесь к [API Reference](07-api-reference.md) для детальной технической информации

## Структура документации

1. **[00-ai-developer-guide.md](00-ai-developer-guide.md)** - Руководство для ИИ-ассистентов (КРИТИЧЕСКИ ВАЖНО)
2. **[01-executive-summary.md](01-executive-summary.md)** - Краткий обзор проекта
3. **[02-project-architecture.md](02-project-architecture.md)** - Архитектура проекта  
4. **[03-setup-installation.md](03-setup-installation.md)** - Установка и настройка
5. **[04-code-organization.md](04-code-organization.md)** - Организация кода
6. **[05-core-concepts.md](05-core-concepts.md)** - Основные концепции
7. **[06-development-workflow.md](06-development-workflow.md)** - Рабочий процесс разработки
8. **[07-api-reference.md](07-api-reference.md)** - Справочник API
9. **[08-common-tasks.md](08-common-tasks.md)** - Примеры общих задач
10. **[09-cloner-systems-guide.md](09-cloner-systems-guide.md)** - Руководство по системам клонеров

---

*Эта документация поддерживается и обновляется командой ClonerPro. Последнее обновление: Декабрь 2024*