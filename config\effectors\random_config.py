"""
Random Effector Parameters для ClonerPro
Определения параметров для автогенерации UI
"""

# Определения групп параметров для Random Effector
RANDOM_EFFECTOR_PARAMETERS = {
    "Effector Settings": [
        {
            "name": "Enable",
            "socket_name": "Enable", 
            "type": "BOOL",
            "default": True,
            "description": "Enable/disable the effector"
        },
        {
            "name": "Strength", 
            "socket_name": "Strength",
            "type": "FLOAT", 
            "default": 1.0,
            "min": 0.0,
            "max": 2.0,
            "description": "Overall strength of the effect"
        }
    ],
    
    "Transform Parameters": [
        {
            "name": "Position",
            "socket_name": "Position",
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Random position offset range"
        },
        {
            "name": "Rotation",
            "socket_name": "Rotation", 
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Random rotation range"
        },
        {
            "name": "Scale",
            "socket_name": "Scale",
            "type": "VECTOR",
            "default": (0.0, 0.0, 0.0),
            "description": "Random scale variation range"
        }
    ],
    
    "Random Settings": [
        {
            "name": "Uniform Scale",
            "socket_name": "Uniform Scale",
            "type": "BOOL",
            "default": True,
            "description": "Use the same random value for all scale axes"
        },
        {
            "name": "Seed",
            "socket_name": "Seed",
            "type": "INT",
            "default": 0,
            "min": 0,
            "max": 10000,
            "description": "Seed for random number generation"
        }
    ]
}


def get_random_effector_parameters():
    """Получить параметры Random Effector"""
    return RANDOM_EFFECTOR_PARAMETERS


