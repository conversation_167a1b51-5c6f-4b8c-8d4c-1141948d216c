"""
Anti-recursion utility for all cloners
Provides unified anti-recursion architecture from advanced_cloners
"""

import bpy


def add_anti_recursion_socket(node_group):
    """
    Add the 'Realize Instances' parameter socket to any node group
    
    Args:
        node_group: The node group to add the socket to
    
    Returns:
        The created socket
    """
    realize_instances_input = node_group.interface.new_socket(
        name="Realize Instances", 
        in_out='INPUT', 
        socket_type='NodeSocketBool'
    )
    realize_instances_input.default_value = True
    return realize_instances_input


def apply_anti_recursion_architecture(nodes, links, group_input, group_output, final_geometry_output, use_anti_recursion=True, location_offset=(900, 0)):
    """
    Apply the complete anti-recursion architecture from advanced_cloners
    
    Args:
        nodes: Node group nodes
        links: Node group links
        group_input: Group input node
        group_output: Group output node
        final_geometry_output: The final geometry output from cloner logic
        use_anti_recursion: Whether to apply anti-recursion
        location_offset: Base location for anti-recursion nodes
    
    Returns:
        None (connects directly to group_output)
    """
    if use_anti_recursion:
        x_offset, y_offset = location_offset
        
        # Join Geometry node to combine all cloner outputs
        join_geometry = nodes.new('GeometryNodeJoinGeometry')
        join_geometry.name = "Join Geometry"
        join_geometry.location = (x_offset, y_offset)
        links.new(final_geometry_output, join_geometry.inputs['Geometry'])
        
        # Realize Instances node for anti-recursion
        realize_instances = nodes.new('GeometryNodeRealizeInstances')
        realize_instances.name = "Realize Instances (Anti-Recursion)"
        realize_instances.location = (x_offset + 100, y_offset)
        links.new(join_geometry.outputs['Geometry'], realize_instances.inputs['Geometry'])
        
        # Switch node controlled by "Realize Instances" parameter
        switch_node = nodes.new('GeometryNodeSwitch')
        switch_node.name = "Anti-Recursion Switch"
        switch_node.input_type = 'GEOMETRY'
        switch_node.location = (x_offset + 200, y_offset)
        
        # Connect the Realize Instances parameter to control the switch
        links.new(group_input.outputs['Realize Instances'], switch_node.inputs['Switch'])
        
        # False = no realization (keep instances), True = realize instances
        links.new(join_geometry.outputs['Geometry'], switch_node.inputs[False])
        links.new(realize_instances.outputs['Geometry'], switch_node.inputs[True])
        
        # Final output through switch
        links.new(switch_node.outputs['Output'], group_output.inputs['Geometry'])
        
        # Removed debug log
    else:
        # Direct output without anti-recursion
        links.new(final_geometry_output, group_output.inputs['Geometry'])
        # Removed debug log


def create_anti_recursion_wrapper(base_cloner_function, cloner_name, *args, **kwargs):
    """
    Wrapper function that automatically adds anti-recursion to any cloner
    
    Args:
        base_cloner_function: The base cloner creation function
        cloner_name: Name for debugging
        *args, **kwargs: Arguments to pass to the base function
    
    Returns:
        Node group with anti-recursion applied
    """
    try:
        # Extract use_anti_recursion from kwargs
        use_anti_recursion = kwargs.get('use_anti_recursion', True)
        
        # Removed debug log
        
        # Call the base cloner function
        node_group = base_cloner_function(*args, **kwargs)
        
        if not node_group:
            print(f"[ERROR] Base cloner function failed for {cloner_name}")
            return None
        
        # Add anti-recursion socket if not already present
        has_realize_socket = False
        for item in node_group.interface.items_tree:
            if item.name == "Realize Instances":
                has_realize_socket = True
                break
        
        if not has_realize_socket:
            add_anti_recursion_socket(node_group)
            # Removed debug log
        
        # Removed debug log
        return node_group
        
    except Exception as e:
        print(f"[ERROR] Anti-recursion wrapper failed for {cloner_name}: {e}")
        import traceback
        traceback.print_exc()
        return None


def add_common_transform_sockets(node_group):
    """Add common transform sockets (Instance Scale, Instance Rotation)"""
    scale_input = node_group.interface.new_socket(name="Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
    scale_input.default_value = (1.0, 1.0, 1.0)

    rotation_input = node_group.interface.new_socket(name="Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_input.default_value = (0.0, 0.0, 0.0)
    
    return scale_input, rotation_input


def add_common_random_sockets(node_group):
    """Add common randomization sockets"""
    random_position_input = node_group.interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_position_input.default_value = (0.0, 0.0, 0.0)

    random_rotation_input = node_group.interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rotation_input.default_value = (0.0, 0.0, 0.0)

    random_scale_input = node_group.interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale_input.default_value = 0.0
    random_scale_input.min_value = 0.0
    random_scale_input.max_value = 1.0

    seed_input = node_group.interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0
    seed_input.max_value = 10000
    
    return random_position_input, random_rotation_input, random_scale_input, seed_input


def add_common_global_sockets(node_group):
    """Add common global transform sockets"""
    global_pos = node_group.interface.new_socket(name="Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_pos.default_value = (0.0, 0.0, 0.0)
    
    global_rot = node_group.interface.new_socket(name="Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    global_rot.default_value = (0.0, 0.0, 0.0)
    
    return global_pos, global_rot


def create_basic_cloner_interface(node_group, cloner_type="Generic"):
    """
    Create basic cloner interface with all common sockets
    
    Args:
        node_group: The node group to add interface to
        cloner_type: Type of cloner for naming
    
    Returns:
        Dictionary with all created sockets for easy reference
    """
    sockets = {}
    
    # Input geometry (for stacked) or Collection (for collection mode)
    if "Stacked" in node_group.name:
        sockets['geometry_input'] = node_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    elif "Collection" in node_group.name:
        sockets['collection_input'] = node_group.interface.new_socket(name="Collection", in_out='INPUT', socket_type='NodeSocketCollection')
    else:
        # Object mode - uses Instance Source
        sockets['instance_source'] = node_group.interface.new_socket(name="Instance Source", in_out='INPUT', socket_type='NodeSocketGeometry')
    
    # Output
    sockets['geometry_output'] = node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
    
    # Anti-recursion
    sockets['realize_instances'] = add_anti_recursion_socket(node_group)
    
    # Common transforms - НЕ добавляем для LINEAR клонера (у него есть Start/End параметры)
    if cloner_type != "LINEAR":
        sockets['instance_scale'], sockets['instance_rotation'] = add_common_transform_sockets(node_group)
    
    # Common randomization
    sockets['random_position'], sockets['random_rotation'], sockets['random_scale'], sockets['random_seed'] = add_common_random_sockets(node_group)
    
    # Global transforms для всех режимов (Object, Collection, Stacked)
    sockets['global_position'], sockets['global_rotation'] = add_common_global_sockets(node_group)
    
    # Removed debug log
    return sockets