"""
Chain Source Discovery - Обнаружение источников цепей
"""

import bpy
from .utils import chain_debug_print


def find_original_chain_source_via_uuid(chain_info, next_cloner_uuid):
    """
    Найти оригинальный источник цепи через UUID связи
    ИСПРАВЛЕНО: Убрана зависимость от hardcoded _ClonerTo имен
    """
    try:
        from ..uuid.manager import BlenderClonerUUIDManager
        
        chain_uuid = chain_info.get("chain_uuid", "")
        if not chain_uuid:
            print(f"🔍 [CHAIN] No chain_uuid found, using fallback methods")
            return None
        
        print(f"🔍 [CHAIN] Tracing chain {chain_uuid} to find original source...")
        
        # Найти все клонеры в этой цепи
        chain_cloners = BlenderClonerUUIDManager.find_cloners_in_chain_by_uuid(chain_uuid)
        if not chain_cloners:
            print(f"🔍 [CHAIN] No cloners found in chain {chain_uuid}")
            return None
        
        # Найти первый клонер в цепи (chain_sequence = 0)
        first_cloner_obj = None
        first_cloner_modifier = None
        
        for cloner_obj, cloner_modifier in chain_cloners:
            sequence = cloner_modifier.get("chain_sequence", -1)
            print(f"🔍 [CHAIN] Checking cloner {cloner_obj.name}, sequence: {sequence}")
            
            if sequence == 0:
                first_cloner_obj = cloner_obj
                first_cloner_modifier = cloner_modifier
                break
        
        if not first_cloner_obj:
            print(f"🔍 [CHAIN] No first cloner (sequence=0) found in chain")
            return None
        
        print(f"🔍 [CHAIN] Found first cloner in chain: {first_cloner_obj.name}")
        
        # Получить оригинальный источник через chain metadata
        from ...core.core import get_original_chain_source
        original_source = get_original_chain_source(first_cloner_obj)
        
        if original_source:
            print(f"🔍 [CHAIN] Found original chain source: {original_source.name}")
            return original_source
        
        # Fallback: получить текущий источник первого клонера
        current_source = get_current_cloner_source(first_cloner_obj, first_cloner_modifier)
        if current_source:
            print(f"🔍 [CHAIN] Using current source from first cloner: {current_source.name}")
            return current_source
        
        print(f"🔍 [CHAIN] Could not determine original source from first cloner")
        return None
        
    except ImportError:
        print("⚠️ [CHAIN] UUID system not available for chain source detection")
        return None
    except Exception as e:
        print(f"❌ [CHAIN] Error finding original chain source: {e}")
        return None


def get_current_cloner_source(cloner_obj, cloner_modifier):
    """
    Получить текущий источник клонера (объект или коллекцию)
    ИСПРАВЛЕНО: Теперь правильно обрабатывает все типы клонеров
    """
    try:
        print(f"🔍 [CHAIN] Getting source for {cloner_obj.name} with modifier {cloner_modifier.name}")
        
        if not cloner_modifier.node_group:
            print(f"🔍 [CHAIN] No node_group found for {cloner_modifier.name}")
            return None
        
        print(f"🔍 [CHAIN] Node group: {cloner_modifier.node_group.name}")
        
        # ОТЛАДКА: Проверяем доступные объекты в сцене
        available_objects = [obj.name for obj in bpy.data.objects if "Clone" in obj.name or "Cube" in obj.name]
        print(f"🔍 [CHAIN] Available objects with 'Clone'/'Cube': {available_objects}")
        
        # ИСПРАВЛЕНО: Правильные приоритеты - wrapper сокеты имеют приоритет
        object_socket_names = [
            'Object',            # Wrapper уровень (приоритет)
            'Source Object',      # Object cloners  
            'Instance Source',    # Logic группа уровень
            'Geometry'           # Stacked mode input
        ]
        
        collection_socket_names = [
            'Collection',         # Wrapper уровень (приоритет)
            'Source Collection'   # Object cloner collection mode
        ]
        
        # Поиск источников в порядке приоритета
        for socket in cloner_modifier.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                print(f"🔍 [CHAIN] Checking socket: {socket.name} (id: {socket.identifier})")
                
                # Проверяем объектные источники
                if socket.name in object_socket_names:
                    source_obj = cloner_modifier.get(socket.identifier)
                    print(f"🔍 [CHAIN] Object socket '{socket.name}' value: {source_obj.name if source_obj else 'None'}")
                    if source_obj:
                        return source_obj
                
                # Проверяем коллекции
                elif socket.name in collection_socket_names:
                    source_coll = cloner_modifier.get(socket.identifier)
                    print(f"🔍 [CHAIN] Collection socket '{socket.name}' value: {source_coll.name if source_coll else 'None'}")
                    if source_coll:
                        return source_coll
        
        print(f"🔍 [CHAIN] No valid source found in sockets")
        return None
    except (AttributeError, ReferenceError) as e:
        print(f"🔍 [CHAIN] Error getting source: {e}")
        return None


def get_source_uuid_from_cloner(cloner_modifier):
    """Получить UUID источника из метаданных клонера"""
    if not cloner_modifier:
        return None
    
    # Проверяем, есть ли сохраненный UUID источника
    from ...core.core import get_cloner_info
    cloner_info = get_cloner_info(cloner_modifier)
    source_uuid = cloner_info.get("chain_source_uuid", "")
    
    if source_uuid:
        print(f"🔍 [CHAIN] Found source UUID in cloner metadata: {source_uuid}")
        return source_uuid
    
    # Если нет сохраненного UUID, пытаемся получить UUID от текущего источника
    current_source = get_current_cloner_source(
        cloner_modifier.id_data, cloner_modifier
    )
    
    if current_source and hasattr(current_source, 'get'):
        source_uuid = current_source.get("source_uuid")
        if source_uuid:
            print(f"🔍 [CHAIN] Found source UUID from current source: {source_uuid}")
            return source_uuid
    
    print(f"🔍 [CHAIN] No source UUID found")
    return None


def find_source_by_uuid(source_uuid):
    """Найти источник по UUID (независимо от имени)"""
    if not source_uuid:
        return None
    
    print(f"🔍 [CHAIN] Searching for source with UUID: {source_uuid}")
    
    # Ищем среди объектов
    for obj in bpy.data.objects:
        obj_uuid = obj.get("source_uuid")
        if obj_uuid == source_uuid:
            print(f"✅ [CHAIN] Found source object by UUID: {obj.name}")
            return obj
    
    # Ищем среди коллекций
    for coll in bpy.data.collections:
        coll_uuid = coll.get("source_uuid")
        if coll_uuid == source_uuid:
            print(f"✅ [CHAIN] Found source collection by UUID: {coll.name}")
            return coll
    
    print(f"❌ [CHAIN] Source with UUID {source_uuid} not found")
    return None


def get_original_source_from_info(chain_info):
    """Получить ПРАВИЛЬНЫЙ источник из информации о цепи с резервными вариантами"""
    
    # ПОПЫТКА 0: Приоритет - поиск по UUID (независимо от имени!)
    source_uuid = chain_info.get("chain_source_uuid", "")
    print(f"🔍 [CHAIN] chain_info contains: {list(chain_info.keys())}")
    print(f"🔍 [CHAIN] Raw chain_source_uuid from chain_info: '{source_uuid}'")
    
    if source_uuid:
        print(f"🔍 [CHAIN] Trying to find source by UUID: {source_uuid}")
        
        # Дополнительная отладка: проверяем все объекты с source_uuid
        objects_with_uuid = []
        for obj in bpy.data.objects:
            obj_uuid = obj.get("source_uuid")
            if obj_uuid:
                objects_with_uuid.append(f"{obj.name}: {obj_uuid}")
        print(f"🔍 [CHAIN] Objects with source_uuid: {objects_with_uuid}")
        
        source_by_uuid = find_source_by_uuid(source_uuid)
        if source_by_uuid:
            print(f"✅ [CHAIN] Found source by UUID: {source_by_uuid.name}")
            return source_by_uuid
    
    # ПОПЫТКА 1: Использовать current_source из chain_info (новое)
    current_source = chain_info.get("current_source")
    if current_source:
        print(f"🔍 [CHAIN] Found current_source in chain_info: {current_source.name}")
        return current_source
    
    # ПОПЫТКА 2: Поиск по имени объекта
    source_obj_name = chain_info.get("chain_source_object", "")
    if source_obj_name and source_obj_name in bpy.data.objects:
        source_obj = bpy.data.objects[source_obj_name]
        print(f"🔍 [CHAIN] Found source object by name: {source_obj.name}")
        return source_obj
    
    # ПОПЫТКА 3: Поиск по имени коллекции
    source_coll_name = chain_info.get("chain_source_collection", "")
    if source_coll_name and source_coll_name in bpy.data.collections:
        source_coll = bpy.data.collections[source_coll_name]
        print(f"🔍 [CHAIN] Found source collection by name: {source_coll.name}")
        return source_coll
    
    print(f"❌ [CHAIN] Could not find original source using any method")
    print(f"    Tried UUID: '{source_uuid}'")
    print(f"    Tried object: '{source_obj_name}'")
    print(f"    Tried collection: '{source_coll_name}'")
    
    return None


def get_deleted_cloner_source(chain_info):
    """Получить источник удаленного клонера из chain_info"""
    
    # Проверяем, есть ли direct ссылка на текущий источник
    current_source = chain_info.get("current_source")
    if current_source:
        print(f"🔍 [CHAIN] Found current source from chain_info: {current_source.name}")
        return current_source
    
    # Fallback к поиску оригинального источника
    original_source = get_original_source_from_info(chain_info)
    if original_source:
        print(f"🔍 [CHAIN] Using original source as deleted cloner source: {original_source.name}")
        return original_source
    
    print(f"❌ [CHAIN] Could not determine deleted cloner source")
    return None


def get_or_create_source_uuid(source_obj):
    """Получить или создать UUID для объекта-источника"""
    if not source_obj:
        return None
    
    # Проверяем, есть ли уже UUID
    existing_uuid = source_obj.get("source_uuid")
    if existing_uuid:
        print(f"🔍 [CHAIN] Found existing source UUID: {existing_uuid}")
        return existing_uuid
    
    # Создаем новый UUID
    import uuid
    new_uuid = str(uuid.uuid4())
    source_obj["source_uuid"] = new_uuid
    
    print(f"🔍 [CHAIN] Created new source UUID: {new_uuid} for {source_obj.name}")
    return new_uuid


def find_parallel_chains_from_same_source(deleted_cloner_name, chain_info):
    """
    Найти параллельные цепи от того же источника
    ОБНОВЛЕНО: Работает через UUID систему, независимо от имен
    """
    parallel_chains = []
    
    try:
        # Метод 1: UUID система - поиск через chain_source_uuid
        source_uuid = chain_info.get("chain_source_uuid", "")
        if source_uuid:
            print(f"🔍 [CHAIN] Searching for parallel chains with source UUID: {source_uuid}")
            
            # Ищем все клонеры с таким же chain_source_uuid
            for obj in bpy.data.objects:
                for modifier in obj.modifiers:
                    if (modifier.type == 'NODES' and 
                        hasattr(modifier, 'node_group') and
                        modifier.node_group):
                        
                        # Проверяем, это ли клонер
                        from ...core.core import get_cloner_info
                        cloner_info = get_cloner_info(modifier)
                        if not cloner_info:
                            continue
                        
                        # Проверяем UUID источника
                        cloner_source_uuid = cloner_info.get("chain_source_uuid", "")
                        if cloner_source_uuid == source_uuid and obj.name != deleted_cloner_name:
                            parallel_chains.append({
                                "cloner_obj": obj,
                                "cloner_modifier": modifier,
                                "cloner_info": cloner_info
                            })
                            print(f"✓ [CHAIN] Found parallel chain cloner: {obj.name}")
        
        # Метод 2: Fallback - поиск по имени источника (legacy)
        else:
            source_obj_name = chain_info.get("chain_source_object", "")
            source_coll_name = chain_info.get("chain_source_collection", "")
            
            if source_obj_name or source_coll_name:
                print(f"🔍 [CHAIN] Fallback: searching by source names obj='{source_obj_name}' coll='{source_coll_name}'")
                
                for obj in bpy.data.objects:
                    for modifier in obj.modifiers:
                        if (modifier.type == 'NODES' and 
                            hasattr(modifier, 'node_group') and
                            modifier.node_group):
                            
                            from ...core.core import get_cloner_info
                            cloner_info = get_cloner_info(modifier)
                            if not cloner_info:
                                continue
                            
                            # Проверяем совпадение источников
                            matches_obj = (source_obj_name and 
                                         cloner_info.get("chain_source_object", "") == source_obj_name)
                            matches_coll = (source_coll_name and 
                                          cloner_info.get("chain_source_collection", "") == source_coll_name)
                            
                            if (matches_obj or matches_coll) and obj.name != deleted_cloner_name:
                                parallel_chains.append({
                                    "cloner_obj": obj,
                                    "cloner_modifier": modifier,
                                    "cloner_info": cloner_info
                                })
                                print(f"✓ [CHAIN] Found parallel chain cloner (legacy): {obj.name}")
        
        print(f"🔍 [CHAIN] Found {len(parallel_chains)} parallel chains from same source")
        return parallel_chains
        
    except Exception as e:
        print(f"❌ [CHAIN] Error finding parallel chains: {e}")
        return []